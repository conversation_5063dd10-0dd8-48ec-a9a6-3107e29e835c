
<!DOCTYPE html>

<html class="no-js" lang="en">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width,initial-scale=1" name="viewport"/>
<meta content="Comprehensive documentation for SizeWise Suite - A modular HVAC engineering and estimating platform" name="description"/>
<meta content="SizeWise Suite Team" name="author"/>
<link href="https://sizewise-suite.github.io/" rel="canonical"/>
<link href="getting-started/installation/" rel="next"/>
<link href="assets/images/favicon.png" rel="icon"/>
<meta content="mkdocs-1.6.1, mkdocs-material-9.6.15" name="generator"/>
<title>SizeWise Suite Documentation</title>
<link href="assets/stylesheets/main.342714a4.min.css" rel="stylesheet"/>
<link href="assets/stylesheets/palette.06af60db.min.css" rel="stylesheet"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&amp;display=fallback" rel="stylesheet"/>
<style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
<script>__md_scope=new URL(".",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
</head>
<body data-md-color-accent="blue" data-md-color-primary="blue" data-md-color-scheme="default" dir="ltr">
<input autocomplete="off" class="md-toggle" data-md-toggle="drawer" id="__drawer" type="checkbox"/>
<input autocomplete="off" class="md-toggle" data-md-toggle="search" id="__search" type="checkbox"/>
<label class="md-overlay" for="__drawer"></label>
<div data-md-component="skip">
<a class="md-skip" href="#sizewise-suite-documentation">
          Skip to content
        </a>
</div>
<div data-md-component="announce">
</div>
<div data-md-color-scheme="default" data-md-component="outdated" hidden="">
</div>
<header class="md-header" data-md-component="header">
<nav aria-label="Header" class="md-header__inner md-grid">
<a aria-label="SizeWise Suite Documentation" class="md-header__button md-logo" data-md-component="logo" href="." title="SizeWise Suite Documentation">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"></path></svg>
</a>
<label class="md-header__button md-icon" for="__drawer">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"></path></svg>
</label>
<div class="md-header__title" data-md-component="header-title">
<div class="md-header__ellipsis">
<div class="md-header__topic">
<span class="md-ellipsis">
            SizeWise Suite Documentation
          </span>
</div>
<div class="md-header__topic" data-md-component="header-topic">
<span class="md-ellipsis">
            
              Home
            
          </span>
</div>
</div>
</div>
<form class="md-header__option" data-md-component="palette">
<input aria-label="Switch to dark mode" class="md-option" data-md-color-accent="blue" data-md-color-media="" data-md-color-primary="blue" data-md-color-scheme="default" id="__palette_0" name="__palette" type="radio"/>
<label class="md-header__button md-icon" for="__palette_1" hidden="" title="Switch to dark mode">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"></path></svg>
</label>
<input aria-label="Switch to light mode" class="md-option" data-md-color-accent="blue" data-md-color-media="" data-md-color-primary="blue" data-md-color-scheme="slate" id="__palette_1" name="__palette" type="radio"/>
<label class="md-header__button md-icon" for="__palette_0" hidden="" title="Switch to light mode">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"></path></svg>
</label>
</form>
<script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
<label class="md-header__button md-icon" for="__search">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"></path></svg>
</label>
<div class="md-search" data-md-component="search" role="dialog">
<label class="md-search__overlay" for="__search"></label>
<div class="md-search__inner" role="search">
<form class="md-search__form" name="search">
<input aria-label="Search" autocapitalize="off" autocomplete="off" autocorrect="off" class="md-search__input" data-md-component="search-query" name="query" placeholder="Search" required="" spellcheck="false" type="text"/>
<label class="md-search__icon md-icon" for="__search">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"></path></svg>
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"></path></svg>
</label>
<nav aria-label="Search" class="md-search__options">
<a aria-label="Share" class="md-search__icon md-icon" data-clipboard="" data-clipboard-text="" data-md-component="search-share" href="javascript:void(0)" tabindex="-1" title="Share">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"></path></svg>
</a>
<button aria-label="Clear" class="md-search__icon md-icon" tabindex="-1" title="Clear" type="reset">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"></path></svg>
</button>
</nav>
</form>
<div class="md-search__output">
<div class="md-search__scrollwrap" data-md-scrollfix="" tabindex="0">
<div class="md-search-result" data-md-component="search-result">
<div class="md-search-result__meta">
            Initializing search
          </div>
<ol class="md-search-result__list" role="presentation"></ol>
</div>
</div>
</div>
</div>
</div>
<div class="md-header__source">
<a class="md-source" data-md-component="source" href="https://github.com/sizewise-suite/sizewise-suite" title="Go to repository">
<div class="md-source__icon md-icon">
<svg viewbox="0 0 496 512" xmlns="http://www.w3.org/2000/svg"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"></path></svg>
</div>
<div class="md-source__repository">
    sizewise-suite/sizewise-suite
  </div>
</a>
</div>
</nav>
</header>
<div class="md-container" data-md-component="container">
<nav aria-label="Tabs" class="md-tabs" data-md-component="tabs">
<div class="md-grid">
<ul class="md-tabs__list">
<li class="md-tabs__item md-tabs__item--active">
<a class="md-tabs__link" href=".">
        
  
  
    
  
  Home

      </a>
</li>
<li class="md-tabs__item">
<a class="md-tabs__link" href="getting-started/installation/">
          
  
  
  Getting Started

        </a>
</li>
<li class="md-tabs__item">
<a class="md-tabs__link" href="user-guide/overview.md">
          
  
  
  User Guide

        </a>
</li>
<li class="md-tabs__item">
<a class="md-tabs__link" href="api/overview.md">
          
  
  
  API Reference

        </a>
</li>
<li class="md-tabs__item">
<a class="md-tabs__link" href="developer/architecture.md">
          
  
  
  Developer Guide

        </a>
</li>
<li class="md-tabs__item">
<a class="md-tabs__link" href="examples/basic-calculations.md">
          
  
  
  Examples

        </a>
</li>
</ul>
</div>
</nav>
<main class="md-main" data-md-component="main">
<div class="md-main__inner md-grid">
<div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation">
<div class="md-sidebar__scrollwrap">
<div class="md-sidebar__inner">
<nav aria-label="Navigation" class="md-nav md-nav--primary md-nav--lifted" data-md-level="0">
<label class="md-nav__title" for="__drawer">
<a aria-label="SizeWise Suite Documentation" class="md-nav__button md-logo" data-md-component="logo" href="." title="SizeWise Suite Documentation">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"></path></svg>
</a>
    SizeWise Suite Documentation
  </label>
<div class="md-nav__source">
<a class="md-source" data-md-component="source" href="https://github.com/sizewise-suite/sizewise-suite" title="Go to repository">
<div class="md-source__icon md-icon">
<svg viewbox="0 0 496 512" xmlns="http://www.w3.org/2000/svg"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"></path></svg>
</div>
<div class="md-source__repository">
    sizewise-suite/sizewise-suite
  </div>
</a>
</div>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item md-nav__item--active">
<input class="md-nav__toggle md-toggle" id="__toc" type="checkbox"/>
<label class="md-nav__link md-nav__link--active" for="__toc">
<span class="md-ellipsis">
    Home
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<a class="md-nav__link md-nav__link--active" href=".">
<span class="md-ellipsis">
    Home
    
  </span>
</a>
<nav aria-label="Table of contents" class="md-nav md-nav--secondary">
<label class="md-nav__title" for="__toc">
<span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
<ul class="md-nav__list" data-md-component="toc" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="#what-is-sizewise-suite">
<span class="md-ellipsis">
      What is SizeWise Suite?
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#core-modules">
<span class="md-ellipsis">
      Core Modules
    </span>
</a>
<nav aria-label="Core Modules" class="md-nav">
<ul class="md-nav__list">
<li class="md-nav__item">
<a class="md-nav__link" href="#air-duct-sizer">
<span class="md-ellipsis">
      🌬️ Air Duct Sizer
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#grease-duct-sizer-coming-soon">
<span class="md-ellipsis">
      🔥 Grease Duct Sizer (Coming Soon)
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#engine-exhaust-sizer-coming-soon">
<span class="md-ellipsis">
      🚗 Engine Exhaust Sizer (Coming Soon)
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#boiler-vent-sizer-coming-soon">
<span class="md-ellipsis">
      🔥 Boiler Vent Sizer (Coming Soon)
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#estimating-app-coming-soon">
<span class="md-ellipsis">
      💰 Estimating App (Coming Soon)
    </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#key-features">
<span class="md-ellipsis">
      Key Features
    </span>
</a>
<nav aria-label="Key Features" class="md-nav">
<ul class="md-nav__list">
<li class="md-nav__item">
<a class="md-nav__link" href="#standards-compliance">
<span class="md-ellipsis">
      ✅ Standards Compliance
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#unit-conversion">
<span class="md-ellipsis">
      🔄 Unit Conversion
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#offline-functionality">
<span class="md-ellipsis">
      💾 Offline Functionality
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#modern-interface">
<span class="md-ellipsis">
      📱 Modern Interface
    </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#quick-start">
<span class="md-ellipsis">
      Quick Start
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#architecture-overview">
<span class="md-ellipsis">
      Architecture Overview
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#technology-stack">
<span class="md-ellipsis">
      Technology Stack
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#getting-help">
<span class="md-ellipsis">
      Getting Help
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#contributing">
<span class="md-ellipsis">
      Contributing
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#license">
<span class="md-ellipsis">
      License
    </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_2" type="checkbox"/>
<label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">
<span class="md-ellipsis">
    Getting Started
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_2_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_2">
<span class="md-nav__icon md-icon"></span>
            Getting Started
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="getting-started/installation/">
<span class="md-ellipsis">
    Installation
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="getting-started/quick-start.md">
<span class="md-ellipsis">
    Quick Start
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="getting-started/requirements.md">
<span class="md-ellipsis">
    System Requirements
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_3" type="checkbox"/>
<label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
<span class="md-ellipsis">
    User Guide
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_3_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_3">
<span class="md-nav__icon md-icon"></span>
            User Guide
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="user-guide/overview.md">
<span class="md-ellipsis">
    Overview
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="user-guide/air-duct-sizer/">
<span class="md-ellipsis">
    Air Duct Sizer
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="user-guide/project-management.md">
<span class="md-ellipsis">
    Project Management
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="user-guide/units-standards.md">
<span class="md-ellipsis">
    Units and Standards
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="user-guide/offline-usage.md">
<span class="md-ellipsis">
    Offline Usage
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_4" type="checkbox"/>
<label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
<span class="md-ellipsis">
    API Reference
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_4_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_4">
<span class="md-nav__icon md-icon"></span>
            API Reference
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="api/overview.md">
<span class="md-ellipsis">
    Overview
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="api/air-duct-calculator/">
<span class="md-ellipsis">
    Air Duct Calculator
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="api/validation.md">
<span class="md-ellipsis">
    Validation
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="api/data-models.md">
<span class="md-ellipsis">
    Data Models
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5" type="checkbox"/>
<label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
<span class="md-ellipsis">
    Developer Guide
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_5">
<span class="md-nav__icon md-icon"></span>
            Developer Guide
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="developer/architecture.md">
<span class="md-ellipsis">
    Architecture
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="developer/contributing.md">
<span class="md-ellipsis">
    Contributing
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="developer/testing.md">
<span class="md-ellipsis">
    Testing
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="developer/building.md">
<span class="md-ellipsis">
    Building
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_6" type="checkbox"/>
<label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
<span class="md-ellipsis">
    Examples
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_6_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_6">
<span class="md-nav__icon md-icon"></span>
            Examples
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="examples/basic-calculations.md">
<span class="md-ellipsis">
    Basic Calculations
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="examples/advanced-usage.md">
<span class="md-ellipsis">
    Advanced Usage
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="examples/integration.md">
<span class="md-ellipsis">
    Integration
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
</ul>
</nav>
</div>
</div>
</div>
<div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc">
<div class="md-sidebar__scrollwrap">
<div class="md-sidebar__inner">
<nav aria-label="Table of contents" class="md-nav md-nav--secondary">
<label class="md-nav__title" for="__toc">
<span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
<ul class="md-nav__list" data-md-component="toc" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="#what-is-sizewise-suite">
<span class="md-ellipsis">
      What is SizeWise Suite?
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#core-modules">
<span class="md-ellipsis">
      Core Modules
    </span>
</a>
<nav aria-label="Core Modules" class="md-nav">
<ul class="md-nav__list">
<li class="md-nav__item">
<a class="md-nav__link" href="#air-duct-sizer">
<span class="md-ellipsis">
      🌬️ Air Duct Sizer
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#grease-duct-sizer-coming-soon">
<span class="md-ellipsis">
      🔥 Grease Duct Sizer (Coming Soon)
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#engine-exhaust-sizer-coming-soon">
<span class="md-ellipsis">
      🚗 Engine Exhaust Sizer (Coming Soon)
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#boiler-vent-sizer-coming-soon">
<span class="md-ellipsis">
      🔥 Boiler Vent Sizer (Coming Soon)
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#estimating-app-coming-soon">
<span class="md-ellipsis">
      💰 Estimating App (Coming Soon)
    </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#key-features">
<span class="md-ellipsis">
      Key Features
    </span>
</a>
<nav aria-label="Key Features" class="md-nav">
<ul class="md-nav__list">
<li class="md-nav__item">
<a class="md-nav__link" href="#standards-compliance">
<span class="md-ellipsis">
      ✅ Standards Compliance
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#unit-conversion">
<span class="md-ellipsis">
      🔄 Unit Conversion
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#offline-functionality">
<span class="md-ellipsis">
      💾 Offline Functionality
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#modern-interface">
<span class="md-ellipsis">
      📱 Modern Interface
    </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#quick-start">
<span class="md-ellipsis">
      Quick Start
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#architecture-overview">
<span class="md-ellipsis">
      Architecture Overview
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#technology-stack">
<span class="md-ellipsis">
      Technology Stack
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#getting-help">
<span class="md-ellipsis">
      Getting Help
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#contributing">
<span class="md-ellipsis">
      Contributing
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#license">
<span class="md-ellipsis">
      License
    </span>
</a>
</li>
</ul>
</nav>
</div>
</div>
</div>
<div class="md-content" data-md-component="content">
<article class="md-content__inner md-typeset">
<a class="md-content__button md-icon" href="https://github.com/sizewise-suite/sizewise-suite/edit/main/docs/index.md" rel="edit" title="Edit this page">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M10 20H6V4h7v5h5v3.1l2-2V8l-6-6H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h4zm10.2-7c.1 0 .3.1.4.2l1.3 1.3c.******* 0 .8l-1 1-2.1-2.1 1-1c.1-.1.2-.2.4-.2m0 3.9L14.1 23H12v-2.1l6.1-6.1z"></path></svg>
</a>
<a class="md-content__button md-icon" href="https://github.com/sizewise-suite/sizewise-suite/raw/main/docs/index.md" title="View source of this page">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M17 18c.56 0 1 .44 1 1s-.44 1-1 1-1-.44-1-1 .44-1 1-1m0-3c-2.73 0-5.06 1.66-6 4 .94 2.34 3.27 4 6 4s5.06-1.66 6-4c-.94-2.34-3.27-4-6-4m0 6.5a2.5 2.5 0 0 1-2.5-2.5 2.5 2.5 0 0 1 2.5-2.5 2.5 2.5 0 0 1 2.5 2.5 2.5 2.5 0 0 1-2.5 2.5M9.27 20H6V4h7v5h5v4.07c.7.08 1.36.25 2 .49V8l-6-6H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h4.5a8.2 8.2 0 0 1-1.23-2"></path></svg>
</a>
<h1 id="sizewise-suite-documentation">SizeWise Suite Documentation<a class="headerlink" href="#sizewise-suite-documentation" title="Permanent link">¶</a></h1>
<p>Welcome to SizeWise Suite, a comprehensive modular HVAC engineering and estimating platform designed for mechanical engineers, estimators, QA professionals, and project managers.</p>
<h2 id="what-is-sizewise-suite">What is SizeWise Suite?<a class="headerlink" href="#what-is-sizewise-suite" title="Permanent link">¶</a></h2>
<p>SizeWise Suite is an <strong>offline-first</strong>, <strong>modular platform</strong> that unifies duct sizing, vent design, and cost estimating in a single workspace. Built with modern web technologies, it provides:</p>
<ul>
<li><strong>Standards Compliance</strong>: Full compliance with SMACNA, NFPA, and ASHRAE standards</li>
<li><strong>Offline-First Design</strong>: Work anywhere, anytime without internet connectivity</li>
<li><strong>Bidirectional Unit Conversion</strong>: Seamless Imperial/SI unit support</li>
<li><strong>Modular Architecture</strong>: Extensible design for future HVAC modules</li>
<li><strong>Progressive Web App</strong>: Install and use like a native application</li>
</ul>
<h2 id="core-modules">Core Modules<a class="headerlink" href="#core-modules" title="Permanent link">¶</a></h2>
<h3 id="air-duct-sizer">🌬️ Air Duct Sizer<a class="headerlink" href="#air-duct-sizer" title="Permanent link">¶</a></h3>
<p>Calculate optimal duct sizes for HVAC systems with SMACNA compliance checking, friction loss analysis, and velocity validation.</p>
<h3 id="grease-duct-sizer-coming-soon">🔥 Grease Duct Sizer <em>(Coming Soon)</em><a class="headerlink" href="#grease-duct-sizer-coming-soon" title="Permanent link">¶</a></h3>
<p>Design grease exhaust systems with NFPA 96 compliance and fire safety considerations.</p>
<h3 id="engine-exhaust-sizer-coming-soon">🚗 Engine Exhaust Sizer <em>(Coming Soon)</em><a class="headerlink" href="#engine-exhaust-sizer-coming-soon" title="Permanent link">¶</a></h3>
<p>Size engine exhaust systems for parking garages and mechanical rooms.</p>
<h3 id="boiler-vent-sizer-coming-soon">🔥 Boiler Vent Sizer <em>(Coming Soon)</em><a class="headerlink" href="#boiler-vent-sizer-coming-soon" title="Permanent link">¶</a></h3>
<p>Calculate boiler vent requirements with code compliance checking.</p>
<h3 id="estimating-app-coming-soon">💰 Estimating App <em>(Coming Soon)</em><a class="headerlink" href="#estimating-app-coming-soon" title="Permanent link">¶</a></h3>
<p>Generate accurate cost estimates for HVAC projects with material and labor calculations.</p>
<h2 id="key-features">Key Features<a class="headerlink" href="#key-features" title="Permanent link">¶</a></h2>
<h3 id="standards-compliance">✅ Standards Compliance<a class="headerlink" href="#standards-compliance" title="Permanent link">¶</a></h3>
<ul>
<li><strong>SMACNA</strong>: Sheet Metal and Air Conditioning Contractors' National Association standards</li>
<li><strong>NFPA</strong>: National Fire Protection Association codes</li>
<li><strong>ASHRAE</strong>: American Society of Heating, Refrigerating and Air-Conditioning Engineers standards</li>
</ul>
<h3 id="unit-conversion">🔄 Unit Conversion<a class="headerlink" href="#unit-conversion" title="Permanent link">¶</a></h3>
<ul>
<li>Seamless conversion between Imperial and Metric units</li>
<li>Temperature, pressure, flow rate, and dimensional conversions</li>
<li>Automatic unit detection and validation</li>
</ul>
<h3 id="offline-functionality">💾 Offline Functionality<a class="headerlink" href="#offline-functionality" title="Permanent link">¶</a></h3>
<ul>
<li>Complete offline operation with IndexedDB storage</li>
<li>Project and calculation persistence</li>
<li>Progressive Web App (PWA) capabilities</li>
<li>No internet required for core functionality</li>
</ul>
<h3 id="modern-interface">📱 Modern Interface<a class="headerlink" href="#modern-interface" title="Permanent link">¶</a></h3>
<ul>
<li>Responsive design for desktop, tablet, and mobile</li>
<li>Intuitive user interface with real-time validation</li>
<li>Dark/light mode support</li>
<li>Accessibility compliant</li>
</ul>
<h2 id="quick-start">Quick Start<a class="headerlink" href="#quick-start" title="Permanent link">¶</a></h2>
<ol>
<li><strong><a href="getting-started/installation/">Install SizeWise Suite</a></strong> on your device</li>
<li><strong><a href="getting-started/quick-start.md">Follow the Quick Start Guide</a></strong> to perform your first calculation</li>
<li><strong><a href="user-guide/overview.md">Explore the User Guide</a></strong> for detailed feature documentation</li>
</ol>
<h2 id="architecture-overview">Architecture Overview<a class="headerlink" href="#architecture-overview" title="Permanent link">¶</a></h2>
<div class="mermaid">graph TB
    A[Frontend - Modular JavaScript] --&gt; B[Core Services]
    B --&gt; C[Storage Manager - IndexedDB]
    B --&gt; D[API Client]
    B --&gt; E[Units Manager]
    B --&gt; F[UI Manager]

    D --&gt; G[Backend - Flask API]
    G --&gt; H[Calculation Engine]
    G --&gt; I[Validation System]
    G --&gt; J[Standards Compliance]

    H --&gt; K[Air Duct Calculator]
    H --&gt; L[Future Modules]

    I --&gt; M[Schema Validation]
    I --&gt; N[Input Validation]

    J --&gt; O[SMACNA Standards]
    J --&gt; P[NFPA Codes]
    J --&gt; Q[ASHRAE Standards]
</div>
<h2 id="technology-stack">Technology Stack<a class="headerlink" href="#technology-stack" title="Permanent link">¶</a></h2>
<ul>
<li><strong>Frontend</strong>: Modular JavaScript, Progressive Web App (PWA)</li>
<li><strong>Backend</strong>: Python Flask with comprehensive calculation engines</li>
<li><strong>Storage</strong>: IndexedDB for offline data persistence</li>
<li><strong>Validation</strong>: Pydantic schemas with JSONSchema validation</li>
<li><strong>Testing</strong>: Jest (frontend) + pytest (backend)</li>
<li><strong>Documentation</strong>: MkDocs with Material theme</li>
</ul>
<h2 id="getting-help">Getting Help<a class="headerlink" href="#getting-help" title="Permanent link">¶</a></h2>
<ul>
<li><strong><a href="user-guide/overview.md">User Guide</a></strong>: Comprehensive usage documentation</li>
<li><strong><a href="api/overview.md">API Reference</a></strong>: Technical API documentation</li>
<li><strong><a href="developer/architecture.md">Developer Guide</a></strong>: Architecture and development information</li>
<li><strong><a href="examples/basic-calculations.md">Examples</a></strong>: Practical usage examples</li>
</ul>
<h2 id="contributing">Contributing<a class="headerlink" href="#contributing" title="Permanent link">¶</a></h2>
<p>SizeWise Suite is designed to be extensible and welcomes contributions. See our <strong><a href="developer/contributing.md">Contributing Guide</a></strong> for information on:</p>
<ul>
<li>Adding new calculation modules</li>
<li>Improving existing functionality</li>
<li>Reporting bugs and requesting features</li>
<li>Development setup and guidelines</li>
</ul>
<h2 id="license">License<a class="headerlink" href="#license" title="Permanent link">¶</a></h2>
<p>SizeWise Suite is released under the MIT License. See the <a href="https://github.com/sizewise-suite/sizewise-suite/blob/main/LICENSE">LICENSE</a> file for details.</p>
<hr/>
<p><em>Built with ❤️ for the HVAC engineering community</em></p>
</article>
</div>
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
</div>
<button class="md-top md-icon" data-md-component="top" hidden="" type="button">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"></path></svg>
  Back to top
</button>
</main>
<footer class="md-footer">
<div class="md-footer-meta md-typeset">
<div class="md-footer-meta__inner md-grid">
<div class="md-copyright">
<div class="md-copyright__highlight">
      Copyright © 2024 SizeWise Suite Team
    </div>
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" rel="noopener" target="_blank">
      Material for MkDocs
    </a>
</div>
<div class="md-social">
<a class="md-social__link" href="https://github.com/sizewise-suite/sizewise-suite" rel="noopener" target="_blank" title="github.com">
<svg viewbox="0 0 496 512" xmlns="http://www.w3.org/2000/svg"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"></path></svg>
</a>
<a class="md-social__link" href="https://sizewise-suite.github.io/" rel="noopener" target="_blank" title="sizewise-suite.github.io">
<svg viewbox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M352 256c0 22.2-1.2 43.6-3.3 64H163.4c-2.2-20.4-3.3-41.8-3.3-64s1.2-43.6 3.3-64h185.3c2.2 20.4 3.3 41.8 3.3 64m28.8-64h123.1c5.3 20.5 8.1 41.9 8.1 64s-2.8 43.5-8.1 64H380.8c2.1-20.6 3.2-42 3.2-64s-1.1-43.4-3.2-64m112.6-32H376.7c-10-63.9-29.8-117.4-55.3-151.6 78.3 20.7 142 77.5 171.9 151.6zm-149.1 0H167.7c6.1-36.4 15.5-68.6 27-94.7 10.5-23.6 22.2-40.7 33.5-51.5C239.4 3.2 248.7 0 256 0s16.6 3.2 27.8 13.8c11.3 10.8 23 27.9 33.5 51.5 11.6 26 20.9 58.2 27 94.7m-209 0H18.6c30-74.1 93.6-130.9 172-151.6-25.5 34.2-45.3 87.7-55.3 151.6M8.1 192h123.1c-2.1 20.6-3.2 42-3.2 64s1.1 43.4 3.2 64H8.1C2.8 299.5 0 278.1 0 256s2.8-43.5 8.1-64m186.6 254.6c-11.6-26-20.9-58.2-27-94.6h176.6c-6.1 36.4-15.5 68.6-27 94.6-10.5 23.6-22.2 40.7-33.5 51.5-11.2 10.7-20.5 13.9-27.8 13.9s-16.6-3.2-27.8-13.8c-11.3-10.8-23-27.9-33.5-51.5zM135.3 352c10 63.9 29.8 117.4 55.3 151.6-78.4-20.7-142-77.5-172-151.6zm358.1 0c-30 74.1-93.6 130.9-171.9 151.6 25.5-34.2 45.2-87.7 55.3-151.6h116.7z"></path></svg>
</a>
</div>
</div>
</div>
</footer>
</div>
<div class="md-dialog" data-md-component="dialog">
<div class="md-dialog__inner md-typeset"></div>
</div>
<script id="__config" type="application/json">{"base": ".", "features": ["navigation.tabs", "navigation.sections", "navigation.expand", "navigation.top", "search.highlight", "search.share", "content.code.copy", "content.action.edit", "content.action.view"], "search": "assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": {"provider": "mike"}}</script>
<script src="assets/javascripts/bundle.56ea9cef.min.js"></script>
<script type="module">import mermaid from "https://unpkg.com/mermaid@10.4.0/dist/mermaid.esm.min.mjs";
mermaid.initialize({
    theme: "base",
    themeVariables: {
        primaryColor: "#1976d2",
        primaryTextColor: "#ffffff"
    }
});</script></body>
</html>