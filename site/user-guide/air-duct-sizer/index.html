
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for SizeWise Suite - A modular HVAC engineering and estimating platform">
      
      
        <meta name="author" content="SizeWise Suite Team">
      
      
        <link rel="canonical" href="https://sizewise-suite.github.io/user-guide/air-duct-sizer/">
      
      
        <link rel="prev" href="../../getting-started/installation/">
      
      
        <link rel="next" href="../../api/air-duct-calculator/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.15">
    
    
      
        <title>Air Duct Sizer - SizeWise Suite Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
      
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#air-duct-sizer-user-guide" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
      <div data-md-color-scheme="default" data-md-component="outdated" hidden>
        
      </div>
    
    
      

<header class="md-header" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="SizeWise Suite Documentation" class="md-header__button md-logo" aria-label="SizeWise Suite Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            SizeWise Suite Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Air Duct Sizer
            
          </span>
        </div>
      </div>
    </div>
    
      
        <form class="md-header__option" data-md-component="palette">
  
    
    
    
    <input class="md-option" data-md-color-media="" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue"  aria-label="Switch to dark mode"  type="radio" name="__palette" id="__palette_0">
    
      <label class="md-header__button md-icon" title="Switch to dark mode" for="__palette_1" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
    
    
    
    <input class="md-option" data-md-color-media="" data-md-color-scheme="slate" data-md-color-primary="blue" data-md-color-accent="blue"  aria-label="Switch to light mode"  type="radio" name="__palette" id="__palette_1">
    
      <label class="md-header__button md-icon" title="Switch to light mode" for="__palette_0" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
</form>
      
    
    
      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
          <a href="javascript:void(0)" class="md-search__icon md-icon" title="Share" aria-label="Share" data-clipboard data-clipboard-text="" data-md-component="search-share" tabindex="-1">
            
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"/></svg>
          </a>
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
      <div class="md-header__source">
        <a href="https://github.com/sizewise-suite/sizewise-suite" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
  </div>
  <div class="md-source__repository">
    sizewise-suite/sizewise-suite
  </div>
</a>
      </div>
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
            
<nav class="md-tabs" aria-label="Tabs" data-md-component="tabs">
  <div class="md-grid">
    <ul class="md-tabs__list">
      
        
  
  
  
  
    <li class="md-tabs__item">
      <a href="../.." class="md-tabs__link">
        
  
  
    
  
  Home

      </a>
    </li>
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../getting-started/installation/" class="md-tabs__link">
          
  
  
  Getting Started

        </a>
      </li>
    
  

      
        
  
  
  
    
  
  
    
    
      <li class="md-tabs__item md-tabs__item--active">
        <a href="../overview.md" class="md-tabs__link">
          
  
  
  User Guide

        </a>
      </li>
    
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../api/overview.md" class="md-tabs__link">
          
  
  
  API Reference

        </a>
      </li>
    
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../developer/architecture.md" class="md-tabs__link">
          
  
  
  Developer Guide

        </a>
      </li>
    
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../examples/basic-calculations.md" class="md-tabs__link">
          
  
  
  Examples

        </a>
      </li>
    
  

      
    </ul>
  </div>
</nav>
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    


  


<nav class="md-nav md-nav--primary md-nav--lifted" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="SizeWise Suite Documentation" class="md-nav__button md-logo" aria-label="SizeWise Suite Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    SizeWise Suite Documentation
  </label>
  
    <div class="md-nav__source">
      <a href="https://github.com/sizewise-suite/sizewise-suite" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
  </div>
  <div class="md-source__repository">
    sizewise-suite/sizewise-suite
  </div>
</a>
    </div>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_2" >
        
          
          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Getting Started
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            Getting Started
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../getting-started/installation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Installation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../getting-started/quick-start.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quick Start
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../getting-started/requirements.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    System Requirements
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
      
        
        
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" checked>
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="">
            
  
  
  <span class="md-ellipsis">
    User Guide
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            User Guide
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../overview.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Air Duct Sizer
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Air Duct Sizer
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#overview" class="md-nav__link">
    <span class="md-ellipsis">
      Overview
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#getting-started" class="md-nav__link">
    <span class="md-ellipsis">
      Getting Started
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Getting Started">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#accessing-the-air-duct-sizer" class="md-nav__link">
    <span class="md-ellipsis">
      Accessing the Air Duct Sizer
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#basic-calculation" class="md-nav__link">
    <span class="md-ellipsis">
      Basic Calculation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#input-parameters" class="md-nav__link">
    <span class="md-ellipsis">
      Input Parameters
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Input Parameters">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#required-fields" class="md-nav__link">
    <span class="md-ellipsis">
      Required Fields
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Required Fields">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#airflow-rate" class="md-nav__link">
    <span class="md-ellipsis">
      Airflow Rate
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#duct-type" class="md-nav__link">
    <span class="md-ellipsis">
      Duct Type
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#friction-rate" class="md-nav__link">
    <span class="md-ellipsis">
      Friction Rate
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#optional-parameters" class="md-nav__link">
    <span class="md-ellipsis">
      Optional Parameters
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Optional Parameters">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#material-type" class="md-nav__link">
    <span class="md-ellipsis">
      Material Type
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#system-pressure-class" class="md-nav__link">
    <span class="md-ellipsis">
      System Pressure Class
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#understanding-results" class="md-nav__link">
    <span class="md-ellipsis">
      Understanding Results
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Understanding Results">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#duct-dimensions" class="md-nav__link">
    <span class="md-ellipsis">
      Duct Dimensions
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Duct Dimensions">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rectangular-ducts" class="md-nav__link">
    <span class="md-ellipsis">
      Rectangular Ducts
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#round-ducts" class="md-nav__link">
    <span class="md-ellipsis">
      Round Ducts
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Metrics
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Performance Metrics">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#velocity" class="md-nav__link">
    <span class="md-ellipsis">
      Velocity
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#pressure-loss" class="md-nav__link">
    <span class="md-ellipsis">
      Pressure Loss
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cross-sectional-area" class="md-nav__link">
    <span class="md-ellipsis">
      Cross-Sectional Area
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#equivalent-diameter" class="md-nav__link">
    <span class="md-ellipsis">
      Equivalent Diameter
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#standards-compliance" class="md-nav__link">
    <span class="md-ellipsis">
      Standards Compliance
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Standards Compliance">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#smacna-standards" class="md-nav__link">
    <span class="md-ellipsis">
      SMACNA Standards
    </span>
  </a>
  
    <nav class="md-nav" aria-label="SMACNA Standards">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#velocity-limits" class="md-nav__link">
    <span class="md-ellipsis">
      Velocity Limits
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#pressure-classifications" class="md-nav__link">
    <span class="md-ellipsis">
      Pressure Classifications
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#construction-standards" class="md-nav__link">
    <span class="md-ellipsis">
      Construction Standards
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#validation-warnings" class="md-nav__link">
    <span class="md-ellipsis">
      Validation Warnings
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#friction-rate-selection" class="md-nav__link">
    <span class="md-ellipsis">
      Friction Rate Selection
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Friction Rate Selection">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#low-friction-005-008-in-wg100-ft" class="md-nav__link">
    <span class="md-ellipsis">
      Low Friction (0.05-0.08 in. w.g./100 ft)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#medium-friction-008-015-in-wg100-ft" class="md-nav__link">
    <span class="md-ellipsis">
      Medium Friction (0.08-0.15 in. w.g./100 ft)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#high-friction-015-05-in-wg100-ft" class="md-nav__link">
    <span class="md-ellipsis">
      High Friction (0.15-0.5 in. w.g./100 ft)
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#duct-type-selection" class="md-nav__link">
    <span class="md-ellipsis">
      Duct Type Selection
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Duct Type Selection">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rectangular-ducts_1" class="md-nav__link">
    <span class="md-ellipsis">
      Rectangular Ducts
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#round-ducts_1" class="md-nav__link">
    <span class="md-ellipsis">
      Round Ducts
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Common Issues">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#velocity-too-high-warning" class="md-nav__link">
    <span class="md-ellipsis">
      "Velocity Too High" Warning
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#unrealistic-dimensions-error" class="md-nav__link">
    <span class="md-ellipsis">
      "Unrealistic Dimensions" Error
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#standards-violation-warning" class="md-nav__link">
    <span class="md-ellipsis">
      "Standards Violation" Warning
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#validation-errors" class="md-nav__link">
    <span class="md-ellipsis">
      Validation Errors
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Validation Errors">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#missing-required-fields" class="md-nav__link">
    <span class="md-ellipsis">
      Missing Required Fields
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#out-of-range-values" class="md-nav__link">
    <span class="md-ellipsis">
      Out of Range Values
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#invalid-combinations" class="md-nav__link">
    <span class="md-ellipsis">
      Invalid Combinations
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#advanced-features" class="md-nav__link">
    <span class="md-ellipsis">
      Advanced Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Advanced Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#material-properties" class="md-nav__link">
    <span class="md-ellipsis">
      Material Properties
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#pressure-loss-calculations" class="md-nav__link">
    <span class="md-ellipsis">
      Pressure Loss Calculations
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#standard-sizes" class="md-nav__link">
    <span class="md-ellipsis">
      Standard Sizes
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Standard Sizes">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#round-ducts-inches" class="md-nav__link">
    <span class="md-ellipsis">
      Round Ducts (inches)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#rectangular-ducts_2" class="md-nav__link">
    <span class="md-ellipsis">
      Rectangular Ducts
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#integration-with-projects" class="md-nav__link">
    <span class="md-ellipsis">
      Integration with Projects
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Integration with Projects">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#saving-calculations" class="md-nav__link">
    <span class="md-ellipsis">
      Saving Calculations
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#exporting-results" class="md-nav__link">
    <span class="md-ellipsis">
      Exporting Results
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#project-management" class="md-nav__link">
    <span class="md-ellipsis">
      Project Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#next-steps" class="md-nav__link">
    <span class="md-ellipsis">
      Next Steps
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../project-management.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Project Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../units-standards.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Units and Standards
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../offline-usage.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Offline Usage
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    API Reference
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            API Reference
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../api/overview.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../api/air-duct-calculator/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Air Duct Calculator
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../api/validation.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Validation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../api/data-models.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Data Models
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Developer Guide
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Developer Guide
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../developer/architecture.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../developer/contributing.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Contributing
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../developer/testing.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Testing
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../developer/building.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Building
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Examples
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            Examples
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../examples/basic-calculations.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Basic Calculations
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../examples/advanced-usage.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Advanced Usage
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../examples/integration.md" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#overview" class="md-nav__link">
    <span class="md-ellipsis">
      Overview
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#getting-started" class="md-nav__link">
    <span class="md-ellipsis">
      Getting Started
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Getting Started">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#accessing-the-air-duct-sizer" class="md-nav__link">
    <span class="md-ellipsis">
      Accessing the Air Duct Sizer
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#basic-calculation" class="md-nav__link">
    <span class="md-ellipsis">
      Basic Calculation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#input-parameters" class="md-nav__link">
    <span class="md-ellipsis">
      Input Parameters
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Input Parameters">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#required-fields" class="md-nav__link">
    <span class="md-ellipsis">
      Required Fields
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Required Fields">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#airflow-rate" class="md-nav__link">
    <span class="md-ellipsis">
      Airflow Rate
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#duct-type" class="md-nav__link">
    <span class="md-ellipsis">
      Duct Type
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#friction-rate" class="md-nav__link">
    <span class="md-ellipsis">
      Friction Rate
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#optional-parameters" class="md-nav__link">
    <span class="md-ellipsis">
      Optional Parameters
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Optional Parameters">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#material-type" class="md-nav__link">
    <span class="md-ellipsis">
      Material Type
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#system-pressure-class" class="md-nav__link">
    <span class="md-ellipsis">
      System Pressure Class
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#understanding-results" class="md-nav__link">
    <span class="md-ellipsis">
      Understanding Results
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Understanding Results">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#duct-dimensions" class="md-nav__link">
    <span class="md-ellipsis">
      Duct Dimensions
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Duct Dimensions">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rectangular-ducts" class="md-nav__link">
    <span class="md-ellipsis">
      Rectangular Ducts
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#round-ducts" class="md-nav__link">
    <span class="md-ellipsis">
      Round Ducts
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Metrics
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Performance Metrics">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#velocity" class="md-nav__link">
    <span class="md-ellipsis">
      Velocity
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#pressure-loss" class="md-nav__link">
    <span class="md-ellipsis">
      Pressure Loss
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cross-sectional-area" class="md-nav__link">
    <span class="md-ellipsis">
      Cross-Sectional Area
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#equivalent-diameter" class="md-nav__link">
    <span class="md-ellipsis">
      Equivalent Diameter
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#standards-compliance" class="md-nav__link">
    <span class="md-ellipsis">
      Standards Compliance
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Standards Compliance">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#smacna-standards" class="md-nav__link">
    <span class="md-ellipsis">
      SMACNA Standards
    </span>
  </a>
  
    <nav class="md-nav" aria-label="SMACNA Standards">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#velocity-limits" class="md-nav__link">
    <span class="md-ellipsis">
      Velocity Limits
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#pressure-classifications" class="md-nav__link">
    <span class="md-ellipsis">
      Pressure Classifications
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#construction-standards" class="md-nav__link">
    <span class="md-ellipsis">
      Construction Standards
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#validation-warnings" class="md-nav__link">
    <span class="md-ellipsis">
      Validation Warnings
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#friction-rate-selection" class="md-nav__link">
    <span class="md-ellipsis">
      Friction Rate Selection
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Friction Rate Selection">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#low-friction-005-008-in-wg100-ft" class="md-nav__link">
    <span class="md-ellipsis">
      Low Friction (0.05-0.08 in. w.g./100 ft)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#medium-friction-008-015-in-wg100-ft" class="md-nav__link">
    <span class="md-ellipsis">
      Medium Friction (0.08-0.15 in. w.g./100 ft)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#high-friction-015-05-in-wg100-ft" class="md-nav__link">
    <span class="md-ellipsis">
      High Friction (0.15-0.5 in. w.g./100 ft)
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#duct-type-selection" class="md-nav__link">
    <span class="md-ellipsis">
      Duct Type Selection
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Duct Type Selection">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rectangular-ducts_1" class="md-nav__link">
    <span class="md-ellipsis">
      Rectangular Ducts
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#round-ducts_1" class="md-nav__link">
    <span class="md-ellipsis">
      Round Ducts
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Common Issues">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#velocity-too-high-warning" class="md-nav__link">
    <span class="md-ellipsis">
      "Velocity Too High" Warning
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#unrealistic-dimensions-error" class="md-nav__link">
    <span class="md-ellipsis">
      "Unrealistic Dimensions" Error
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#standards-violation-warning" class="md-nav__link">
    <span class="md-ellipsis">
      "Standards Violation" Warning
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#validation-errors" class="md-nav__link">
    <span class="md-ellipsis">
      Validation Errors
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Validation Errors">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#missing-required-fields" class="md-nav__link">
    <span class="md-ellipsis">
      Missing Required Fields
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#out-of-range-values" class="md-nav__link">
    <span class="md-ellipsis">
      Out of Range Values
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#invalid-combinations" class="md-nav__link">
    <span class="md-ellipsis">
      Invalid Combinations
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#advanced-features" class="md-nav__link">
    <span class="md-ellipsis">
      Advanced Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Advanced Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#material-properties" class="md-nav__link">
    <span class="md-ellipsis">
      Material Properties
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#pressure-loss-calculations" class="md-nav__link">
    <span class="md-ellipsis">
      Pressure Loss Calculations
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#standard-sizes" class="md-nav__link">
    <span class="md-ellipsis">
      Standard Sizes
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Standard Sizes">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#round-ducts-inches" class="md-nav__link">
    <span class="md-ellipsis">
      Round Ducts (inches)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#rectangular-ducts_2" class="md-nav__link">
    <span class="md-ellipsis">
      Rectangular Ducts
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#integration-with-projects" class="md-nav__link">
    <span class="md-ellipsis">
      Integration with Projects
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Integration with Projects">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#saving-calculations" class="md-nav__link">
    <span class="md-ellipsis">
      Saving Calculations
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#exporting-results" class="md-nav__link">
    <span class="md-ellipsis">
      Exporting Results
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#project-management" class="md-nav__link">
    <span class="md-ellipsis">
      Project Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#next-steps" class="md-nav__link">
    <span class="md-ellipsis">
      Next Steps
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  


  
    <a href="https://github.com/sizewise-suite/sizewise-suite/edit/main/docs/user-guide/air-duct-sizer.md" title="Edit this page" class="md-content__button md-icon" rel="edit">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M10 20H6V4h7v5h5v3.1l2-2V8l-6-6H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h4zm10.2-7c.1 0 .3.1.4.2l1.3 1.3c.******* 0 .8l-1 1-2.1-2.1 1-1c.1-.1.2-.2.4-.2m0 3.9L14.1 23H12v-2.1l6.1-6.1z"/></svg>
    </a>
  
  
    
      
    
    <a href="https://github.com/sizewise-suite/sizewise-suite/raw/main/docs/user-guide/air-duct-sizer.md" title="View source of this page" class="md-content__button md-icon">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M17 18c.56 0 1 .44 1 1s-.44 1-1 1-1-.44-1-1 .44-1 1-1m0-3c-2.73 0-5.06 1.66-6 4 .94 2.34 3.27 4 6 4s5.06-1.66 6-4c-.94-2.34-3.27-4-6-4m0 6.5a2.5 2.5 0 0 1-2.5-2.5 2.5 2.5 0 0 1 2.5-2.5 2.5 2.5 0 0 1 2.5 2.5 2.5 2.5 0 0 1-2.5 2.5M9.27 20H6V4h7v5h5v4.07c.7.08 1.36.25 2 .49V8l-6-6H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h4.5a8.2 8.2 0 0 1-1.23-2"/></svg>
    </a>
  


<h1 id="air-duct-sizer-user-guide">Air Duct Sizer User Guide<a class="headerlink" href="#air-duct-sizer-user-guide" title="Permanent link">&para;</a></h1>
<p>The Air Duct Sizer is SizeWise Suite's flagship module for calculating optimal duct sizes in HVAC systems. It provides SMACNA-compliant calculations with comprehensive validation and standards checking.</p>
<h2 id="overview">Overview<a class="headerlink" href="#overview" title="Permanent link">&para;</a></h2>
<p>The Air Duct Sizer helps you:</p>
<ul>
<li><strong>Calculate optimal duct dimensions</strong> for given airflow requirements</li>
<li><strong>Validate designs</strong> against SMACNA standards</li>
<li><strong>Analyze pressure losses</strong> and system performance</li>
<li><strong>Compare rectangular vs. round duct options</strong></li>
<li><strong>Ensure velocity compliance</strong> with industry standards</li>
</ul>
<h2 id="getting-started">Getting Started<a class="headerlink" href="#getting-started" title="Permanent link">&para;</a></h2>
<h3 id="accessing-the-air-duct-sizer">Accessing the Air Duct Sizer<a class="headerlink" href="#accessing-the-air-duct-sizer" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Open SizeWise Suite</strong> in your browser</li>
<li><strong>Navigate to the Air Duct Sizer</strong> module from the main dashboard</li>
<li><strong>Select your preferred units</strong> (Imperial or Metric)</li>
</ol>
<h3 id="basic-calculation">Basic Calculation<a class="headerlink" href="#basic-calculation" title="Permanent link">&para;</a></h3>
<p>To perform a basic duct sizing calculation:</p>
<ol>
<li><strong>Enter the airflow rate</strong> (CFM for Imperial, L/s for Metric)</li>
<li><strong>Select duct type</strong> (Rectangular or Round)</li>
<li><strong>Set friction rate</strong> (in. w.g./100 ft for Imperial, Pa/m for Metric)</li>
<li><strong>Click "Calculate"</strong> to get results</li>
</ol>
<h2 id="input-parameters">Input Parameters<a class="headerlink" href="#input-parameters" title="Permanent link">&para;</a></h2>
<h3 id="required-fields">Required Fields<a class="headerlink" href="#required-fields" title="Permanent link">&para;</a></h3>
<h4 id="airflow-rate">Airflow Rate<a class="headerlink" href="#airflow-rate" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Imperial</strong>: CFM (Cubic Feet per Minute)</li>
<li><strong>Metric</strong>: L/s (Liters per Second)</li>
<li><strong>Range</strong>: 25 - 50,000 CFM (12 - 24,000 L/s)</li>
<li><strong>Validation</strong>: Must be positive number</li>
</ul>
<h4 id="duct-type">Duct Type<a class="headerlink" href="#duct-type" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Rectangular</strong>: Traditional rectangular ductwork</li>
<li><strong>Round</strong>: Circular ductwork (typically more efficient)</li>
</ul>
<h4 id="friction-rate">Friction Rate<a class="headerlink" href="#friction-rate" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Imperial</strong>: in. w.g./100 ft (inches water gauge per 100 feet)</li>
<li><strong>Metric</strong>: Pa/m (Pascals per meter)</li>
<li><strong>Typical Range</strong>: 0.05 - 0.5 in. w.g./100 ft (0.4 - 4.0 Pa/m)</li>
<li><strong>Recommended</strong>: 0.08 - 0.15 in. w.g./100 ft for most applications</li>
</ul>
<h3 id="optional-parameters">Optional Parameters<a class="headerlink" href="#optional-parameters" title="Permanent link">&para;</a></h3>
<h4 id="material-type">Material Type<a class="headerlink" href="#material-type" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Galvanized Steel</strong> (default): Standard ductwork material</li>
<li><strong>Aluminum</strong>: Lightweight alternative</li>
<li><strong>Stainless Steel</strong>: Corrosion-resistant option</li>
<li><strong>Flexible Duct</strong>: For short runs and connections</li>
</ul>
<h4 id="system-pressure-class">System Pressure Class<a class="headerlink" href="#system-pressure-class" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Low Pressure</strong>: Up to 2 in. w.g. (500 Pa)</li>
<li><strong>Medium Pressure</strong>: 2-6 in. w.g. (500-1500 Pa)</li>
<li><strong>High Pressure</strong>: 6-10 in. w.g. (1500-2500 Pa)</li>
</ul>
<h2 id="understanding-results">Understanding Results<a class="headerlink" href="#understanding-results" title="Permanent link">&para;</a></h2>
<h3 id="duct-dimensions">Duct Dimensions<a class="headerlink" href="#duct-dimensions" title="Permanent link">&para;</a></h3>
<h4 id="rectangular-ducts">Rectangular Ducts<a class="headerlink" href="#rectangular-ducts" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Width × Height</strong>: Optimized dimensions in inches or millimeters</li>
<li><strong>Aspect Ratio</strong>: Typically between 1:1 and 4:1 for efficiency</li>
<li><strong>Standard Sizes</strong>: Rounded to nearest standard dimensions</li>
</ul>
<h4 id="round-ducts">Round Ducts<a class="headerlink" href="#round-ducts" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Diameter</strong>: Optimal diameter in inches or millimeters</li>
<li><strong>Standard Sizes</strong>: Rounded to nearest standard pipe size</li>
</ul>
<h3 id="performance-metrics">Performance Metrics<a class="headerlink" href="#performance-metrics" title="Permanent link">&para;</a></h3>
<h4 id="velocity">Velocity<a class="headerlink" href="#velocity" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Value</strong>: Air velocity in FPM (feet per minute) or m/s (meters per second)</li>
<li><strong>SMACNA Limits</strong>: </li>
<li>Supply ducts: 1000-2500 FPM (5-13 m/s)</li>
<li>Return ducts: 800-1500 FPM (4-8 m/s)</li>
<li><strong>Status</strong>: Pass/Fail indication for velocity compliance</li>
</ul>
<h4 id="pressure-loss">Pressure Loss<a class="headerlink" href="#pressure-loss" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Value</strong>: Friction loss per unit length</li>
<li><strong>Units</strong>: in. w.g./100 ft or Pa/m</li>
<li><strong>Calculation</strong>: Based on Darcy-Weisbach equation with duct-specific factors</li>
</ul>
<h4 id="cross-sectional-area">Cross-Sectional Area<a class="headerlink" href="#cross-sectional-area" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Value</strong>: Internal duct area</li>
<li><strong>Units</strong>: sq. ft or sq. m</li>
<li><strong>Usage</strong>: For airflow density calculations</li>
</ul>
<h4 id="equivalent-diameter">Equivalent Diameter<a class="headerlink" href="#equivalent-diameter" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Rectangular Ducts</strong>: Hydraulic diameter for pressure loss calculations</li>
<li><strong>Round Ducts</strong>: Same as actual diameter</li>
<li><strong>Formula</strong>: De = 1.3 × (a × b)^0.625 / (a + b)^0.25</li>
</ul>
<h2 id="standards-compliance">Standards Compliance<a class="headerlink" href="#standards-compliance" title="Permanent link">&para;</a></h2>
<h3 id="smacna-standards">SMACNA Standards<a class="headerlink" href="#smacna-standards" title="Permanent link">&para;</a></h3>
<p>The Air Duct Sizer validates designs against SMACNA (Sheet Metal and Air Conditioning Contractors' National Association) standards:</p>
<h4 id="velocity-limits">Velocity Limits<a class="headerlink" href="#velocity-limits" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Supply Air</strong>: Maximum 2500 FPM (13 m/s) for noise control</li>
<li><strong>Return Air</strong>: Maximum 1500 FPM (8 m/s) for energy efficiency</li>
<li><strong>Exhaust Air</strong>: Maximum 2000 FPM (10 m/s) depending on application</li>
</ul>
<h4 id="pressure-classifications">Pressure Classifications<a class="headerlink" href="#pressure-classifications" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Low Pressure</strong>: Residential and light commercial</li>
<li><strong>Medium Pressure</strong>: Commercial and industrial</li>
<li><strong>High Pressure</strong>: Industrial and specialized applications</li>
</ul>
<h4 id="construction-standards">Construction Standards<a class="headerlink" href="#construction-standards" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Sealing Requirements</strong>: Based on pressure class and leakage rates</li>
<li><strong>Support Spacing</strong>: Maximum distances between hangers</li>
<li><strong>Joint Types</strong>: Appropriate connection methods for pressure class</li>
</ul>
<h3 id="validation-warnings">Validation Warnings<a class="headerlink" href="#validation-warnings" title="Permanent link">&para;</a></h3>
<p>The system provides warnings for:</p>
<ul>
<li><strong>Very Low Airflow</strong>: Below 25 CFM (12 L/s) - verify requirements</li>
<li><strong>Very High Airflow</strong>: Above 50,000 CFM (24,000 L/s) - consider multiple ducts</li>
<li><strong>Low Friction Rate</strong>: Below 0.05 in. w.g./100 ft - may result in oversized ducts</li>
<li><strong>High Friction Rate</strong>: Above 0.5 in. w.g./100 ft - may result in undersized ducts</li>
<li><strong>Velocity Exceedance</strong>: Above SMACNA recommended limits</li>
</ul>
<h2 id="best-practices">Best Practices<a class="headerlink" href="#best-practices" title="Permanent link">&para;</a></h2>
<h3 id="friction-rate-selection">Friction Rate Selection<a class="headerlink" href="#friction-rate-selection" title="Permanent link">&para;</a></h3>
<h4 id="low-friction-005-008-in-wg100-ft">Low Friction (0.05-0.08 in. w.g./100 ft)<a class="headerlink" href="#low-friction-005-008-in-wg100-ft" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Pros</strong>: Lower energy costs, quieter operation</li>
<li><strong>Cons</strong>: Larger ducts, higher material costs</li>
<li><strong>Use</strong>: Long duct runs, energy-efficient designs</li>
</ul>
<h4 id="medium-friction-008-015-in-wg100-ft">Medium Friction (0.08-0.15 in. w.g./100 ft)<a class="headerlink" href="#medium-friction-008-015-in-wg100-ft" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Pros</strong>: Balanced approach, standard practice</li>
<li><strong>Cons</strong>: Moderate energy and material costs</li>
<li><strong>Use</strong>: Most commercial applications</li>
</ul>
<h4 id="high-friction-015-05-in-wg100-ft">High Friction (0.15-0.5 in. w.g./100 ft)<a class="headerlink" href="#high-friction-015-05-in-wg100-ft" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Pros</strong>: Smaller ducts, lower material costs</li>
<li><strong>Cons</strong>: Higher energy costs, potential noise issues</li>
<li><strong>Use</strong>: Short runs, space-constrained applications</li>
</ul>
<h3 id="duct-type-selection">Duct Type Selection<a class="headerlink" href="#duct-type-selection" title="Permanent link">&para;</a></h3>
<h4 id="rectangular-ducts_1">Rectangular Ducts<a class="headerlink" href="#rectangular-ducts_1" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Advantages</strong>: Fits in tight spaces, easier to insulate</li>
<li><strong>Disadvantages</strong>: Higher pressure loss, more complex fabrication</li>
<li><strong>Best For</strong>: Space-constrained installations, architectural integration</li>
</ul>
<h4 id="round-ducts_1">Round Ducts<a class="headerlink" href="#round-ducts_1" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Advantages</strong>: Lower pressure loss, easier fabrication, better airflow</li>
<li><strong>Disadvantages</strong>: Requires more space, harder to conceal</li>
<li><strong>Best For</strong>: Exposed installations, energy efficiency priority</li>
</ul>
<h2 id="troubleshooting">Troubleshooting<a class="headerlink" href="#troubleshooting" title="Permanent link">&para;</a></h2>
<h3 id="common-issues">Common Issues<a class="headerlink" href="#common-issues" title="Permanent link">&para;</a></h3>
<h4 id="velocity-too-high-warning">"Velocity Too High" Warning<a class="headerlink" href="#velocity-too-high-warning" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Cause</strong>: Friction rate too high or airflow too high for duct size</li>
<li><strong>Solution</strong>: Reduce friction rate or consider larger duct/multiple ducts</li>
</ul>
<h4 id="unrealistic-dimensions-error">"Unrealistic Dimensions" Error<a class="headerlink" href="#unrealistic-dimensions-error" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Cause</strong>: Input parameters result in impractical duct sizes</li>
<li><strong>Solution</strong>: Adjust friction rate or verify airflow requirements</li>
</ul>
<h4 id="standards-violation-warning">"Standards Violation" Warning<a class="headerlink" href="#standards-violation-warning" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Cause</strong>: Design exceeds SMACNA recommended limits</li>
<li><strong>Solution</strong>: Review design parameters and adjust as needed</li>
</ul>
<h3 id="validation-errors">Validation Errors<a class="headerlink" href="#validation-errors" title="Permanent link">&para;</a></h3>
<h4 id="missing-required-fields">Missing Required Fields<a class="headerlink" href="#missing-required-fields" title="Permanent link">&para;</a></h4>
<ul>
<li>Ensure all required inputs are provided</li>
<li>Check for valid numeric values</li>
</ul>
<h4 id="out-of-range-values">Out of Range Values<a class="headerlink" href="#out-of-range-values" title="Permanent link">&para;</a></h4>
<ul>
<li>Verify airflow is within supported range</li>
<li>Check friction rate is reasonable for application</li>
</ul>
<h4 id="invalid-combinations">Invalid Combinations<a class="headerlink" href="#invalid-combinations" title="Permanent link">&para;</a></h4>
<ul>
<li>Some parameter combinations may not be physically realizable</li>
<li>Adjust inputs to achieve practical results</li>
</ul>
<h2 id="advanced-features">Advanced Features<a class="headerlink" href="#advanced-features" title="Permanent link">&para;</a></h2>
<h3 id="material-properties">Material Properties<a class="headerlink" href="#material-properties" title="Permanent link">&para;</a></h3>
<p>Different duct materials have varying roughness factors that affect pressure loss:</p>
<ul>
<li><strong>Galvanized Steel</strong>: Roughness factor 0.0003 ft</li>
<li><strong>Aluminum</strong>: Roughness factor 0.0002 ft</li>
<li><strong>Stainless Steel</strong>: Roughness factor 0.0002 ft</li>
<li><strong>Flexible Duct</strong>: Roughness factor 0.003 ft (much higher)</li>
</ul>
<h3 id="pressure-loss-calculations">Pressure Loss Calculations<a class="headerlink" href="#pressure-loss-calculations" title="Permanent link">&para;</a></h3>
<p>The system uses the Darcy-Weisbach equation with duct-specific modifications:</p>
<div class="highlight"><pre><span></span><code><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a>ΔP = f × (L/D) × (ρ × V²/2)
</code></pre></div>
<p>Where:
- ΔP = Pressure loss
- f = Friction factor (based on Reynolds number and roughness)
- L = Duct length
- D = Hydraulic diameter
- ρ = Air density
- V = Air velocity</p>
<h3 id="standard-sizes">Standard Sizes<a class="headerlink" href="#standard-sizes" title="Permanent link">&para;</a></h3>
<p>The calculator rounds results to standard duct sizes:</p>
<h4 id="round-ducts-inches">Round Ducts (inches)<a class="headerlink" href="#round-ducts-inches" title="Permanent link">&para;</a></h4>
<p>4, 5, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36</p>
<h4 id="rectangular-ducts_2">Rectangular Ducts<a class="headerlink" href="#rectangular-ducts_2" title="Permanent link">&para;</a></h4>
<p>Standard increments based on SMACNA guidelines, typically in 2-inch increments for smaller sizes and 4-inch increments for larger sizes.</p>
<h2 id="integration-with-projects">Integration with Projects<a class="headerlink" href="#integration-with-projects" title="Permanent link">&para;</a></h2>
<h3 id="saving-calculations">Saving Calculations<a class="headerlink" href="#saving-calculations" title="Permanent link">&para;</a></h3>
<p>All calculations are automatically saved to your current project (if one is active) or as standalone calculations. Saved data includes:</p>
<ul>
<li>Input parameters</li>
<li>Calculated results</li>
<li>Compliance status</li>
<li>Timestamp and metadata</li>
</ul>
<h3 id="exporting-results">Exporting Results<a class="headerlink" href="#exporting-results" title="Permanent link">&para;</a></h3>
<p>Results can be exported in various formats:</p>
<ul>
<li><strong>PDF Report</strong>: Formatted calculation summary</li>
<li><strong>CSV Data</strong>: Raw data for spreadsheet analysis</li>
<li><strong>JSON</strong>: Machine-readable format for integration</li>
</ul>
<h3 id="project-management">Project Management<a class="headerlink" href="#project-management" title="Permanent link">&para;</a></h3>
<p>Organize your calculations by:</p>
<ul>
<li><strong>Creating projects</strong> for different buildings or systems</li>
<li><strong>Grouping calculations</strong> by system type or zone</li>
<li><strong>Adding notes</strong> and descriptions for future reference</li>
<li><strong>Comparing alternatives</strong> side-by-side</li>
</ul>
<h2 id="next-steps">Next Steps<a class="headerlink" href="#next-steps" title="Permanent link">&para;</a></h2>
<ul>
<li><strong><a href="project-management.md">Project Management Guide</a></strong>: Learn to organize your work</li>
<li><strong><a href="units-standards.md">Units and Standards</a></strong>: Understand unit conversions and standards</li>
<li><strong><a href="../../api/air-duct-calculator/">API Reference</a></strong>: Technical integration details</li>
<li><strong><a href="../examples/basic-calculations.md">Examples</a></strong>: Practical calculation examples</li>
</ul>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
          <button type="button" class="md-top md-icon" data-md-component="top" hidden>
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  Back to top
</button>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
    <div class="md-copyright__highlight">
      Copyright &copy; 2024 SizeWise Suite Team
    </div>
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
        <div class="md-social">
  
    
    
    
    
      
      
    
    <a href="https://github.com/sizewise-suite/sizewise-suite" target="_blank" rel="noopener" title="github.com" class="md-social__link">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
    </a>
  
    
    
    
    
      
      
    
    <a href="https://sizewise-suite.github.io/" target="_blank" rel="noopener" title="sizewise-suite.github.io" class="md-social__link">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M352 256c0 22.2-1.2 43.6-3.3 64H163.4c-2.2-20.4-3.3-41.8-3.3-64s1.2-43.6 3.3-64h185.3c2.2 20.4 3.3 41.8 3.3 64m28.8-64h123.1c5.3 20.5 8.1 41.9 8.1 64s-2.8 43.5-8.1 64H380.8c2.1-20.6 3.2-42 3.2-64s-1.1-43.4-3.2-64m112.6-32H376.7c-10-63.9-29.8-117.4-55.3-151.6 78.3 20.7 142 77.5 171.9 151.6zm-149.1 0H167.7c6.1-36.4 15.5-68.6 27-94.7 10.5-23.6 22.2-40.7 33.5-51.5C239.4 3.2 248.7 0 256 0s16.6 3.2 27.8 13.8c11.3 10.8 23 27.9 33.5 51.5 11.6 26 20.9 58.2 27 94.7m-209 0H18.6c30-74.1 93.6-130.9 172-151.6-25.5 34.2-45.3 87.7-55.3 151.6M8.1 192h123.1c-2.1 20.6-3.2 42-3.2 64s1.1 43.4 3.2 64H8.1C2.8 299.5 0 278.1 0 256s2.8-43.5 8.1-64m186.6 254.6c-11.6-26-20.9-58.2-27-94.6h176.6c-6.1 36.4-15.5 68.6-27 94.6-10.5 23.6-22.2 40.7-33.5 51.5-11.2 10.7-20.5 13.9-27.8 13.9s-16.6-3.2-27.8-13.8c-11.3-10.8-23-27.9-33.5-51.5zM135.3 352c10 63.9 29.8 117.4 55.3 151.6-78.4-20.7-142-77.5-172-151.6zm358.1 0c-30 74.1-93.6 130.9-171.9 151.6 25.5-34.2 45.2-87.7 55.3-151.6h116.7z"/></svg>
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": ["navigation.tabs", "navigation.sections", "navigation.expand", "navigation.top", "search.highlight", "search.share", "content.code.copy", "content.action.edit", "content.action.view"], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": {"provider": "mike"}}</script>
    
    
      <script src="../../assets/javascripts/bundle.56ea9cef.min.js"></script>
      
    
  </body>
</html>