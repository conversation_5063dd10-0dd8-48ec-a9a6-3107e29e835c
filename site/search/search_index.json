{"config": {"lang": ["en"], "separator": "[\\s\\-]+", "pipeline": ["stop<PERSON>ordFilter"]}, "docs": [{"location": "", "title": "SizeWise Suite Documentation", "text": "<p>Welcome to SizeWise Suite, a comprehensive modular HVAC engineering and estimating platform designed for mechanical engineers, estimators, QA professionals, and project managers.</p>"}, {"location": "#what-is-sizewise-suite", "title": "What is SizeWise Suite?", "text": "<p>SizeWise Suite is an offline-first, modular platform that unifies duct sizing, vent design, and cost estimating in a single workspace. Built with modern web technologies, it provides:</p> <ul> <li>Standards Compliance: Full compliance with SMACNA, NFPA, and ASHRAE standards</li> <li>Offline-First Design: Work anywhere, anytime without internet connectivity</li> <li>Bidirectional Unit Conversion: Seamless Imperial/SI unit support</li> <li>Modular Architecture: Extensible design for future HVAC modules</li> <li>Progressive Web App: Install and use like a native application</li> </ul>"}, {"location": "#core-modules", "title": "<PERSON>", "text": ""}, {"location": "#air-duct-sizer", "title": "🌬️ Air Duct Sizer", "text": "<p>Calculate optimal duct sizes for HVAC systems with SMACNA compliance checking, friction loss analysis, and velocity validation.</p>"}, {"location": "#grease-duct-sizer-coming-soon", "title": "🔥 Grease Duct <PERSON> (Coming Soon)", "text": "<p>Design grease exhaust systems with NFPA 96 compliance and fire safety considerations.</p>"}, {"location": "#engine-exhaust-sizer-coming-soon", "title": "🚗 Engine Exhaust Sizer (Coming Soon)", "text": "<p>Size engine exhaust systems for parking garages and mechanical rooms.</p>"}, {"location": "#boiler-vent-sizer-coming-soon", "title": "🔥 <PERSON><PERSON> (Coming Soon)", "text": "<p>Calculate boiler vent requirements with code compliance checking.</p>"}, {"location": "#estimating-app-coming-soon", "title": "💰 Estimating App (Coming Soon)", "text": "<p>Generate accurate cost estimates for HVAC projects with material and labor calculations.</p>"}, {"location": "#key-features", "title": "Key Features", "text": ""}, {"location": "#standards-compliance", "title": "✅ Standards Compliance", "text": "<ul> <li>SMACNA: Sheet Metal and Air Conditioning Contractors' National Association standards</li> <li>NFPA: National Fire Protection Association codes</li> <li>ASHRAE: American Society of Heating, Refrigerating and Air-Conditioning Engineers standards</li> </ul>"}, {"location": "#unit-conversion", "title": "🔄 Unit Conversion", "text": "<ul> <li>Seamless conversion between Imperial and Metric units</li> <li>Temperature, pressure, flow rate, and dimensional conversions</li> <li>Automatic unit detection and validation</li> </ul>"}, {"location": "#offline-functionality", "title": "💾 Offline Functionality", "text": "<ul> <li>Complete offline operation with IndexedDB storage</li> <li>Project and calculation persistence</li> <li>Progressive Web App (PWA) capabilities</li> <li>No internet required for core functionality</li> </ul>"}, {"location": "#modern-interface", "title": "📱 Modern Interface", "text": "<ul> <li>Responsive design for desktop, tablet, and mobile</li> <li>Intuitive user interface with real-time validation</li> <li>Dark/light mode support</li> <li>Accessibility compliant</li> </ul>"}, {"location": "#quick-start", "title": "Quick Start", "text": "<ol> <li>Install SizeWise Suite on your device</li> <li>Follow the Quick Start Guide to perform your first calculation</li> <li>Explore the User Guide for detailed feature documentation</li> </ol>"}, {"location": "#architecture-overview", "title": "Architecture Overview", "text": "graph TB     A[Frontend - Modular JavaScript] --&gt; B[Core Services]     B --&gt; C[Storage Manager - IndexedDB]     B --&gt; D[API Client]     B --&gt; E[Units Manager]     B --&gt; F[UI Manager]      D --&gt; G[Backend - Flask API]     G --&gt; H[Calculation Engine]     G --&gt; I[Validation System]     G --&gt; J[Standards Compliance]      H --&gt; K[Air Duct Calculator]     H --&gt; L[Future Modules]      I --&gt; M[Schema Validation]     I --&gt; N[Input Validation]      J --&gt; O[SMACNA Standards]     J --&gt; P[NFPA Codes]     J --&gt; Q[ASHRAE Standards]"}, {"location": "#technology-stack", "title": "Technology Stack", "text": "<ul> <li>Frontend: Modular JavaScript, Progressive Web App (PWA)</li> <li>Backend: Python Flask with comprehensive calculation engines</li> <li>Storage: IndexedDB for offline data persistence</li> <li>Validation: Pydantic schemas with JSONSchema validation</li> <li>Testing: Jest (frontend) + pytest (backend)</li> <li>Documentation: MkDocs with Material theme</li> </ul>"}, {"location": "#getting-help", "title": "Getting Help", "text": "<ul> <li>User Guide: Comprehensive usage documentation</li> <li>API Reference: Technical API documentation</li> <li>Developer Guide: Architecture and development information</li> <li>Examples: Practical usage examples</li> </ul>"}, {"location": "#contributing", "title": "Contributing", "text": "<p>SizeWise Suite is designed to be extensible and welcomes contributions. See our Contributing Guide for information on:</p> <ul> <li>Adding new calculation modules</li> <li>Improving existing functionality</li> <li>Reporting bugs and requesting features</li> <li>Development setup and guidelines</li> </ul>"}, {"location": "#license", "title": "License", "text": "<p>SizeWise Suite is released under the MIT License. See the LICENSE file for details.</p> <p>Built with ❤️ for the HVAC engineering community</p>"}, {"location": "api/air-duct-calculator/", "title": "Air Duct Calculator API", "text": "<p>The Air Duct Calculator API provides programmatic access to HVAC duct sizing calculations with SMACNA standards compliance.</p>"}, {"location": "api/air-duct-calculator/#base-url", "title": "Base URL", "text": "<pre><code>http://localhost:5000/api/calculations/air-duct\n</code></pre>"}, {"location": "api/air-duct-calculator/#authentication", "title": "Authentication", "text": "<p>Currently, no authentication is required for the API. This may change in future versions.</p>"}, {"location": "api/air-duct-calculator/#endpoints", "title": "Endpoints", "text": ""}, {"location": "api/air-duct-calculator/#calculate-duct-size", "title": "Calculate Duct Size", "text": "<p>Calculate optimal duct dimensions for given parameters.</p>"}, {"location": "api/air-duct-calculator/#request", "title": "Request", "text": "<pre><code>POST /api/calculations/air-duct\nContent-Type: application/json\n</code></pre>"}, {"location": "api/air-duct-calculator/#request-body", "title": "Request Body", "text": "<pre><code>{\n  \"airflow\": 1000,\n  \"duct_type\": \"rectangular\",\n  \"friction_rate\": 0.08,\n  \"units\": \"imperial\",\n  \"material\": \"galvanized_steel\",\n  \"pressure_class\": \"low\"\n}\n</code></pre>"}, {"location": "api/air-duct-calculator/#parameters", "title": "Parameters", "text": "Parameter Type Required Description <code>airflow</code> number Yes Airflow rate (CFM for imperial, L/s for metric) <code>duct_type</code> string Yes \"rectangular\" or \"round\" <code>friction_rate</code> number Yes Friction rate (in. w.g./100 ft for imperial, Pa/m for metric) <code>units</code> string Yes \"imperial\" or \"metric\" <code>material</code> string No Duct material type (default: \"galvanized_steel\") <code>pressure_class</code> string No \"low\", \"medium\", or \"high\" (default: \"low\")"}, {"location": "api/air-duct-calculator/#response", "title": "Response", "text": "<pre><code>{\n  \"success\": true,\n  \"input_data\": {\n    \"airflow\": 1000,\n    \"duct_type\": \"rectangular\",\n    \"friction_rate\": 0.08,\n    \"units\": \"imperial\"\n  },\n  \"results\": {\n    \"duct_size\": \"16\\\" x 6\\\"\",\n    \"width\": {\n      \"value\": 16.0,\n      \"unit\": \"in\"\n    },\n    \"height\": {\n      \"value\": 6.0,\n      \"unit\": \"in\"\n    },\n    \"area\": {\n      \"value\": 0.67,\n      \"unit\": \"sq_ft\"\n    },\n    \"velocity\": {\n      \"value\": 1500.0,\n      \"unit\": \"fpm\"\n    },\n    \"equivalent_diameter\": {\n      \"value\": 10.41,\n      \"unit\": \"in\"\n    },\n    \"pressure_loss\": {\n      \"value\": 1002.59,\n      \"unit\": \"in_wg_per_100ft\"\n    }\n  },\n  \"compliance\": {\n    \"smacna\": {\n      \"velocity\": {\n        \"passed\": true,\n        \"value\": 1500.0,\n        \"limit\": 2500,\n        \"message\": \"Velocity within SMACNA limits\"\n      }\n    }\n  },\n  \"warnings\": [],\n  \"errors\": [],\n  \"metadata\": {\n    \"calculated_at\": \"2024-01-15T10:30:00.000Z\",\n    \"version\": \"0.1.0\"\n  }\n}\n</code></pre>"}, {"location": "api/air-duct-calculator/#error-response", "title": "Error Response", "text": "<pre><code>{\n  \"success\": false,\n  \"errors\": [\n    \"Airflow must be a positive number\",\n    \"Duct type must be 'rectangular' or 'round'\"\n  ],\n  \"warnings\": []\n}\n</code></pre>"}, {"location": "api/air-duct-calculator/#validate-input", "title": "Validate Input", "text": "<p>Validate input parameters without performing calculation.</p>"}, {"location": "api/air-duct-calculator/#request_1", "title": "Request", "text": "<pre><code>POST /api/calculations/air-duct/validate\nContent-Type: application/json\n</code></pre>"}, {"location": "api/air-duct-calculator/#request-body_1", "title": "Request Body", "text": "<pre><code>{\n  \"airflow\": 25,\n  \"duct_type\": \"rectangular\",\n  \"friction_rate\": 0.08,\n  \"units\": \"imperial\"\n}\n</code></pre>"}, {"location": "api/air-duct-calculator/#response_1", "title": "Response", "text": "<pre><code>{\n  \"is_valid\": true,\n  \"errors\": [],\n  \"warnings\": [\n    \"Very low airflow - verify this is correct\"\n  ]\n}\n</code></pre>"}, {"location": "api/air-duct-calculator/#get-standard-sizes", "title": "Get Standard Sizes", "text": "<p>Retrieve standard duct sizes for a given type.</p>"}, {"location": "api/air-duct-calculator/#request_2", "title": "Request", "text": "<pre><code>GET /api/calculations/air-duct/standard-sizes/{duct_type}\n</code></pre>"}, {"location": "api/air-duct-calculator/#parameters_1", "title": "Parameters", "text": "Parameter Type Required Description <code>duct_type</code> string Yes \"rectangular\" or \"round\""}, {"location": "api/air-duct-calculator/#response_2", "title": "Response", "text": "<pre><code>{\n  \"success\": true,\n  \"duct_type\": \"round\",\n  \"sizes\": [4, 5, 6, 8, 10, 12, 14, 16, 18, 20, 24, 26, 28, 30, 32, 34, 36]\n}\n</code></pre>"}, {"location": "api/air-duct-calculator/#get-materials", "title": "Get Materials", "text": "<p>Retrieve available duct materials and their properties.</p>"}, {"location": "api/air-duct-calculator/#request_3", "title": "Request", "text": "<pre><code>GET /api/calculations/air-duct/materials\n</code></pre>"}, {"location": "api/air-duct-calculator/#response_3", "title": "Response", "text": "<pre><code>{\n  \"success\": true,\n  \"materials\": {\n    \"galvanized_steel\": {\n      \"name\": \"Galvanized Steel\",\n      \"roughness\": 0.0003,\n      \"description\": \"Standard galvanized steel ductwork\"\n    },\n    \"aluminum\": {\n      \"name\": \"Aluminum\",\n      \"roughness\": 0.0002,\n      \"description\": \"Lightweight aluminum ductwork\"\n    },\n    \"stainless_steel\": {\n      \"name\": \"Stainless Steel\",\n      \"roughness\": 0.0002,\n      \"description\": \"Corrosion-resistant stainless steel\"\n    },\n    \"flexible\": {\n      \"name\": \"Flexible Duct\",\n      \"roughness\": 0.003,\n      \"description\": \"Flexible ductwork for short runs\"\n    }\n  }\n}\n</code></pre>"}, {"location": "api/air-duct-calculator/#data-models", "title": "Data Models", "text": ""}, {"location": "api/air-duct-calculator/#input-data-model", "title": "Input Data Model", "text": "<pre><code>interface AirDuctInput {\n  airflow: number;           // CFM (imperial) or L/s (metric)\n  duct_type: \"rectangular\" | \"round\";\n  friction_rate: number;     // in. w.g./100 ft (imperial) or Pa/m (metric)\n  units: \"imperial\" | \"metric\";\n  material?: string;         // Optional, default: \"galvanized_steel\"\n  pressure_class?: \"low\" | \"medium\" | \"high\"; // Optional, default: \"low\"\n}\n</code></pre>"}, {"location": "api/air-duct-calculator/#result-data-model", "title": "Result Data Model", "text": "<pre><code>interface AirDuctResult {\n  duct_size: string;         // Human-readable size description\n  area: ValueWithUnit;       // Cross-sectional area\n  velocity: ValueWithUnit;   // Air velocity\n  equivalent_diameter: ValueWithUnit; // Hydraulic diameter\n  pressure_loss: ValueWithUnit; // Friction loss per unit length\n\n  // Rectangular ducts only\n  width?: ValueWithUnit;\n  height?: ValueWithUnit;\n\n  // Round ducts only\n  diameter?: ValueWithUnit;\n}\n\ninterface ValueWithUnit {\n  value: number;\n  unit: string;\n}\n</code></pre>"}, {"location": "api/air-duct-calculator/#compliance-data-model", "title": "Compliance Data Model", "text": "<pre><code>interface ComplianceResult {\n  smacna: {\n    velocity: {\n      passed: boolean;\n      value: number;\n      limit: number;\n      message: string;\n    };\n  };\n}\n</code></pre>"}, {"location": "api/air-duct-calculator/#error-handling", "title": "Erro<PERSON>", "text": ""}, {"location": "api/air-duct-calculator/#http-status-codes", "title": "HTTP Status Codes", "text": "Code Description 200 Success 400 Bad Request - Invalid input parameters 422 Unprocessable Entity - Validation errors 500 Internal Server Error"}, {"location": "api/air-duct-calculator/#error-types", "title": "Error Types", "text": ""}, {"location": "api/air-duct-calculator/#validation-errors", "title": "Validation Errors", "text": "<ul> <li>Missing Required Field: Required parameter not provided</li> <li>Invalid Type: Parameter type doesn't match expected type</li> <li>Out of Range: Parameter value outside acceptable range</li> <li>Invalid Combination: Parameter combination is not valid</li> </ul>"}, {"location": "api/air-duct-calculator/#calculation-errors", "title": "Calculation Errors", "text": "<ul> <li>Calculation Failed: Internal calculation error</li> <li>Unrealistic Result: Calculation produces impractical results</li> <li>Standards Violation: Result violates industry standards</li> </ul>"}, {"location": "api/air-duct-calculator/#rate-limiting", "title": "Rate Limiting", "text": "<p>Currently, no rate limiting is implemented. This may be added in future versions.</p>"}, {"location": "api/air-duct-calculator/#versioning", "title": "Versioning", "text": "<p>The API uses semantic versioning. The current version is included in all responses under <code>metadata.version</code>.</p>"}, {"location": "api/air-duct-calculator/#examples", "title": "Examples", "text": ""}, {"location": "api/air-duct-calculator/#basic-rectangular-duct-calculation", "title": "Basic Rectangular Duct Calculation", "text": "<pre><code>curl -X POST http://localhost:5000/api/calculations/air-duct \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"airflow\": 1000,\n    \"duct_type\": \"rectangular\",\n    \"friction_rate\": 0.08,\n    \"units\": \"imperial\"\n  }'\n</code></pre>"}, {"location": "api/air-duct-calculator/#round-duct-with-high-friction", "title": "Round Duct with High Friction", "text": "<pre><code>curl -X POST http://localhost:5000/api/calculations/air-duct \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"airflow\": 2000,\n    \"duct_type\": \"round\",\n    \"friction_rate\": 0.2,\n    \"units\": \"imperial\",\n    \"material\": \"stainless_steel\"\n  }'\n</code></pre>"}, {"location": "api/air-duct-calculator/#metric-units-calculation", "title": "Metric Units Calculation", "text": "<pre><code>curl -X POST http://localhost:5000/api/calculations/air-duct \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"airflow\": 500,\n    \"duct_type\": \"rectangular\",\n    \"friction_rate\": 1.0,\n    \"units\": \"metric\"\n  }'\n</code></pre>"}, {"location": "api/air-duct-calculator/#input-validation", "title": "Input Validation", "text": "<pre><code>curl -X POST http://localhost:5000/api/calculations/air-duct/validate \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"airflow\": 25,\n    \"duct_type\": \"rectangular\",\n    \"friction_rate\": 0.08,\n    \"units\": \"imperial\"\n  }'\n</code></pre>"}, {"location": "api/air-duct-calculator/#integration-examples", "title": "Integration Examples", "text": ""}, {"location": "api/air-duct-calculator/#javascripttypescript", "title": "JavaScript/TypeScript", "text": "<pre><code>class AirDuctCalculator {\n  private baseUrl = 'http://localhost:5000/api/calculations/air-duct';\n\n  async calculate(input: AirDuctInput): Promise&lt;AirDuctResponse&gt; {\n    const response = await fetch(this.baseUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(input),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  }\n\n  async validate(input: AirDuctInput): Promise&lt;ValidationResult&gt; {\n    const response = await fetch(`${this.baseUrl}/validate`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(input),\n    });\n\n    return await response.json();\n  }\n}\n</code></pre>"}, {"location": "api/air-duct-calculator/#python", "title": "Python", "text": "<pre><code>import requests\nfrom typing import Dict, Any\n\nclass AirDuctCalculator:\n    def __init__(self, base_url: str = \"http://localhost:5000/api/calculations/air-duct\"):\n        self.base_url = base_url\n\n    def calculate(self, input_data: Dict[str, Any]) -&gt; Dict[str, Any]:\n        response = requests.post(\n            self.base_url,\n            json=input_data,\n            headers={\"Content-Type\": \"application/json\"}\n        )\n        response.raise_for_status()\n        return response.json()\n\n    def validate(self, input_data: Dict[str, Any]) -&gt; Dict[str, Any]:\n        response = requests.post(\n            f\"{self.base_url}/validate\",\n            json=input_data,\n            headers={\"Content-Type\": \"application/json\"}\n        )\n        return response.json()\n\n# Usage example\ncalculator = AirDuctCalculator()\nresult = calculator.calculate({\n    \"airflow\": 1000,\n    \"duct_type\": \"rectangular\",\n    \"friction_rate\": 0.08,\n    \"units\": \"imperial\"\n})\n</code></pre>"}, {"location": "api/air-duct-calculator/#changelog", "title": "Changelog", "text": ""}, {"location": "api/air-duct-calculator/#version-010", "title": "Version 0.1.0", "text": "<ul> <li>Initial API implementation</li> <li>Basic duct sizing calculations</li> <li>SMACNA standards compliance</li> <li>Input validation</li> <li>Standard sizes and materials endpoints</li> </ul>"}, {"location": "getting-started/installation/", "title": "Installation Guide", "text": "<p>SizeWise Suite can be installed and used in multiple ways, depending on your needs and environment.</p>"}, {"location": "getting-started/installation/#web-application-recommended", "title": "Web Application (Recommended)", "text": "<p>The easiest way to use SizeWise Suite is through the web application, which works in any modern browser.</p>"}, {"location": "getting-started/installation/#prerequisites", "title": "Prerequisites", "text": "<ul> <li>Modern web browser (Chrome 90+, Firefox 88+, Safari 14+, Edge 90+)</li> <li>JavaScript enabled</li> <li>Minimum 100MB available storage for offline data</li> </ul>"}, {"location": "getting-started/installation/#access-the-application", "title": "Access the Application", "text": "<ol> <li>Open your web browser</li> <li>Navigate to the application URL (when deployed)</li> <li>Allow the application to install as a Progressive Web App (PWA) when prompted</li> </ol>"}, {"location": "getting-started/installation/#progressive-web-app-installation", "title": "Progressive Web App Installation", "text": "<p>SizeWise Suite can be installed as a PWA for a native app-like experience:</p>"}, {"location": "getting-started/installation/#desktop-installation", "title": "Desktop Installation", "text": "<ol> <li>Chrome/Edge: Click the install icon in the address bar or use the \"Install SizeWise Suite\" option in the menu</li> <li>Firefox: Use the \"Install\" option in the address bar</li> <li>Safari: Use \"Add to Dock\" from the File menu</li> </ol>"}, {"location": "getting-started/installation/#mobile-installation", "title": "Mobile Installation", "text": "<ol> <li>Chrome (Android): Tap \"Add to Home Screen\" from the browser menu</li> <li>Safari (iOS): Tap the share button and select \"Add to Home Screen\"</li> </ol>"}, {"location": "getting-started/installation/#local-development-setup", "title": "Local Development Setup", "text": "<p>For developers or users who want to run SizeWise Suite locally:</p>"}, {"location": "getting-started/installation/#prerequisites_1", "title": "Prerequisites", "text": "<ul> <li>Python 3.9+ with pip</li> <li>Node.js 16+ with npm</li> <li>Git for version control</li> </ul>"}, {"location": "getting-started/installation/#installation-steps", "title": "Installation Steps", "text": "<ol> <li> <p>Clone the repository <pre><code>git clone https://github.com/sizewise-suite/sizewise-suite.git\ncd sizewise-suite\n</code></pre></p> </li> <li> <p>Set up Python environment <pre><code>python3 -m venv venv\nsource venv/bin/activate  # On Windows: venv\\Scripts\\activate\npip install -r requirements.txt\n</code></pre></p> </li> <li> <p>Install Node.js dependencies <pre><code>npm install\n</code></pre></p> </li> <li> <p>Start the backend server <pre><code>npm run start:backend\n# Or manually: python run_backend.py\n</code></pre></p> </li> <li> <p>Start the frontend development server <pre><code>npm run dev\n</code></pre></p> </li> <li> <p>Access the application</p> </li> <li>Frontend: http://localhost:3000</li> <li>Backend API: http://localhost:5000</li> </ol>"}, {"location": "getting-started/installation/#production-build", "title": "Production Build", "text": "<p>To build SizeWise Suite for production deployment:</p> <pre><code># Build the frontend\nnpm run build\n\n# The built files will be in the dist/ directory\n# Serve them with any static file server\n</code></pre>"}, {"location": "getting-started/installation/#system-requirements", "title": "System Requirements", "text": ""}, {"location": "getting-started/installation/#minimum-requirements", "title": "Minimum Requirements", "text": "<ul> <li>RAM: 2GB available memory</li> <li>Storage: 500MB free disk space</li> <li>Network: Internet connection for initial setup (optional for offline use)</li> <li>Browser: Modern browser with JavaScript and IndexedDB support</li> </ul>"}, {"location": "getting-started/installation/#recommended-requirements", "title": "Recommended Requirements", "text": "<ul> <li>RAM: 4GB+ available memory</li> <li>Storage: 1GB+ free disk space</li> <li>Network: Broadband connection for faster initial loading</li> <li>Display: 1024x768 minimum resolution (responsive design supports all sizes)</li> </ul>"}, {"location": "getting-started/installation/#browser-compatibility", "title": "Browser Compatibility", "text": "<p>SizeWise Suite is tested and supported on:</p> Browser Minimum Version Notes Chrome 90+ Full PWA support Firefox 88+ Full PWA support Safari 14+ PWA support with limitations Edge 90+ Full PWA support Mobile Chrome 90+ Full mobile support Mobile Safari 14+ Full mobile support"}, {"location": "getting-started/installation/#unsupported-browsers", "title": "Unsupported Browsers", "text": "<ul> <li>Internet Explorer (all versions)</li> <li>Chrome &lt; 90</li> <li>Firefox &lt; 88</li> <li>Safari &lt; 14</li> </ul>"}, {"location": "getting-started/installation/#offline-functionality", "title": "Offline Functionality", "text": "<p>SizeWise Suite is designed to work offline after the initial installation:</p>"}, {"location": "getting-started/installation/#what-works-offline", "title": "What Works Offline", "text": "<ul> <li>✅ All calculation modules</li> <li>✅ Project management</li> <li>✅ Data storage and retrieval</li> <li>✅ Standards compliance checking</li> <li>✅ Unit conversions</li> <li>✅ Documentation (if cached)</li> </ul>"}, {"location": "getting-started/installation/#what-requires-internet", "title": "What Requires Internet", "text": "<ul> <li>❌ Initial application download</li> <li>❌ Software updates</li> <li>❌ External documentation links</li> <li>❌ Future cloud sync features</li> </ul>"}, {"location": "getting-started/installation/#troubleshooting", "title": "Troubleshooting", "text": ""}, {"location": "getting-started/installation/#common-issues", "title": "Common Issues", "text": ""}, {"location": "getting-started/installation/#application-wont-load", "title": "Application Won't Load", "text": "<ol> <li>Check browser compatibility - Ensure you're using a supported browser version</li> <li>Clear browser cache - Force refresh with Ctrl+F5 (Cmd+Shift+R on Mac)</li> <li>Check JavaScript - Ensure JavaScript is enabled in your browser</li> <li>Disable extensions - Try disabling browser extensions that might interfere</li> </ol>"}, {"location": "getting-started/installation/#calculations-not-working", "title": "Calculations Not Working", "text": "<ol> <li>Check backend connection - Ensure the backend server is running (local development)</li> <li>Verify input data - Check that all required fields are filled correctly</li> <li>Check browser console - Look for error messages in the developer console</li> </ol>"}, {"location": "getting-started/installation/#data-not-saving", "title": "Data Not Saving", "text": "<ol> <li>Check storage permissions - Ensure the browser allows local storage</li> <li>Clear storage - Try clearing IndexedDB data and restarting</li> <li>Check available space - Ensure sufficient disk space is available</li> </ol>"}, {"location": "getting-started/installation/#pwa-installation-issues", "title": "PWA Installation Issues", "text": "<ol> <li>Use supported browser - PWA installation requires a compatible browser</li> <li>Check HTTPS - PWA requires secure connection (HTTPS or localhost)</li> <li>Clear cache - Clear browser cache and try again</li> </ol>"}, {"location": "getting-started/installation/#getting-help", "title": "Getting Help", "text": "<p>If you encounter issues not covered here:</p> <ol> <li>Check the User Guide for detailed usage information</li> <li>Review API Documentation for technical details</li> <li>Submit an issue on the GitHub repository</li> <li>Contact support through the application's help system</li> </ol>"}, {"location": "getting-started/installation/#next-steps", "title": "Next Steps", "text": "<p>After installation, continue with:</p> <ul> <li>Quick Start Guide - Perform your first calculation</li> <li>User Guide - Learn all features</li> <li>Examples - See practical usage examples</li> </ul>"}, {"location": "user-guide/air-duct-sizer/", "title": "Air Duct Sizer User Guide", "text": "<p>The Air Duct Sizer is SizeWise Suite's flagship module for calculating optimal duct sizes in HVAC systems. It provides SMACNA-compliant calculations with comprehensive validation and standards checking.</p>"}, {"location": "user-guide/air-duct-sizer/#overview", "title": "Overview", "text": "<p>The Air Duct Sizer helps you:</p> <ul> <li>Calculate optimal duct dimensions for given airflow requirements</li> <li>Validate designs against SMACNA standards</li> <li>Analyze pressure losses and system performance</li> <li>Compare rectangular vs. round duct options</li> <li>Ensure velocity compliance with industry standards</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#getting-started", "title": "Getting Started", "text": ""}, {"location": "user-guide/air-duct-sizer/#accessing-the-air-duct-sizer", "title": "Accessing the Air Duct Sizer", "text": "<ol> <li>Open SizeWise Suite in your browser</li> <li>Navigate to the Air Duct Sizer module from the main dashboard</li> <li>Select your preferred units (Imperial or Metric)</li> </ol>"}, {"location": "user-guide/air-duct-sizer/#basic-calculation", "title": "Basic Calculation", "text": "<p>To perform a basic duct sizing calculation:</p> <ol> <li>Enter the airflow rate (CFM for Imperial, L/s for Metric)</li> <li>Select duct type (Rectangular or Round)</li> <li>Set friction rate (in. w.g./100 ft for Imperial, Pa/m for Metric)</li> <li>Click \"Calculate\" to get results</li> </ol>"}, {"location": "user-guide/air-duct-sizer/#input-parameters", "title": "Input Parameters", "text": ""}, {"location": "user-guide/air-duct-sizer/#required-fields", "title": "Required <PERSON>", "text": ""}, {"location": "user-guide/air-duct-sizer/#airflow-rate", "title": "Airflow Rate", "text": "<ul> <li>Imperial: CFM (Cubic Feet per Minute)</li> <li>Metric: L/s (Liters per Second)</li> <li>Range: 25 - 50,000 CFM (12 - 24,000 L/s)</li> <li>Validation: Must be positive number</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#duct-type", "title": "Duct Type", "text": "<ul> <li>Rectangular: Traditional rectangular ductwork</li> <li>Round: Circular ductwork (typically more efficient)</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#friction-rate", "title": "Friction Rate", "text": "<ul> <li>Imperial: in. w.g./100 ft (inches water gauge per 100 feet)</li> <li>Metric: Pa/m (Pascals per meter)</li> <li>Typical Range: 0.05 - 0.5 in. w.g./100 ft (0.4 - 4.0 Pa/m)</li> <li>Recommended: 0.08 - 0.15 in. w.g./100 ft for most applications</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#optional-parameters", "title": "Optional Parameters", "text": ""}, {"location": "user-guide/air-duct-sizer/#material-type", "title": "Material Type", "text": "<ul> <li>Galvanized Steel (default): Standard ductwork material</li> <li>Aluminum: Lightweight alternative</li> <li>Stainless Steel: Corrosion-resistant option</li> <li>Flexible Duct: For short runs and connections</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#system-pressure-class", "title": "System Pressure Class", "text": "<ul> <li>Low Pressure: Up to 2 in. w.g. (500 Pa)</li> <li>Medium Pressure: 2-6 in. w.g. (500-1500 Pa)</li> <li>High Pressure: 6-10 in. w.g. (1500-2500 Pa)</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#understanding-results", "title": "Understanding Results", "text": ""}, {"location": "user-guide/air-duct-sizer/#duct-dimensions", "title": "Duct Dimensions", "text": ""}, {"location": "user-guide/air-duct-sizer/#rectangular-ducts", "title": "Rectangular Ducts", "text": "<ul> <li>Width × Height: Optimized dimensions in inches or millimeters</li> <li>Aspect Ratio: Typically between 1:1 and 4:1 for efficiency</li> <li>Standard Sizes: Rounded to nearest standard dimensions</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#round-ducts", "title": "Round Ducts", "text": "<ul> <li>Diameter: Optimal diameter in inches or millimeters</li> <li>Standard Sizes: Rounded to nearest standard pipe size</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#performance-metrics", "title": "Performance Metrics", "text": ""}, {"location": "user-guide/air-duct-sizer/#velocity", "title": "Velocity", "text": "<ul> <li>Value: Air velocity in FPM (feet per minute) or m/s (meters per second)</li> <li>SMACNA Limits: </li> <li>Supply ducts: 1000-2500 FPM (5-13 m/s)</li> <li>Return ducts: 800-1500 FPM (4-8 m/s)</li> <li>Status: Pass/Fail indication for velocity compliance</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#pressure-loss", "title": "Pressure Loss", "text": "<ul> <li>Value: Friction loss per unit length</li> <li>Units: in. w.g./100 ft or Pa/m</li> <li>Calculation: Based on Darcy<PERSON>bach equation with duct-specific factors</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#cross-sectional-area", "title": "Cross-Sectional Area", "text": "<ul> <li>Value: Internal duct area</li> <li>Units: sq. ft or sq. m</li> <li>Usage: For airflow density calculations</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#equivalent-diameter", "title": "Equivalent Diameter", "text": "<ul> <li>Rectangular Ducts: Hydraulic diameter for pressure loss calculations</li> <li>Round Ducts: Same as actual diameter</li> <li>Formula: De = 1.3 × (a × b)^0.625 / (a + b)^0.25</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#standards-compliance", "title": "Standards Compliance", "text": ""}, {"location": "user-guide/air-duct-sizer/#smacna-standards", "title": "SMACNA Standards", "text": "<p>The Air Duct Sizer validates designs against SMACNA (Sheet Metal and Air Conditioning Contractors' National Association) standards:</p>"}, {"location": "user-guide/air-duct-sizer/#velocity-limits", "title": "Velocity Limits", "text": "<ul> <li>Supply Air: Maximum 2500 FPM (13 m/s) for noise control</li> <li>Return Air: Maximum 1500 FPM (8 m/s) for energy efficiency</li> <li>Exhaust Air: Maximum 2000 FPM (10 m/s) depending on application</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#pressure-classifications", "title": "Pressure Classifications", "text": "<ul> <li>Low Pressure: Residential and light commercial</li> <li>Medium Pressure: Commercial and industrial</li> <li>High Pressure: Industrial and specialized applications</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#construction-standards", "title": "Construction Standards", "text": "<ul> <li>Sealing Requirements: Based on pressure class and leakage rates</li> <li>Support Spacing: Maximum distances between hangers</li> <li>Joint Types: Appropriate connection methods for pressure class</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#validation-warnings", "title": "Validation Warnings", "text": "<p>The system provides warnings for:</p> <ul> <li>Very Low Airflow: Below 25 CFM (12 L/s) - verify requirements</li> <li>Very High Airflow: Above 50,000 CFM (24,000 L/s) - consider multiple ducts</li> <li>Low Friction Rate: Below 0.05 in. w.g./100 ft - may result in oversized ducts</li> <li>High Friction Rate: Above 0.5 in. w.g./100 ft - may result in undersized ducts</li> <li>Velocity Exceedance: Above SMACNA recommended limits</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#best-practices", "title": "Best Practices", "text": ""}, {"location": "user-guide/air-duct-sizer/#friction-rate-selection", "title": "Friction Rate Selection", "text": ""}, {"location": "user-guide/air-duct-sizer/#low-friction-005-008-in-wg100-ft", "title": "Low Friction (0.05-0.08 in. w.g./100 ft)", "text": "<ul> <li>Pros: Lower energy costs, quieter operation</li> <li>Cons: Larger ducts, higher material costs</li> <li>Use: Long duct runs, energy-efficient designs</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#medium-friction-008-015-in-wg100-ft", "title": "Medium Friction (0.08-0.15 in. w.g./100 ft)", "text": "<ul> <li>Pros: Balanced approach, standard practice</li> <li>Cons: Moderate energy and material costs</li> <li>Use: Most commercial applications</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#high-friction-015-05-in-wg100-ft", "title": "High Friction (0.15-0.5 in. w.g./100 ft)", "text": "<ul> <li>Pros: Smaller ducts, lower material costs</li> <li>Cons: Higher energy costs, potential noise issues</li> <li>Use: Short runs, space-constrained applications</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#duct-type-selection", "title": "Duct Type Selection", "text": ""}, {"location": "user-guide/air-duct-sizer/#rectangular-ducts_1", "title": "Rectangular Ducts", "text": "<ul> <li>Advantages: Fits in tight spaces, easier to insulate</li> <li>Disadvantages: Higher pressure loss, more complex fabrication</li> <li>Best For: Space-constrained installations, architectural integration</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#round-ducts_1", "title": "Round Ducts", "text": "<ul> <li>Advantages: Lower pressure loss, easier fabrication, better airflow</li> <li>Disadvantages: Requires more space, harder to conceal</li> <li>Best For: Exposed installations, energy efficiency priority</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#troubleshooting", "title": "Troubleshooting", "text": ""}, {"location": "user-guide/air-duct-sizer/#common-issues", "title": "Common Issues", "text": ""}, {"location": "user-guide/air-duct-sizer/#velocity-too-high-warning", "title": "\"Velocity Too High\" Warning", "text": "<ul> <li>Cause: Friction rate too high or airflow too high for duct size</li> <li>Solution: Reduce friction rate or consider larger duct/multiple ducts</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#unrealistic-dimensions-error", "title": "\"Unrealistic Dimensions\" Error", "text": "<ul> <li>Cause: Input parameters result in impractical duct sizes</li> <li>Solution: Adjust friction rate or verify airflow requirements</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#standards-violation-warning", "title": "\"Standards Violation\" Warning", "text": "<ul> <li>Cause: Design exceeds SMACNA recommended limits</li> <li>Solution: Review design parameters and adjust as needed</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#validation-errors", "title": "Validation Errors", "text": ""}, {"location": "user-guide/air-duct-sizer/#missing-required-fields", "title": "Missing Required Fields", "text": "<ul> <li>Ensure all required inputs are provided</li> <li>Check for valid numeric values</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#out-of-range-values", "title": "Out of Range Values", "text": "<ul> <li>Verify airflow is within supported range</li> <li>Check friction rate is reasonable for application</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#invalid-combinations", "title": "Invalid Combinations", "text": "<ul> <li>Some parameter combinations may not be physically realizable</li> <li>Adjust inputs to achieve practical results</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#advanced-features", "title": "Advanced Features", "text": ""}, {"location": "user-guide/air-duct-sizer/#material-properties", "title": "Material Properties", "text": "<p>Different duct materials have varying roughness factors that affect pressure loss:</p> <ul> <li>Galvanized Steel: Roughness factor 0.0003 ft</li> <li>Aluminum: Roughness factor 0.0002 ft</li> <li>Stainless Steel: Roughness factor 0.0002 ft</li> <li>Flexible Duct: Roughness factor 0.003 ft (much higher)</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#pressure-loss-calculations", "title": "Pressure Loss Calculations", "text": "<p>The system uses the <PERSON><PERSON><PERSON> equation with duct-specific modifications:</p> <pre><code>ΔP = f × (L/D) × (ρ × V²/2)\n</code></pre> <p>Where: - ΔP = Pressure loss - f = Friction factor (based on Reynolds number and roughness) - L = Duct length - D = Hydraulic diameter - ρ = Air density - V = Air velocity</p>"}, {"location": "user-guide/air-duct-sizer/#standard-sizes", "title": "Standard Sizes", "text": "<p>The calculator rounds results to standard duct sizes:</p>"}, {"location": "user-guide/air-duct-sizer/#round-ducts-inches", "title": "Round Ducts (inches)", "text": "<p>4, 5, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36</p>"}, {"location": "user-guide/air-duct-sizer/#rectangular-ducts_2", "title": "Rectangular Ducts", "text": "<p>Standard increments based on SMACNA guidelines, typically in 2-inch increments for smaller sizes and 4-inch increments for larger sizes.</p>"}, {"location": "user-guide/air-duct-sizer/#integration-with-projects", "title": "Integration with Projects", "text": ""}, {"location": "user-guide/air-duct-sizer/#saving-calculations", "title": "Saving Calculations", "text": "<p>All calculations are automatically saved to your current project (if one is active) or as standalone calculations. Saved data includes:</p> <ul> <li>Input parameters</li> <li>Calculated results</li> <li>Compliance status</li> <li>Timestamp and metadata</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#exporting-results", "title": "Exporting Results", "text": "<p>Results can be exported in various formats:</p> <ul> <li>PDF Report: Formatted calculation summary</li> <li>CSV Data: Raw data for spreadsheet analysis</li> <li>JSON: Machine-readable format for integration</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#project-management", "title": "Project Management", "text": "<p>Organize your calculations by:</p> <ul> <li>Creating projects for different buildings or systems</li> <li>Grouping calculations by system type or zone</li> <li>Adding notes and descriptions for future reference</li> <li>Comparing alternatives side-by-side</li> </ul>"}, {"location": "user-guide/air-duct-sizer/#next-steps", "title": "Next Steps", "text": "<ul> <li>Project Management Guide: Learn to organize your work</li> <li>Units and Standards: Understand unit conversions and standards</li> <li>API Reference: Technical integration details</li> <li>Examples: Practical calculation examples</li> </ul>"}]}