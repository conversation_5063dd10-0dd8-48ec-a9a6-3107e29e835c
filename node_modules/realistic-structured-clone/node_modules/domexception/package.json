{"name": "domexception", "description": "An implementation of the DOMException class from browsers", "keywords": ["dom", "webidl", "web idl", "domexception", "error", "exception"], "version": "1.0.1", "author": "Domenic Denicola <<EMAIL>> (https://domenic.me/)", "license": "MIT", "repository": "jsdom/domexception", "main": "lib/public-api.js", "files": ["lib/"], "scripts": {"prepublish": "node scripts/generate.js", "pretest": "npm run prepublish", "test": "mocha", "lint": "eslint lib"}, "dependencies": {"webidl-conversions": "^4.0.2"}, "devDependencies": {"eslint": "^4.3.0", "mkdirp": "^0.5.1", "mocha": "^3.5.0", "request": "^2.81.0", "webidl2js": "^7.2.0"}}