import fakeIndexedDB from "./fakeIndexedDB.js";
export default fakeIndexedDB;
export { fakeIndexedDB as indexedDB };
export { default as IDBCursor } from "./FDBCursor.js";
export { default as IDBCursorWithValue } from "./FDBCursorWithValue.js";
export { default as IDBDatabase } from "./FDBDatabase.js";
export { default as IDBFactory } from "./FDBFactory.js";
export { default as IDBIndex } from "./FDBIndex.js";
export { default as IDBKeyRange } from "./FDBKeyRange.js";
export { default as IDBObjectStore } from "./FDBObjectStore.js";
export { default as IDBOpenDBRequest } from "./FDBOpenDBRequest.js";
export { default as IDBRequest } from "./FDBRequest.js";
export { default as IDBTransaction } from "./FDBTransaction.js";
export { default as IDBVersionChangeEvent } from "./FDBVersionChangeEvent.js";