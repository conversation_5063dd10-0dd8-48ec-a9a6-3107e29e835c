import { InvalidStateError } from "./lib/errors.js";
import FakeEventTarget from "./lib/FakeEventTarget.js";

class FDBRequest extends FakeEventTarget {
  _result = null;
  _error = null;
  source = null;
  transaction = null;
  readyState = "pending";
  onsuccess = null;
  onerror = null;

  get error() {
    if (this.readyState === "pending") {
      throw new InvalidStateError();
    }

    return this._error;
  }

  set error(value) {
    this._error = value;
  }

  get result() {
    if (this.readyState === "pending") {
      throw new InvalidStateError();
    }

    return this._result;
  }

  set result(value) {
    this._result = value;
  }

  toString() {
    return "[object IDBRequest]";
  }

}

export default FDBRequest;