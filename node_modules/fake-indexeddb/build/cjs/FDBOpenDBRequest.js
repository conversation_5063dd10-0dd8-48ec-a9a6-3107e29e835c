"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _FDBRequest = _interopRequireDefault(require("./FDBRequest.js"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

class FDBOpenDBRequest extends _FDBRequest.default {
  onupgradeneeded = null;
  onblocked = null;

  toString() {
    return "[object IDBOpenDBRequest]";
  }

}

var _default = FDBOpenDBRequest;
exports.default = _default;
module.exports = exports.default;