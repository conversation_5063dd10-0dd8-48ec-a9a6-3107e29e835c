"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "IDBCursor", {
  enumerable: true,
  get: function () {
    return _FDBCursor.default;
  }
});
Object.defineProperty(exports, "IDBCursorWithValue", {
  enumerable: true,
  get: function () {
    return _FDBCursorWithValue.default;
  }
});
Object.defineProperty(exports, "IDBDatabase", {
  enumerable: true,
  get: function () {
    return _FDBDatabase.default;
  }
});
Object.defineProperty(exports, "IDBFactory", {
  enumerable: true,
  get: function () {
    return _FDBFactory.default;
  }
});
Object.defineProperty(exports, "IDBIndex", {
  enumerable: true,
  get: function () {
    return _FDBIndex.default;
  }
});
Object.defineProperty(exports, "IDBKeyRange", {
  enumerable: true,
  get: function () {
    return _FDBKeyRange.default;
  }
});
Object.defineProperty(exports, "IDBObjectStore", {
  enumerable: true,
  get: function () {
    return _FDBObjectStore.default;
  }
});
Object.defineProperty(exports, "IDBOpenDBRequest", {
  enumerable: true,
  get: function () {
    return _FDBOpenDBRequest.default;
  }
});
Object.defineProperty(exports, "IDBRequest", {
  enumerable: true,
  get: function () {
    return _FDBRequest.default;
  }
});
Object.defineProperty(exports, "IDBTransaction", {
  enumerable: true,
  get: function () {
    return _FDBTransaction.default;
  }
});
Object.defineProperty(exports, "IDBVersionChangeEvent", {
  enumerable: true,
  get: function () {
    return _FDBVersionChangeEvent.default;
  }
});
exports.default = void 0;
Object.defineProperty(exports, "indexedDB", {
  enumerable: true,
  get: function () {
    return _fakeIndexedDB.default;
  }
});

var _fakeIndexedDB = _interopRequireDefault(require("./fakeIndexedDB.js"));

var _FDBCursor = _interopRequireDefault(require("./FDBCursor.js"));

var _FDBCursorWithValue = _interopRequireDefault(require("./FDBCursorWithValue.js"));

var _FDBDatabase = _interopRequireDefault(require("./FDBDatabase.js"));

var _FDBFactory = _interopRequireDefault(require("./FDBFactory.js"));

var _FDBIndex = _interopRequireDefault(require("./FDBIndex.js"));

var _FDBKeyRange = _interopRequireDefault(require("./FDBKeyRange.js"));

var _FDBObjectStore = _interopRequireDefault(require("./FDBObjectStore.js"));

var _FDBOpenDBRequest = _interopRequireDefault(require("./FDBOpenDBRequest.js"));

var _FDBRequest = _interopRequireDefault(require("./FDBRequest.js"));

var _FDBTransaction = _interopRequireDefault(require("./FDBTransaction.js"));

var _FDBVersionChangeEvent = _interopRequireDefault(require("./FDBVersionChangeEvent.js"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var _default = _fakeIndexedDB.default;
exports.default = _default;