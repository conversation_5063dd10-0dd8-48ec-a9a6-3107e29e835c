"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _FDBCursor = _interopRequireDefault(require("./FDBCursor.js"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

class FDBCursorWithValue extends _FDBCursor.default {
  value = undefined;

  constructor(source, range, direction, request) {
    super(source, range, direction, request);
  }

  toString() {
    return "[object IDBCursorWithValue]";
  }

}

var _default = FDBCursorWithValue;
exports.default = _default;
module.exports = exports.default;