declare const fakeIndexedDB: typeof indexedDB;
declare const FDBCursor: typeof IDBCursor;
declare const FDBCursorWithValue: typeof IDBCursorWithValue;
declare const FDBDatabase: typeof IDBDatabase;
declare const FDBFactory: typeof IDBFactory;
declare const FDBIndex: typeof IDBIndex;
declare const FDBKeyRange: typeof IDBKeyRange;
declare const FDBObjectStore: typeof IDBObjectStore;
declare const FDBOpenDBRequest: typeof IDBOpenDBRequest;
declare const FDBRequest: typeof IDBRequest;
declare const FDBTransaction: typeof IDBTransaction;
declare const FDBVersionChangeEvent: typeof IDBVersionChangeEvent;

export default fakeIndexedDB;

export {
    fakeIndexedDB as indexedDB,
    FDBCursor as IDBCursor,
    FDBCursorWithValue as IDBCursorWithValue,
    FDBDatabase as IDBDatabase,
    FDBFactory as IDBFactory,
    FDBIndex as IDBIndex,
    FDBKeyRange as IDBKeyRange,
    FDBObjectStore as IDBObjectStore,
    FDBOpenDBRequest as IDBOpenDBRequest,
    FDBRequest as IDBRequest,
    FDBTransaction as IDBTransaction,
    FDBVersionChangeEvent as IDBVersionChangeEvent,
};
