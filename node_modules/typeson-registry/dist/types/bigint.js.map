{"version": 3, "file": "bigint.js", "sources": ["../../types/bigint.js"], "sourcesContent": ["/* globals BigInt */\n\nconst bigint = {\n    bigint: {\n        test (x) {\n            return typeof x === 'bigint';\n        },\n        replace (n) { return String(n); },\n        revive (s) { return BigInt(s); }\n    }\n};\n\nexport default bigint;\n"], "names": ["bigint", "test", "x", "replace", "n", "String", "revive", "s", "BigInt"], "mappings": "yTAEe,CACXA,OAAQ,CACJC,mBAAMC,SACkB,iBAANA,GAElBC,yBAASC,UAAYC,OAAOD,IAC5BE,uBAAQC,UAAYC,OAAOD"}