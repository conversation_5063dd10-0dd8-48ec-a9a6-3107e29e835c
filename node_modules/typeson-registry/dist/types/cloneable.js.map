{"version": 3, "file": "cloneable.js", "sources": ["../../types/cloneable.js", "../../utils/generateUUID.js"], "sourcesContent": ["import generateUUID from '../utils/generateUUID.js';\n\nconst cloneableObjectsByUUID = {};\n\nconst cloneable = {\n    cloneable: {\n        test (x) {\n            return x && typeof x === 'object' &&\n                typeof x[Symbol.for('cloneEncapsulate')] === 'function';\n        },\n        replace (clonable) {\n            const encapsulated = clonable[Symbol.for('cloneEncapsulate')]();\n            const uuid = generateUUID();\n            cloneableObjectsByUUID[uuid] = clonable;\n            return {uuid, encapsulated};\n        },\n        revive ({uuid, encapsulated}) {\n            return cloneableObjectsByUUID[uuid][Symbol.for('cloneRevive')](\n                encapsulated\n            );\n        }\n    }\n};\n\nexport default cloneable;\n", "/* globals performance */\n\n// The `performance` global is optional\n\n/**\n * @todo We could use `import generateUUID from 'uuid/v4';` (but it needs\n *   crypto library, etc.; `rollup-plugin-node-builtins` doesn't recommend\n *   using its own version and though there is <https://www.npmjs.com/package/crypto-browserify>,\n *   it may be troublesome to bundle and not strongly needed)\n * @returns {string}\n */\nexport default function generateUUID () { //  Adapted from original: public domain/MIT: http://stackoverflow.com/a/8809472/271577\n    /* istanbul ignore next */\n    let d = Date.now() +\n        // use high-precision timer if available\n        // istanbul ignore next\n        (typeof performance !== 'undefined' &&\n            typeof performance.now === 'function'\n            ? performance.now()\n            : 0);\n\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/gu, function (c) {\n        /* eslint-disable no-bitwise */\n        const r = Math.trunc((d + Math.random() * 16) % 16);\n        d = Math.floor(d / 16);\n        return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);\n        /* eslint-enable no-bitwise */\n    });\n}\n"], "names": ["cloneableObjectsByUUID", "cloneable", "test", "x", "_typeof", "Symbol", "replace", "clonable", "encapsulated", "uuid", "generateUUID", "d", "Date", "now", "performance", "c", "r", "Math", "trunc", "random", "floor", "toString", "revive"], "mappings": "uiBAEA,IAAMA,EAAyB,SAEb,CACdC,UAAW,CACPC,mBAAMC,UACKA,GAAkB,WAAbC,QAAOD,IAC8B,mBAAtCA,EAAEE,WAAW,sBAE5BC,yBAASC,OACCC,EAAeD,EAASF,WAAW,uBACnCI,ECDH,SAASC,mBAEhBC,EAAIC,KAAKC,OAGe,oBAAhBC,aACuB,mBAApBA,YAAYD,IACjBC,YAAYD,MACZ,SAEH,uCAAuCP,QAAQ,SAAU,SAAUS,OAEhEC,EAAIC,KAAKC,OAAOP,EAAoB,GAAhBM,KAAKE,UAAiB,WAChDR,EAAIM,KAAKG,MAAMT,EAAI,KACL,MAANI,EAAYC,EAAS,EAAJA,EAAU,GAAMK,SAAS,ODbjCX,UACbV,EAAuBS,GAAQF,EACxB,CAACE,KAAAA,EAAMD,aAAAA,IAElBc,8BAASb,IAAAA,KAAMD,IAAAA,oBACJR,EAAuBS,GAAMJ,WAAW,gBAC3CG"}