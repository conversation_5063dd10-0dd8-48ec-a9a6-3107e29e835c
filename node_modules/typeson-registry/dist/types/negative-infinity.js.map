{"version": 3, "file": "negative-infinity.js", "sources": ["../../types/negative-infinity.js"], "sourcesContent": ["const negativeInfinity = {\n    negativeInfinity: {\n        test (x) { return x === Number.NEGATIVE_INFINITY; },\n        replace (n) { return '-Infinity'; },\n        revive (s) { return Number.NEGATIVE_INFINITY; }\n    }\n};\n\nexport default negativeInfinity;\n"], "names": ["negativeInfinity", "test", "x", "Number", "NEGATIVE_INFINITY", "replace", "n", "revive", "s"], "mappings": "mUAAyB,CACrBA,iBAAkB,CACdC,mBAAMC,UAAYA,IAAMC,OAAOC,mBAC/BC,yBAASC,SAAY,aACrBC,uBAAQC,UAAYL,OAAOC"}