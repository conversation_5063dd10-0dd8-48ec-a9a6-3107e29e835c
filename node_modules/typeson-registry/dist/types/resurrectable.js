!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):((e="undefined"!=typeof globalThis?globalThis:e||self).Typeson=e.Typeson||{},e.Typeson.types=e.Typeson.types||{},e.Typeson.types.resurrectable=t())}(this,(function(){"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var e={};return{resurrectable:{test:function test(e){return e&&!Array.isArray(e)&&["object","function","symbol"].includes(_typeof(e))},replace:function replace(t){var n=function generateUUID(){var e=Date.now()+("undefined"!=typeof performance&&"function"==typeof performance.now?performance.now():0);return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var n=Math.trunc((e+16*Math.random())%16);return e=Math.floor(e/16),("x"===t?n:3&n|8).toString(16)}))}();return e[n]=t,n},revive:function revive(t){return e[t]}}}}));
//# sourceMappingURL=resurrectable.js.map
