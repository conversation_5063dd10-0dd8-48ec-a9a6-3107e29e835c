{"version": 3, "file": "nan.js", "sources": ["../../types/nan.js"], "sourcesContent": ["const nan = {\n    nan: {\n        test (x) { return Number.isNaN(x); },\n        replace (n) { return 'NaN'; },\n        revive (s) { return Number.NaN; }\n    }\n};\n\nexport default nan;\n"], "names": ["nan", "test", "x", "Number", "isNaN", "replace", "n", "revive", "s", "NaN"], "mappings": "sTAAY,CACRA,IAAK,CACDC,mBAAMC,UAAYC,OAAOC,MAAMF,IAC/BG,yBAASC,SAAY,OACrBC,uBAAQC,UAAYL,OAAOM"}