{"version": 3, "file": "infinity.js", "sources": ["../../types/infinity.js"], "sourcesContent": ["const infinity = {\n    infinity: {\n        test (x) { return x === Number.POSITIVE_INFINITY; },\n        replace (n) { return 'Infinity'; },\n        revive (s) { return Number.POSITIVE_INFINITY; }\n    }\n};\n\nexport default infinity;\n"], "names": ["infinity", "test", "x", "Number", "POSITIVE_INFINITY", "replace", "n", "revive", "s"], "mappings": "2TAAiB,CACbA,SAAU,CACNC,mBAAMC,UAAYA,IAAMC,OAAOC,mBAC/BC,yBAASC,SAAY,YACrBC,uBAAQC,UAAYL,OAAOC"}