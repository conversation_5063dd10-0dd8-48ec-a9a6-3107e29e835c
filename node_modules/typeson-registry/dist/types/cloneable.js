!function(e,o){"object"==typeof exports&&"undefined"!=typeof module?module.exports=o():"function"==typeof define&&define.amd?define(o):((e="undefined"!=typeof globalThis?globalThis:e||self).Typeson=e.Typeson||{},e.Typeson.types=e.Typeson.types||{},e.Typeson.types.cloneable=o())}(this,(function(){"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var e={};return{cloneable:{test:function test(e){return e&&"object"===_typeof(e)&&"function"==typeof e[Symbol.for("cloneEncapsulate")]},replace:function replace(o){var n=o[Symbol.for("cloneEncapsulate")](),t=function generateUUID(){var e=Date.now()+("undefined"!=typeof performance&&"function"==typeof performance.now?performance.now():0);return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(o){var n=Math.trunc((e+16*Math.random())%16);return e=Math.floor(e/16),("x"===o?n:3&n|8).toString(16)}))}();return e[t]=o,{uuid:t,encapsulated:n}},revive:function revive(o){var n=o.uuid,t=o.encapsulated;return e[n][Symbol.for("cloneRevive")](t)}}}}));
//# sourceMappingURL=cloneable.js.map
