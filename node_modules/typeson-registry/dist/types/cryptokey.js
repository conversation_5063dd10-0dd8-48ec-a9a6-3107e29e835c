!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):((e="undefined"!=typeof globalThis?globalThis:e||self).Typeson=e.Typeson||{},e.Typeson.types=e.Typeson.types||{},e.Typeson.types.cryptokey=t())}(this,(function(){"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function _typeof(e){return typeof e}:function _typeof(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function _defineProperty(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){_defineProperty(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function _slicedToArray(e,t){return function _arrayWithHoles(e){if(Array.isArray(e))return e}(e)||function _iterableToArrayLimit(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,o=!1,a=void 0;try{for(var i,c=e[Symbol.iterator]();!(r=(i=c.next()).done)&&(n.push(i.value),!t||n.length!==t);r=!0);}catch(e){o=!0,a=e}finally{try{r||null==c.return||c.return()}finally{if(o)throw a}}return n}(e,t)||_unsupportedIterableToArray(e,t)||function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _toConsumableArray(e){return function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}(e)||function _iterableToArray(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||_unsupportedIterableToArray(e)||function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var e=function TypesonPromise(e){_classCallCheck(this,TypesonPromise),this.p=new Promise(e)};e.__typeson__type__="TypesonPromise","undefined"!=typeof Symbol&&(e.prototype[Symbol.toStringTag]="TypesonPromise"),e.prototype.then=function(t,n){var r=this;return new e((function(e,o){r.p.then((function(n){e(t?t(n):n)})).catch((function(e){return n?n(e):Promise.reject(e)})).then(e,o)}))},e.prototype.catch=function(e){return this.then(null,e)},e.resolve=function(t){return new e((function(e){e(t)}))},e.reject=function(t){return new e((function(e,n){n(t)}))},["all","race"].forEach((function(t){e[t]=function(n){return new e((function(e,r){Promise[t](n.map((function(e){return e&&e.constructor&&"TypesonPromise"===e.constructor.__typeson__type__?e.p:e}))).then(e,r)}))}}));var t={}.toString,n={}.hasOwnProperty,r=Object.getPrototypeOf,o=n.toString;function isThenable(e,t){return isObject(e)&&"function"==typeof e.then&&(!t||"function"==typeof e.catch)}function toStringTag(e){return t.call(e).slice(8,-1)}function hasConstructorOf(e,t){if(!e||"object"!==_typeof(e))return!1;var a=r(e);if(!a)return null===t;var i=n.call(a,"constructor")&&a.constructor;return"function"!=typeof i?null===t:t===i||(null!==t&&o.call(i)===o.call(t)||"function"==typeof t&&"string"==typeof i.__typeson__type__&&i.__typeson__type__===t.__typeson__type__)}function isPlainObject(e){return!(!e||"Object"!==toStringTag(e))&&(!r(e)||hasConstructorOf(e,Object))}function isObject(e){return e&&"object"===_typeof(e)}function escapeKeyPathComponent(e){return e.replace(/~/g,"~0").replace(/\./g,"~1")}function unescapeKeyPathComponent(e){return e.replace(/~1/g,".").replace(/~0/g,"~")}function getByKeyPath(e,t){if(""===t)return e;var n=t.indexOf(".");if(n>-1){var r=e[unescapeKeyPathComponent(t.slice(0,n))];return void 0===r?void 0:getByKeyPath(r,t.slice(n+1))}return e[unescapeKeyPathComponent(t)]}function setAtKeyPath(e,t,n){if(""===t)return n;var r=t.indexOf(".");return r>-1?setAtKeyPath(e[unescapeKeyPathComponent(t.slice(0,r))],t.slice(r+1),n):(e[unescapeKeyPathComponent(t)]=n,e)}function _await(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var a=Object.keys,i=Array.isArray,c={}.hasOwnProperty,s=["type","replaced","iterateIn","iterateUnsetNumeric"];function _async(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}function nestedPathsFirst(e,t){if(""===e.keypath)return-1;var n=e.keypath.match(/\./g)||0,r=t.keypath.match(/\./g)||0;return n&&(n=n.length),r&&(r=r.length),n>r?-1:n<r?1:e.keypath<t.keypath?-1:e.keypath>t.keypath}var u=function(){function Typeson(e){_classCallCheck(this,Typeson),this.options=e,this.plainObjectReplacers=[],this.nonplainObjectReplacers=[],this.revivers={},this.types={}}return function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),e}(Typeson,[{key:"stringify",value:function stringify(e,t,n,r){r=_objectSpread2(_objectSpread2(_objectSpread2({},this.options),r),{},{stringification:!0});var o=this.encapsulate(e,null,r);return i(o)?JSON.stringify(o[0],t,n):o.then((function(e){return JSON.stringify(e,t,n)}))}},{key:"stringifySync",value:function stringifySync(e,t,n,r){return this.stringify(e,t,n,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},r),{},{sync:!0}))}},{key:"stringifyAsync",value:function stringifyAsync(e,t,n,r){return this.stringify(e,t,n,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},r),{},{sync:!1}))}},{key:"parse",value:function parse(e,t,n){return n=_objectSpread2(_objectSpread2(_objectSpread2({},this.options),n),{},{parse:!0}),this.revive(JSON.parse(e,t),n)}},{key:"parseSync",value:function parseSync(e,t,n){return this.parse(e,t,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},n),{},{sync:!0}))}},{key:"parseAsync",value:function parseAsync(e,t,n){return this.parse(e,t,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},n),{},{sync:!1}))}},{key:"specialTypeNames",value:function specialTypeNames(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return n.returnTypeNames=!0,this.encapsulate(e,t,n)}},{key:"rootTypeName",value:function rootTypeName(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return n.iterateNone=!0,this.encapsulate(e,t,n)}},{key:"encapsulate",value:function encapsulate(t,n,r){var o=_async((function(t,n){return _await(Promise.all(n.map((function(e){return e[1].p}))),(function(r){return _await(Promise.all(r.map(_async((function(r){var a=!1,i=[],c=_slicedToArray(n.splice(0,1),1),s=_slicedToArray(c[0],7),u=s[0],p=s[2],y=s[3],l=s[4],f=s[5],h=s[6],v=_encapsulate(u,r,p,y,i,!0,h),d=hasConstructorOf(v,e);return function _invoke(e,t){var n=e();return n&&n.then?n.then(t):t(n)}((function(){if(u&&d)return _await(v.p,(function(e){return l[f]=e,a=!0,o(t,i)}))}),(function(e){return a?e:(u?l[f]=v:t=d?v.p:v,o(t,i))}))})))),(function(){return t}))}))})),u=(r=_objectSpread2(_objectSpread2({sync:!0},this.options),r)).sync,p=this,y={},l=[],f=[],h=[],v=!("cyclic"in r)||r.cyclic,d=r.encapsulateObserver,b=_encapsulate("",t,v,n||{},h);function finish(e){var t=Object.values(y);if(r.iterateNone)return t.length?t[0]:Typeson.getJSONType(e);if(t.length){if(r.returnTypeNames)return _toConsumableArray(new Set(t));e&&isPlainObject(e)&&!c.call(e,"$types")?e.$types=y:e={$:e,$types:{$:y}}}else isObject(e)&&c.call(e,"$types")&&(e={$:e,$types:!0});return!r.returnTypeNames&&e}function _adaptBuiltinStateObjectProperties(e,t,n){Object.assign(e,t);var r=s.map((function(t){var n=e[t];return delete e[t],n}));n(),s.forEach((function(t,n){e[t]=r[n]}))}function _encapsulate(t,n,o,s,u,h,v){var b,_={},O=_typeof(n),m=d?function(r){var a=v||s.type||Typeson.getJSONType(n);d(Object.assign(r||_,{keypath:t,value:n,cyclic:o,stateObj:s,promisesData:u,resolvingTypesonPromise:h,awaitingTypesonPromise:hasConstructorOf(n,e)},{type:a}))}:null;if(["string","boolean","number","undefined"].includes(O))return void 0===n||Number.isNaN(n)||n===Number.NEGATIVE_INFINITY||n===Number.POSITIVE_INFINITY?(b=s.replaced?n:replace(t,n,s,u,!1,h,m))!==n&&(_={replaced:b}):b=n,m&&m(),b;if(null===n)return m&&m(),n;if(o&&!s.iterateIn&&!s.iterateUnsetNumeric&&n&&"object"===_typeof(n)){var j=l.indexOf(n);if(!(j<0))return y[t]="#",m&&m({cyclicKeypath:f[j]}),"#"+f[j];!0===o&&(l.push(n),f.push(t))}var g,S=isPlainObject(n),T=i(n),P=(S||T)&&(!p.plainObjectReplacers.length||s.replaced)||s.iterateIn?n:replace(t,n,s,u,S||T,null,m);if(P!==n?(b=P,_={replaced:P}):""===t&&hasConstructorOf(n,e)?(u.push([t,n,o,s,void 0,void 0,s.type]),b=n):T&&"object"!==s.iterateIn||"array"===s.iterateIn?(g=new Array(n.length),_={clone:g}):(["function","symbol"].includes(_typeof(n))||"toJSON"in n||hasConstructorOf(n,e)||hasConstructorOf(n,Promise)||hasConstructorOf(n,ArrayBuffer))&&!S&&"object"!==s.iterateIn?b=n:(g={},s.addLength&&(g.length=n.length),_={clone:g}),m&&m(),r.iterateNone)return g||b;if(!g)return b;if(s.iterateIn){var w=function _loop(r){var a={ownKeys:c.call(n,r)};_adaptBuiltinStateObjectProperties(s,a,(function(){var a=t+(t?".":"")+escapeKeyPathComponent(r),i=_encapsulate(a,n[r],Boolean(o),s,u,h);hasConstructorOf(i,e)?u.push([a,i,Boolean(o),s,g,r,s.type]):void 0!==i&&(g[r]=i)}))};for(var A in n)w(A);m&&m({endIterateIn:!0,end:!0})}else a(n).forEach((function(r){var a=t+(t?".":"")+escapeKeyPathComponent(r);_adaptBuiltinStateObjectProperties(s,{ownKeys:!0},(function(){var t=_encapsulate(a,n[r],Boolean(o),s,u,h);hasConstructorOf(t,e)?u.push([a,t,Boolean(o),s,g,r,s.type]):void 0!==t&&(g[r]=t)}))})),m&&m({endIterateOwn:!0,end:!0});if(s.iterateUnsetNumeric){for(var k=n.length,C=function _loop2(r){if(!(r in n)){var a=t+(t?".":"")+r;_adaptBuiltinStateObjectProperties(s,{ownKeys:!1},(function(){var t=_encapsulate(a,void 0,Boolean(o),s,u,h);hasConstructorOf(t,e)?u.push([a,t,Boolean(o),s,g,r,s.type]):void 0!==t&&(g[r]=t)}))}},N=0;N<k;N++)C(N);m&&m({endIterateUnsetNumeric:!0,end:!0})}return g}function replace(e,t,n,r,o,a,i){for(var c=o?p.plainObjectReplacers:p.nonplainObjectReplacers,s=c.length;s--;){var l=c[s];if(l.test(t,n)){var f=l.type;if(p.revivers[f]){var h=y[e];y[e]=h?[f].concat(h):f}return Object.assign(n,{type:f,replaced:!0}),!u&&l.replaceAsync||l.replace?(i&&i({replacing:!0}),_encapsulate(e,l[u||!l.replaceAsync?"replace":"replaceAsync"](t,n),v&&"readonly",n,r,a,f)):(i&&i({typeDetected:!0}),_encapsulate(e,t,v&&"readonly",n,r,a,f))}}return t}return h.length?u&&r.throwOnBadSyncType?function(){throw new TypeError("Sync method requested but async result obtained")}():Promise.resolve(o(b,h)).then(finish):!u&&r.throwOnBadSyncType?function(){throw new TypeError("Async method requested but sync result obtained")}():r.stringification&&u?[finish(b)]:u?finish(b):Promise.resolve(finish(b))}},{key:"encapsulateSync",value:function encapsulateSync(e,t,n){return this.encapsulate(e,t,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},n),{},{sync:!0}))}},{key:"encapsulateAsync",value:function encapsulateAsync(e,t,n){return this.encapsulate(e,t,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},n),{},{sync:!1}))}},{key:"revive",value:function revive(t,n){var r=t&&t.$types;if(!r)return t;if(!0===r)return t.$;var o=(n=_objectSpread2(_objectSpread2({sync:!0},this.options),n)).sync,c=[],s={},u=!0;r.$&&isPlainObject(r.$)&&(t=t.$,r=r.$,u=!1);var y=this;function executeReviver(e,t){var n=_slicedToArray(y.revivers[e]||[],1)[0];if(!n)throw new Error("Unregistered type: "+e);return o&&!("revive"in n)?t:n[o&&n.revive?"revive":!o&&n.reviveAsync?"reviveAsync":"revive"](t,s)}var l=[];function checkUndefined(e){return hasConstructorOf(e,p)?void 0:e}var f,h=function revivePlainObjects(){var n=[];if(Object.entries(r).forEach((function(e){var t=_slicedToArray(e,2),o=t[0],a=t[1];"#"!==a&&[].concat(a).forEach((function(e){_slicedToArray(y.revivers[e]||[null,{}],2)[1].plain&&(n.push({keypath:o,type:e}),delete r[o])}))})),n.length)return n.sort(nestedPathsFirst).reduce((function reducer(n,r){var o=r.keypath,a=r.type;if(isThenable(n))return n.then((function(e){return reducer(e,{keypath:o,type:a})}));var i=getByKeyPath(t,o);if(hasConstructorOf(i=executeReviver(a,i),e))return i.then((function(e){var n=setAtKeyPath(t,o,e);n===e&&(t=n)}));var c=setAtKeyPath(t,o,i);c===i&&(t=c)}),void 0)}();return hasConstructorOf(h,e)?f=h.then((function(){return t})):(f=function _revive(t,n,o,s,y){if(!u||"$types"!==t){var f=r[t],h=i(n);if(h||isPlainObject(n)){var v=h?new Array(n.length):{};for(a(n).forEach((function(r){var a=_revive(t+(t?".":"")+escapeKeyPathComponent(r),n[r],o||v,v,r),i=function set(e){return hasConstructorOf(e,p)?v[r]=void 0:void 0!==e&&(v[r]=e),e};hasConstructorOf(a,e)?l.push(a.then((function(e){return i(e)}))):i(a)})),n=v;c.length;){var d=_slicedToArray(c[0],4),b=d[0],_=d[1],O=d[2],m=d[3],j=getByKeyPath(b,_);if(void 0===j)break;O[m]=j,c.splice(0,1)}}if(!f)return n;if("#"===f){var g=getByKeyPath(o,n.slice(1));return void 0===g&&c.push([o,n.slice(1),s,y]),g}return[].concat(f).reduce((function reducer(t,n){return hasConstructorOf(t,e)?t.then((function(e){return reducer(e,n)})):executeReviver(n,t)}),n)}}("",t,null),l.length&&(f=e.resolve(f).then((function(t){return e.all([t].concat(l))})).then((function(e){return _slicedToArray(e,1)[0]})))),isThenable(f)?o&&n.throwOnBadSyncType?function(){throw new TypeError("Sync method requested but async result obtained")}():hasConstructorOf(f,e)?f.p.then(checkUndefined):f:!o&&n.throwOnBadSyncType?function(){throw new TypeError("Async method requested but sync result obtained")}():o?checkUndefined(f):Promise.resolve(checkUndefined(f))}},{key:"reviveSync",value:function reviveSync(e,t){return this.revive(e,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},t),{},{sync:!0}))}},{key:"reviveAsync",value:function reviveAsync(e,t){return this.revive(e,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},t),{},{sync:!1}))}},{key:"register",value:function register(e,t){return t=t||{},[].concat(e).forEach((function R(e){var n=this;if(i(e))return e.map((function(e){return R.call(n,e)}));e&&a(e).forEach((function(n){if("#"===n)throw new TypeError("# cannot be used as a type name as it is reserved for cyclic objects");if(Typeson.JSON_TYPES.includes(n))throw new TypeError("Plain JSON object types are reserved as type names");var r=e[n],o=r&&r.testPlainObjects?this.plainObjectReplacers:this.nonplainObjectReplacers,a=o.filter((function(e){return e.type===n}));if(a.length&&(o.splice(o.indexOf(a[0]),1),delete this.revivers[n],delete this.types[n]),"function"==typeof r){var c=r;r={test:function test(e){return e&&e.constructor===c},replace:function replace(e){return _objectSpread2({},e)},revive:function revive(e){return Object.assign(Object.create(c.prototype),e)}}}else if(i(r)){var s=_slicedToArray(r,3);r={test:s[0],replace:s[1],revive:s[2]}}if(r&&r.test){var u={type:n,test:r.test.bind(r)};r.replace&&(u.replace=r.replace.bind(r)),r.replaceAsync&&(u.replaceAsync=r.replaceAsync.bind(r));var p="number"==typeof t.fallback?t.fallback:t.fallback?0:Number.POSITIVE_INFINITY;if(r.testPlainObjects?this.plainObjectReplacers.splice(p,0,u):this.nonplainObjectReplacers.splice(p,0,u),r.revive||r.reviveAsync){var y={};r.revive&&(y.revive=r.revive.bind(r)),r.reviveAsync&&(y.reviveAsync=r.reviveAsync.bind(r)),this.revivers[n]=[y,{plain:r.testPlainObjects}]}this.types[n]=r}}),this)}),this),this}}]),Typeson}(),p=function Undefined(){_classCallCheck(this,Undefined)};return p.__typeson__type__="TypesonUndefined",u.Undefined=p,u.Promise=e,u.isThenable=isThenable,u.toStringTag=toStringTag,u.hasConstructorOf=hasConstructorOf,u.isObject=isObject,u.isPlainObject=isPlainObject,u.isUserObject=function isUserObject(e){if(!e||"Object"!==toStringTag(e))return!1;var t=r(e);return!t||(hasConstructorOf(e,Object)||isUserObject(t))},u.escapeKeyPathComponent=escapeKeyPathComponent,u.unescapeKeyPathComponent=unescapeKeyPathComponent,u.getByKeyPath=getByKeyPath,u.getJSONType=function getJSONType(e){return null===e?"null":Array.isArray(e)?"array":_typeof(e)},u.JSON_TYPES=["null","boolean","number","string","array","object"],{cryptokey:{test:function test(e){return"CryptoKey"===u.toStringTag(e)&&e.extractable},replaceAsync:function replaceAsync(e){return new u.Promise((function(t,n){crypto.subtle.exportKey("jwk",e).catch((function(e){n(e)})).then((function(n){t({jwk:n,algorithm:e.algorithm,usages:e.usages})}))}))},revive:function revive(e){var t=e.jwk,n=e.algorithm,r=e.usages;return crypto.subtle.importKey("jwk",t,n,!0,r)}}}}));
//# sourceMappingURL=cryptokey.js.map
