{"version": 3, "file": "resurrectable.js", "sources": ["../../types/resurrectable.js", "../../utils/generateUUID.js"], "sourcesContent": ["// Here we allow the exact same non-plain object, function, and symbol\n//  instances to be resurrected (assuming the same session/environment);\n//  plain objects are ignored by <PERSON>son so not presently available and\n//  we consciously exclude arrays\n\nimport generateUUID from '../utils/generateUUID.js';\n\nconst resurrectableObjectsByUUID = {};\n\nconst resurrectable = {\n    resurrectable: {\n        test (x) {\n            return x &&\n                !Array.isArray(x) &&\n                ['object', 'function', 'symbol'].includes(typeof x);\n        },\n        replace (rsrrctble) {\n            const uuid = generateUUID();\n            resurrectableObjectsByUUID[uuid] = rsrrctble;\n            return uuid;\n        },\n        revive (serializedResurrectable) {\n            return resurrectableObjectsByUUID[serializedResurrectable];\n        }\n    }\n};\n\nexport default resurrectable;\n", "/* globals performance */\n\n// The `performance` global is optional\n\n/**\n * @todo We could use `import generateUUID from 'uuid/v4';` (but it needs\n *   crypto library, etc.; `rollup-plugin-node-builtins` doesn't recommend\n *   using its own version and though there is <https://www.npmjs.com/package/crypto-browserify>,\n *   it may be troublesome to bundle and not strongly needed)\n * @returns {string}\n */\nexport default function generateUUID () { //  Adapted from original: public domain/MIT: http://stackoverflow.com/a/8809472/271577\n    /* istanbul ignore next */\n    let d = Date.now() +\n        // use high-precision timer if available\n        // istanbul ignore next\n        (typeof performance !== 'undefined' &&\n            typeof performance.now === 'function'\n            ? performance.now()\n            : 0);\n\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/gu, function (c) {\n        /* eslint-disable no-bitwise */\n        const r = Math.trunc((d + Math.random() * 16) % 16);\n        d = Math.floor(d / 16);\n        return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);\n        /* eslint-enable no-bitwise */\n    });\n}\n"], "names": ["resurrectableObjectsByUUID", "resurrectable", "test", "x", "Array", "isArray", "includes", "replace", "rsrrctble", "uuid", "generateUUID", "d", "Date", "now", "performance", "c", "r", "Math", "trunc", "random", "floor", "toString", "revive", "serializedResurrectable"], "mappings": "2iBAOA,IAAMA,EAA6B,SAEb,CAClBC,cAAe,CACXC,mBAAMC,UACKA,IACFC,MAAMC,QAAQF,IACf,CAAC,SAAU,WAAY,UAAUG,iBAAgBH,KAEzDI,yBAASC,OACCC,ECNH,SAASC,mBAEhBC,EAAIC,KAAKC,OAGe,oBAAhBC,aACuB,mBAApBA,YAAYD,IACjBC,YAAYD,MACZ,SAEH,uCAAuCN,QAAQ,SAAU,SAAUQ,OAEhEC,EAAIC,KAAKC,OAAOP,EAAoB,GAAhBM,KAAKE,UAAiB,WAChDR,EAAIM,KAAKG,MAAMT,EAAI,KACL,MAANI,EAAYC,EAAS,EAAJA,EAAU,GAAMK,SAAS,ODRjCX,UACbV,EAA2BS,GAAQD,EAC5BC,GAEXa,uBAAQC,UACGvB,EAA2BuB"}