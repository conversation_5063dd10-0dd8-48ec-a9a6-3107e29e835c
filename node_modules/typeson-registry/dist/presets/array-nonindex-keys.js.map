{"version": 3, "file": "array-nonindex-keys.js", "sources": ["../../presets/array-nonindex-keys.js"], "sourcesContent": ["const arrayNonindexKeys = [\n    {\n        arrayNonindexKeys: {\n            testPlainObjects: true,\n            test (x, stateObj) {\n                if (Array.isArray(x)) {\n                    if (\n                        // By avoiding serializing arrays into objects which\n                        //  have only positive-integer keys, we reduce\n                        //  size and improve revival performance; arrays with\n                        //  non-index keys will be larger however\n                        Object.keys(x).some((k) => {\n                            //  No need to check for `isNaN` or\n                            //   `isNaN(Number.parseInt())` as `NaN` will be\n                            //   treated as a string.\n                            //  No need to do check as\n                            //   `Number.parseInt(Number())` since scientific\n                            //   notation will be pre-resolved if a number\n                            //   was given, and it will otherwise be a string\n                            return String(Number.parseInt(k)) !== k;\n                        })\n                    ) {\n                        stateObj.iterateIn = 'object';\n                        stateObj.addLength = true;\n                    }\n                    return true;\n                }\n                return false;\n            },\n            replace (a, stateObj) {\n                // Catch sparse undefined\n                stateObj.iterateUnsetNumeric = true;\n                return a;\n            },\n            revive (o) {\n                if (Array.isArray(o)) {\n                    return o;\n                }\n                const arr = [];\n                // No map here as may be a sparse array (including\n                //   with `length` set)\n                // Todo: Reenable when Node `engines` >= 7\n                // Object.entries(o).forEach(([key, val]) => {\n                Object.keys(o).forEach((key) => {\n                    const val = o[key];\n                    arr[key] = val;\n                });\n                return arr;\n            }\n        }\n    },\n    {\n        sparseUndefined: {\n            test (x, stateObj) {\n                return typeof x === 'undefined' && stateObj.ownKeys === false;\n            },\n            replace (n) { return 0; },\n            revive (s) { return undefined; } // Will avoid adding anything\n        }\n    }\n];\n\nexport default arrayNonindexKeys;\n"], "names": ["arrayNonindexKeys", "testPlainObjects", "test", "x", "stateObj", "Array", "isArray", "Object", "keys", "some", "k", "String", "Number", "parseInt", "iterateIn", "add<PERSON><PERSON><PERSON>", "replace", "a", "iterateUnsetNumeric", "revive", "o", "arr", "for<PERSON>ach", "key", "val", "sparseUndefined", "ownKeys", "n", "s"], "mappings": "0UAA0B,CACtB,CACIA,kBAAmB,CACfC,kBAAkB,EAClBC,mBAAMC,EAAGC,WACDC,MAAMC,QAAQH,KAMVI,OAAOC,KAAKL,GAAGM,MAAK,SAACC,UAQVC,OAAOC,OAAOC,SAASH,MAAQA,OAG1CN,EAASU,UAAY,SACrBV,EAASW,WAAY,IAElB,IAIfC,yBAASC,EAAGb,UAERA,EAASc,qBAAsB,EACxBD,GAEXE,uBAAQC,MACAf,MAAMC,QAAQc,UACPA,MAELC,EAAM,UAKZd,OAAOC,KAAKY,GAAGE,SAAQ,SAACC,OACdC,EAAMJ,EAAEG,GACdF,EAAIE,GAAOC,KAERH,KAInB,CACII,gBAAiB,CACbvB,mBAAMC,EAAGC,eACe,IAAND,IAA0C,IAArBC,EAASsB,SAEhDV,yBAASW,UAAY,GACrBR,uBAAQS"}