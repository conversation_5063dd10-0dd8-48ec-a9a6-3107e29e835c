!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):((e="undefined"!=typeof globalThis?globalThis:e||self).Typeson=e.Typeson||{},e.Typeson.presets=e.Typeson.presets||{},e.Typeson.presets.sparseUndefined=n())}(this,(function(){"use strict";return[{sparseArrays:{testPlainObjects:!0,test:function test(e){return Array.isArray(e)},replace:function replace(e,n){return n.iterateUnsetNumeric=!0,e}}},{sparseUndefined:{test:function test(e,n){return void 0===e&&!1===n.ownKeys},replace:function replace(e){return 0},revive:function revive(e){}}}]}));
//# sourceMappingURL=sparse-undefined.js.map
