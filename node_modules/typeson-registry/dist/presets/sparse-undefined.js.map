{"version": 3, "file": "sparse-undefined.js", "sources": ["../../presets/sparse-undefined.js"], "sourcesContent": ["const sparseUndefined = [\n    {\n        sparseArrays: {\n            testPlainObjects: true,\n            test (x) { return Array.isArray(x); },\n            replace (a, stateObj) {\n                stateObj.iterateUnsetNumeric = true;\n                return a;\n            }\n        }\n    },\n    {\n        sparseUndefined: {\n            test (x, stateObj) {\n                return typeof x === 'undefined' && stateObj.ownKeys === false;\n            },\n            replace (n) { return 0; },\n            revive (s) { return undefined; } // Will avoid adding anything\n        }\n    }\n];\n\nexport default sparseUndefined;\n"], "names": ["sparseA<PERSON>ys", "testPlainObjects", "test", "x", "Array", "isArray", "replace", "a", "stateObj", "iterateUnsetNumeric", "sparseUndefined", "ownKeys", "n", "revive", "s"], "mappings": "wUAAwB,CACpB,CACIA,aAAc,CACVC,kBAAkB,EAClBC,mBAAMC,UAAYC,MAAMC,QAAQF,IAChCG,yBAASC,EAAGC,UACRA,EAASC,qBAAsB,EACxBF,KAInB,CACIG,gBAAiB,CACbR,mBAAMC,EAAGK,eACe,IAANL,IAA0C,IAArBK,EAASG,SAEhDL,yBAASM,UAAY,GACrBC,uBAAQC"}