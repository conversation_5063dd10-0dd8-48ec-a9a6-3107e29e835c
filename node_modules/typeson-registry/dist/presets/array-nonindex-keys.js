!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):((e="undefined"!=typeof globalThis?globalThis:e||self).Typeson=e.Typeson||{},e.Typeson.presets=e.Typeson.presets||{},e.Typeson.presets.arrayNonindexKeys=n())}(this,(function(){"use strict";return[{arrayNonindexKeys:{testPlainObjects:!0,test:function test(e,n){return!!Array.isArray(e)&&(Object.keys(e).some((function(e){return String(Number.parseInt(e))!==e}))&&(n.iterateIn="object",n.addLength=!0),!0)},replace:function replace(e,n){return n.iterateUnsetNumeric=!0,e},revive:function revive(e){if(Array.isArray(e))return e;var n=[];return Object.keys(e).forEach((function(t){var r=e[t];n[t]=r})),n}}},{sparseUndefined:{test:function test(e,n){return void 0===e&&!1===n.ownKeys},replace:function replace(e){return 0},revive:function revive(e){}}}]}));
//# sourceMappingURL=array-nonindex-keys.js.map
