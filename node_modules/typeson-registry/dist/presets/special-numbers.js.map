{"version": 3, "file": "special-numbers.js", "sources": ["../../presets/special-numbers.js", "../../types/nan.js", "../../types/infinity.js", "../../types/negative-infinity.js"], "sourcesContent": ["import nan from '../types/nan.js';\nimport infinity from '../types/infinity.js';\nimport NegativeInfinity from '../types/negative-infinity.js';\n\nconst specialNumbers = [\n    nan,\n    infinity,\n    NegativeInfinity\n];\n\nexport default specialNumbers;\n", "const nan = {\n    nan: {\n        test (x) { return Number.isNaN(x); },\n        replace (n) { return 'NaN'; },\n        revive (s) { return Number.NaN; }\n    }\n};\n\nexport default nan;\n", "const infinity = {\n    infinity: {\n        test (x) { return x === Number.POSITIVE_INFINITY; },\n        replace (n) { return 'Infinity'; },\n        revive (s) { return Number.POSITIVE_INFINITY; }\n    }\n};\n\nexport default infinity;\n", "const negativeInfinity = {\n    negativeInfinity: {\n        test (x) { return x === Number.NEGATIVE_INFINITY; },\n        replace (n) { return '-Infinity'; },\n        revive (s) { return Number.NEGATIVE_INFINITY; }\n    }\n};\n\nexport default negativeInfinity;\n"], "names": ["nan", "test", "x", "Number", "isNaN", "replace", "n", "revive", "s", "NaN", "infinity", "POSITIVE_INFINITY", "negativeInfinity", "NEGATIVE_INFINITY"], "mappings": "uUAIuB,CCJX,CACRA,IAAK,CACDC,mBAAMC,UAAYC,OAAOC,MAAMF,IAC/BG,yBAASC,SAAY,OACrBC,uBAAQC,UAAYL,OAAOM,OCJlB,CACbC,SAAU,CACNT,mBAAMC,UAAYA,IAAMC,OAAOQ,mBAC/BN,yBAASC,SAAY,YACrBC,uBAAQC,UAAYL,OAAOQ,qBCJV,CACrBC,iBAAkB,CACdX,mBAAMC,UAAYA,IAAMC,OAAOU,mBAC/BR,yBAASC,SAAY,aACrBC,uBAAQC,UAAYL,OAAOU"}