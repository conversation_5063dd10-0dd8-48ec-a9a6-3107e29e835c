{"version": 3, "file": "socketio.js", "sources": ["../../presets/array-nonindex-keys.js", "../../node_modules/typeson/dist/typeson-esm.js", "../../types/undef.js", "../../types/primitive-objects.js", "../../presets/special-numbers.js", "../../types/nan.js", "../../types/infinity.js", "../../types/negative-infinity.js", "../../types/date.js", "../../types/error.js", "../../types/errors.js", "../../node_modules/base64-arraybuffer-es6/dist/base64-arraybuffer-es.js", "../../types/regexp.js", "../../types/map.js", "../../types/set.js", "../../types/arraybuffer.js", "../../types/typed-arrays.js", "../../types/dataview.js", "../../types/intl-types.js", "../../types/bigint.js", "../../types/bigint-object.js", "../../presets/builtin.js", "../../types/typed-arrays-socketio.js", "../../presets/socketio.js"], "sourcesContent": ["const arrayNonindexKeys = [\n    {\n        arrayNonindexKeys: {\n            testPlainObjects: true,\n            test (x, stateObj) {\n                if (Array.isArray(x)) {\n                    if (\n                        // By avoiding serializing arrays into objects which\n                        //  have only positive-integer keys, we reduce\n                        //  size and improve revival performance; arrays with\n                        //  non-index keys will be larger however\n                        Object.keys(x).some((k) => {\n                            //  No need to check for `isNaN` or\n                            //   `isNaN(Number.parseInt())` as `NaN` will be\n                            //   treated as a string.\n                            //  No need to do check as\n                            //   `Number.parseInt(Number())` since scientific\n                            //   notation will be pre-resolved if a number\n                            //   was given, and it will otherwise be a string\n                            return String(Number.parseInt(k)) !== k;\n                        })\n                    ) {\n                        stateObj.iterateIn = 'object';\n                        stateObj.addLength = true;\n                    }\n                    return true;\n                }\n                return false;\n            },\n            replace (a, stateObj) {\n                // Catch sparse undefined\n                stateObj.iterateUnsetNumeric = true;\n                return a;\n            },\n            revive (o) {\n                if (Array.isArray(o)) {\n                    return o;\n                }\n                const arr = [];\n                // No map here as may be a sparse array (including\n                //   with `length` set)\n                // Todo: Reenable when Node `engines` >= 7\n                // Object.entries(o).forEach(([key, val]) => {\n                Object.keys(o).forEach((key) => {\n                    const val = o[key];\n                    arr[key] = val;\n                });\n                return arr;\n            }\n        }\n    },\n    {\n        sparseUndefined: {\n            test (x, stateObj) {\n                return typeof x === 'undefined' && stateObj.ownKeys === false;\n            },\n            replace (n) { return 0; },\n            revive (s) { return undefined; } // Will avoid adding anything\n        }\n    }\n];\n\nexport default arrayNonindexKeys;\n", "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter);\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\n/**\n* @callback TypesonFulfilled\n* @returns {Promise<any>|any}\n*/\n\n/**\n* @callback TypesonRejected\n* @returns {Promise<any>|any}\n*/\n\n/**\n * @callback TypesonResolve\n * @param {any} value\n * @returns {Promise<any>}\n */\n\n/**\n * @callback TypesonReject\n * @param {Error|any} error\n * @returns {Promise<any>}\n */\n\n/**\n * @callback TypesonResolveReject\n * @param {TypesonResolve} typesonResolve\n * @param {TypesonReject} typesonReject\n * @returns {Promise<any>}\n */\n\n/* eslint-disable block-spacing, space-before-function-paren,\n  space-before-blocks, space-infix-ops, semi, promise/avoid-new,\n  jsdoc/require-jsdoc */\n\n/**\n * We keep this function minimized so if using two instances of this\n *   library, where one is minimized and one is not, it will still work\n *   with `hasConstructorOf`.\n * With ES6 classes, we may be able to simply use `class TypesonPromise\n *   extends Promise` and add a string tag for detection.\n * @param {TypesonResolveReject} f\n */\nvar TypesonPromise = function TypesonPromise(f) {\n  _classCallCheck(this, TypesonPromise);\n\n  this.p = new Promise(f);\n};\n/* eslint-enable block-spacing, space-before-function-paren,\n  space-before-blocks, space-infix-ops, semi, promise/avoid-new,\n  jsdoc/require-jsdoc */\n// eslint-disable-next-line max-len\n// class TypesonPromise extends Promise {get[Symbol.toStringTag](){return 'TypesonPromise'};} // eslint-disable-line keyword-spacing, space-before-function-paren, space-before-blocks, block-spacing, semi\n\n\nTypesonPromise.__typeson__type__ = 'TypesonPromise'; // Note: core-js-bundle provides a `Symbol` polyfill\n\n/* istanbul ignore else */\n\nif (typeof Symbol !== 'undefined') {\n  // Ensure `isUserObject` will return `false` for `TypesonPromise`\n  TypesonPromise.prototype[Symbol.toStringTag] = 'TypesonPromise';\n}\n/**\n *\n * @param {TypesonFulfilled} [onFulfilled]\n * @param {TypesonRejected} [onRejected]\n * @returns {TypesonPromise}\n */\n\n\nTypesonPromise.prototype.then = function (onFulfilled, onRejected) {\n  var _this = this;\n\n  return new TypesonPromise(function (typesonResolve, typesonReject) {\n    // eslint-disable-next-line promise/catch-or-return\n    _this.p.then(function (res) {\n      // eslint-disable-next-line promise/always-return\n      typesonResolve(onFulfilled ? onFulfilled(res) : res);\n    })[\"catch\"](function (res) {\n      return onRejected ? onRejected(res) : Promise.reject(res);\n    }).then(typesonResolve, typesonReject);\n  });\n};\n/**\n *\n * @param {TypesonRejected} onRejected\n * @returns {TypesonPromise}\n */\n\n\nTypesonPromise.prototype[\"catch\"] = function (onRejected) {\n  return this.then(null, onRejected);\n};\n/**\n *\n * @param {any} v\n * @returns {TypesonPromise}\n */\n\n\nTypesonPromise.resolve = function (v) {\n  return new TypesonPromise(function (typesonResolve) {\n    typesonResolve(v);\n  });\n};\n/**\n *\n * @param {any} v\n * @returns {TypesonPromise}\n */\n\n\nTypesonPromise.reject = function (v) {\n  return new TypesonPromise(function (typesonResolve, typesonReject) {\n    typesonReject(v);\n  });\n};\n\n['all', 'race'].forEach(function (meth) {\n  /**\n   *\n   * @param {Promise<any>[]} promArr\n   * @returns {TypesonPromise}\n   */\n  TypesonPromise[meth] = function (promArr) {\n    return new TypesonPromise(function (typesonResolve, typesonReject) {\n      // eslint-disable-next-line promise/catch-or-return\n      Promise[meth](promArr.map(function (prom) {\n        return prom && prom.constructor && prom.constructor.__typeson__type__ === 'TypesonPromise' ? prom.p : prom;\n      })).then(typesonResolve, typesonReject);\n    });\n  };\n});\n\nvar _ref = {},\n    toStr = _ref.toString,\n    hasOwn$1 = {}.hasOwnProperty,\n    getProto = Object.getPrototypeOf,\n    fnToString = hasOwn$1.toString;\n/**\n * Second argument not in use internally, but provided for utility.\n * @param {any} v\n * @param {boolean} catchCheck\n * @returns {boolean}\n */\n\nfunction isThenable(v, catchCheck) {\n  return isObject(v) && typeof v.then === 'function' && (!catchCheck || typeof v[\"catch\"] === 'function');\n}\n/**\n *\n * @param {any} val\n * @returns {string}\n */\n\n\nfunction toStringTag(val) {\n  return toStr.call(val).slice(8, -1);\n}\n/**\n * This function is dependent on both constructors\n *   being identical so any minimization is expected of both.\n * @param {any} a\n * @param {GenericFunction} b\n * @returns {boolean}\n */\n\n\nfunction hasConstructorOf(a, b) {\n  if (!a || _typeof(a) !== 'object') {\n    return false;\n  }\n\n  var proto = getProto(a);\n\n  if (!proto) {\n    return b === null;\n  }\n\n  var Ctor = hasOwn$1.call(proto, 'constructor') && proto.constructor;\n\n  if (typeof Ctor !== 'function') {\n    return b === null;\n  }\n\n  if (b === Ctor) {\n    return true;\n  }\n\n  if (b !== null && fnToString.call(Ctor) === fnToString.call(b)) {\n    return true;\n  }\n\n  if (typeof b === 'function' && typeof Ctor.__typeson__type__ === 'string' && Ctor.__typeson__type__ === b.__typeson__type__) {\n    return true;\n  }\n\n  return false;\n}\n/**\n *\n * @param {any} val\n * @returns {boolean}\n */\n\n\nfunction isPlainObject(val) {\n  // Mirrors jQuery's\n  if (!val || toStringTag(val) !== 'Object') {\n    return false;\n  }\n\n  var proto = getProto(val);\n\n  if (!proto) {\n    // `Object.create(null)`\n    return true;\n  }\n\n  return hasConstructorOf(val, Object);\n}\n/**\n *\n * @param {any} val\n * @returns {boolean}\n */\n\n\nfunction isUserObject(val) {\n  if (!val || toStringTag(val) !== 'Object') {\n    return false;\n  }\n\n  var proto = getProto(val);\n\n  if (!proto) {\n    // `Object.create(null)`\n    return true;\n  }\n\n  return hasConstructorOf(val, Object) || isUserObject(proto);\n}\n/**\n *\n * @param {any} v\n * @returns {boolean}\n */\n\n\nfunction isObject(v) {\n  return v && _typeof(v) === 'object';\n}\n/**\n *\n * @param {string} keyPathComponent\n * @returns {string}\n */\n\n\nfunction escapeKeyPathComponent(keyPathComponent) {\n  return keyPathComponent.replace(/~/g, '~0').replace(/\\./g, '~1');\n}\n/**\n *\n * @param {string} keyPathComponent\n * @returns {string}\n */\n\n\nfunction unescapeKeyPathComponent(keyPathComponent) {\n  return keyPathComponent.replace(/~1/g, '.').replace(/~0/g, '~');\n}\n/**\n * @param {PlainObject|GenericArray} obj\n * @param {string} keyPath\n * @returns {any}\n */\n\n\nfunction getByKeyPath(obj, keyPath) {\n  if (keyPath === '') {\n    return obj;\n  }\n\n  var period = keyPath.indexOf('.');\n\n  if (period > -1) {\n    var innerObj = obj[unescapeKeyPathComponent(keyPath.slice(0, period))];\n    return innerObj === undefined ? undefined : getByKeyPath(innerObj, keyPath.slice(period + 1));\n  }\n\n  return obj[unescapeKeyPathComponent(keyPath)];\n}\n/**\n *\n * @param {PlainObject} obj\n * @param {string} keyPath\n * @param {any} value\n * @returns {any}\n */\n\n\nfunction setAtKeyPath(obj, keyPath, value) {\n  if (keyPath === '') {\n    return value;\n  }\n\n  var period = keyPath.indexOf('.');\n\n  if (period > -1) {\n    var innerObj = obj[unescapeKeyPathComponent(keyPath.slice(0, period))];\n    return setAtKeyPath(innerObj, keyPath.slice(period + 1), value);\n  }\n\n  obj[unescapeKeyPathComponent(keyPath)] = value;\n  return obj;\n}\n/**\n *\n * @param {external:JSON} value\n * @returns {\"null\"|\"array\"|\"undefined\"|\"boolean\"|\"number\"|\"string\"|\n *  \"object\"|\"symbol\"}\n */\n\n\nfunction getJSONType(value) {\n  return value === null ? 'null' : Array.isArray(value) ? 'array' : _typeof(value);\n}\n\nfunction _await(value, then, direct) {\n  if (direct) {\n    return then ? then(value) : value;\n  }\n\n  if (!value || !value.then) {\n    value = Promise.resolve(value);\n  }\n\n  return then ? value.then(then) : value;\n}\n\nvar keys = Object.keys,\n    isArray = Array.isArray,\n    hasOwn = {}.hasOwnProperty,\n    internalStateObjPropsToIgnore = ['type', 'replaced', 'iterateIn', 'iterateUnsetNumeric'];\n/**\n * Handle plain object revivers first so reference setting can use\n * revived type (e.g., array instead of object); assumes revived\n * has same structure or will otherwise break subsequent references.\n * @param {PlainObjectType} a\n * @param {PlainObjectType} b\n * @returns {1|-1|boolean}\n */\n\nfunction _async(f) {\n  return function () {\n    for (var args = [], i = 0; i < arguments.length; i++) {\n      args[i] = arguments[i];\n    }\n\n    try {\n      return Promise.resolve(f.apply(this, args));\n    } catch (e) {\n      return Promise.reject(e);\n    }\n  };\n}\n/**\n * @callback Tester\n * @param {any} value\n * @param {StateObject} stateobj\n * @returns {boolean}\n */\n\n/**\n* @callback Replacer\n* @param {any} value\n* @param {StateObject} stateObj\n* @returns {any} Should be JSON-stringifiable\n*/\n\n/**\n* @callback Reviver\n* @param {JSON} value\n* @param {StateObject} stateObj\n* @returns {any}\n*/\n\n/**\n* @typedef {PlainObject} TypesonOptions\n* @property {boolean} stringification Auto-set by `stringify`\n*/\n\n/**\n * An instance of this class can be used to call `stringify()` and `parse()`.\n * Typeson resolves cyclic references by default. Can also be extended to\n * support custom types using the register() method.\n *\n * @class\n * @param {{cyclic: boolean}} [options] - if cyclic (default true),\n *   cyclic references will be handled gracefully.\n */\n\n\nfunction _invoke(body, then) {\n  var result = body();\n\n  if (result && result.then) {\n    return result.then(then);\n  }\n\n  return then(result);\n}\n\nfunction nestedPathsFirst(a, b) {\n  if (a.keypath === '') {\n    return -1;\n  }\n\n  var as = a.keypath.match(/\\./g) || 0;\n  var bs = b.keypath.match(/\\./g) || 0;\n\n  if (as) {\n    as = as.length;\n  }\n\n  if (bs) {\n    bs = bs.length;\n  }\n\n  return as > bs ? -1 : as < bs ? 1 : a.keypath < b.keypath ? -1 : a.keypath > b.keypath;\n}\n\nvar Typeson = /*#__PURE__*/function () {\n  /**\n   * @param {TypesonOptions} options\n   */\n  function Typeson(options) {\n    _classCallCheck(this, Typeson);\n\n    this.options = options; // Replacers signature: replace (value). Returns falsy if not\n    //   replacing. Otherwise ['Date', value.getTime()]\n\n    this.plainObjectReplacers = [];\n    this.nonplainObjectReplacers = []; // Revivers: [{type => reviver}, {plain: boolean}].\n    //   Sample: [{'Date': value => new Date(value)}, {plain: false}]\n\n    this.revivers = {};\n    /** Types registered via `register()`. */\n\n    this.types = {};\n  }\n  /**\n  * @typedef {null|boolean|number|string|GenericArray|PlainObject} JSON\n  */\n\n  /**\n  * @callback JSONReplacer\n  * @param {\"\"|string} key\n  * @param {JSON} value\n  * @returns {number|string|boolean|null|PlainObject|undefined}\n  * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#The%20replacer%20parameter\n  */\n\n  /**\n   * Serialize given object to Typeson.\n   * Initial arguments work identical to those of `JSON.stringify`.\n   * The `replacer` argument has nothing to do with our replacers.\n   * @param {any} obj\n   * @param {JSONReplacer|string[]} replacer\n   * @param {number|string} space\n   * @param {TypesonOptions} opts\n   * @returns {string|Promise<string>} Promise resolves to a string\n   */\n\n\n  _createClass(Typeson, [{\n    key: \"stringify\",\n    value: function stringify(obj, replacer, space, opts) {\n      opts = _objectSpread2(_objectSpread2(_objectSpread2({}, this.options), opts), {}, {\n        stringification: true\n      });\n      var encapsulated = this.encapsulate(obj, null, opts);\n\n      if (isArray(encapsulated)) {\n        return JSON.stringify(encapsulated[0], replacer, space);\n      }\n\n      return encapsulated.then(function (res) {\n        return JSON.stringify(res, replacer, space);\n      });\n    }\n    /**\n     * Also sync but throws on non-sync result.\n     * @param {any} obj\n     * @param {JSONReplacer|string[]} replacer\n     * @param {number|string} space\n     * @param {TypesonOptions} opts\n     * @returns {string}\n     */\n\n  }, {\n    key: \"stringifySync\",\n    value: function stringifySync(obj, replacer, space, opts) {\n      return this.stringify(obj, replacer, space, _objectSpread2(_objectSpread2({\n        throwOnBadSyncType: true\n      }, opts), {}, {\n        sync: true\n      }));\n    }\n    /**\n     *\n     * @param {any} obj\n     * @param {JSONReplacer|string[]} replacer\n     * @param {number|string} space\n     * @param {TypesonOptions} opts\n     * @returns {Promise<string>}\n     */\n\n  }, {\n    key: \"stringifyAsync\",\n    value: function stringifyAsync(obj, replacer, space, opts) {\n      return this.stringify(obj, replacer, space, _objectSpread2(_objectSpread2({\n        throwOnBadSyncType: true\n      }, opts), {}, {\n        sync: false\n      }));\n    }\n    /**\n    * @callback JSONReviver\n    * @param {string} key\n    * @param {JSON} value\n    * @returns {JSON}\n    */\n\n    /**\n     * Parse Typeson back into an obejct.\n     * Initial arguments works identical to those of `JSON.parse()`.\n     * @param {string} text\n     * @param {JSONReviver} reviver This JSON reviver has nothing to do with\n     *   our revivers.\n     * @param {TypesonOptions} opts\n     * @returns {external:JSON}\n     */\n\n  }, {\n    key: \"parse\",\n    value: function parse(text, reviver, opts) {\n      opts = _objectSpread2(_objectSpread2(_objectSpread2({}, this.options), opts), {}, {\n        parse: true\n      });\n      return this.revive(JSON.parse(text, reviver), opts);\n    }\n    /**\n    * Also sync but throws on non-sync result.\n    * @param {string} text\n    * @param {JSONReviver} reviver This JSON reviver has nothing to do with\n    *   our revivers.\n    * @param {TypesonOptions} opts\n    * @returns {external:JSON}\n    */\n\n  }, {\n    key: \"parseSync\",\n    value: function parseSync(text, reviver, opts) {\n      return this.parse(text, reviver, _objectSpread2(_objectSpread2({\n        throwOnBadSyncType: true\n      }, opts), {}, {\n        sync: true\n      }));\n    }\n    /**\n    * @param {string} text\n    * @param {JSONReviver} reviver This JSON reviver has nothing to do with\n    *   our revivers.\n    * @param {TypesonOptions} opts\n    * @returns {Promise<external:JSON>} Resolves to `external:JSON`\n    */\n\n  }, {\n    key: \"parseAsync\",\n    value: function parseAsync(text, reviver, opts) {\n      return this.parse(text, reviver, _objectSpread2(_objectSpread2({\n        throwOnBadSyncType: true\n      }, opts), {}, {\n        sync: false\n      }));\n    }\n    /**\n    * @typedef {} StateObject\n    */\n\n    /**\n     *\n     * @param {any} obj\n     * @param {StateObject} stateObj\n     * @param {TypesonOptions} [opts={}]\n     * @returns {string[]|false}\n     */\n\n  }, {\n    key: \"specialTypeNames\",\n    value: function specialTypeNames(obj, stateObj) {\n      var opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      opts.returnTypeNames = true;\n      return this.encapsulate(obj, stateObj, opts);\n    }\n    /**\n     *\n     * @param {any} obj\n     * @param {PlainObject} stateObj\n     * @param {PlainObject} [opts={}]\n     * @returns {Promise<any>|GenericArray|PlainObject|string|false}\n     */\n\n  }, {\n    key: \"rootTypeName\",\n    value: function rootTypeName(obj, stateObj) {\n      var opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      opts.iterateNone = true;\n      return this.encapsulate(obj, stateObj, opts);\n    }\n    /**\n     * Encapsulate a complex object into a plain Object by replacing\n     * registered types with plain objects representing the types data.\n     *\n     * This method is used internally by `Typeson.stringify()`.\n     * @param {any} obj - Object to encapsulate.\n     * @param {PlainObject} stateObj\n     * @param {PlainObject} opts\n     * @returns {Promise<any>|GenericArray|PlainObject|string|false}\n     */\n\n  }, {\n    key: \"encapsulate\",\n    value: function encapsulate(obj, stateObj, opts) {\n      /**\n       *\n       * @param {any} ret\n       * @param {GenericArray} promisesData\n       * @returns {Promise<any>}\n       */\n      var checkPromises = _async(function (ret, promisesData) {\n        return _await(Promise.all(promisesData.map(function (pd) {\n          return pd[1].p;\n        })), function (promResults) {\n          return _await(Promise.all(promResults.map(_async(function (promResult) {\n            var _exit = false;\n            var newPromisesData = [];\n\n            var _promisesData$splice = promisesData.splice(0, 1),\n                _promisesData$splice2 = _slicedToArray(_promisesData$splice, 1),\n                prData = _promisesData$splice2[0];\n\n            var _prData = _slicedToArray(prData, 7),\n                keyPath = _prData[0],\n                cyclic = _prData[2],\n                stateObj = _prData[3],\n                parentObj = _prData[4],\n                key = _prData[5],\n                detectedType = _prData[6];\n\n            var encaps = _encapsulate(keyPath, promResult, cyclic, stateObj, newPromisesData, true, detectedType);\n\n            var isTypesonPromise = hasConstructorOf(encaps, TypesonPromise); // Handle case where an embedded custom type itself\n            //   returns a `Typeson.Promise`\n\n            return _invoke(function () {\n              if (keyPath && isTypesonPromise) {\n                return _await(encaps.p, function (encaps2) {\n                  parentObj[key] = encaps2;\n                  _exit = true;\n                  return checkPromises(ret, newPromisesData);\n                });\n              }\n            }, function (_result) {\n              if (_exit) return _result;\n\n              if (keyPath) {\n                parentObj[key] = encaps;\n              } else if (isTypesonPromise) {\n                ret = encaps.p;\n              } else {\n                // If this is itself a `Typeson.Promise` (because the\n                //   original value supplied was a `Promise` or\n                //   because the supplied custom type value resolved\n                //   to one), returning it below will be fine since\n                //   a `Promise` is expected anyways given current\n                //   config (and if not a `Promise`, it will be ready\n                //   as the resolve value)\n                ret = encaps;\n              }\n\n              return checkPromises(ret, newPromisesData);\n            });\n          }))), function () {\n            return ret;\n          });\n        });\n      });\n      /**\n      * @typedef {PlainObject} OwnKeysObject\n      * @property {boolean} ownKeys\n      */\n\n      /**\n      * @callback BuiltinStateObjectPropertiesCallback\n      * @returns {void}\n      */\n\n      /**\n       *\n       * @param {StateObject} stateObj\n       * @param {OwnKeysObject} ownKeysObj\n       * @param {BuiltinStateObjectPropertiesCallback} cb\n       * @returns {undefined}\n       */\n\n\n      opts = _objectSpread2(_objectSpread2({\n        sync: true\n      }, this.options), opts);\n      var _opts = opts,\n          sync = _opts.sync;\n      var that = this,\n          types = {},\n          refObjs = [],\n          // For checking cyclic references\n      refKeys = [],\n          // For checking cyclic references\n      promisesDataRoot = []; // Clone the object deeply while at the same time replacing any\n      //   special types or cyclic reference:\n\n      var cyclic = 'cyclic' in opts ? opts.cyclic : true;\n      var _opts2 = opts,\n          encapsulateObserver = _opts2.encapsulateObserver;\n\n      var ret = _encapsulate('', obj, cyclic, stateObj || {}, promisesDataRoot);\n      /**\n       *\n       * @param {any} ret\n       * @returns {GenericArray|PlainObject|string|false}\n       */\n\n\n      function finish(ret) {\n        // Add `$types` to result only if we ever bumped into a\n        //  special type (or special case where object has own `$types`)\n        var typeNames = Object.values(types);\n\n        if (opts.iterateNone) {\n          if (typeNames.length) {\n            return typeNames[0];\n          }\n\n          return Typeson.getJSONType(ret);\n        }\n\n        if (typeNames.length) {\n          if (opts.returnTypeNames) {\n            return _toConsumableArray(new Set(typeNames));\n          } // Special if array (or a primitive) was serialized\n          //   because JSON would ignore custom `$types` prop on it\n\n\n          if (!ret || !isPlainObject(ret) || // Also need to handle if this is an object with its\n          //   own `$types` property (to avoid ambiguity)\n          hasOwn.call(ret, '$types')) {\n            ret = {\n              $: ret,\n              $types: {\n                $: types\n              }\n            };\n          } else {\n            ret.$types = types;\n          } // No special types\n\n        } else if (isObject(ret) && hasOwn.call(ret, '$types')) {\n          ret = {\n            $: ret,\n            $types: true\n          };\n        }\n\n        if (opts.returnTypeNames) {\n          return false;\n        }\n\n        return ret;\n      }\n\n      function _adaptBuiltinStateObjectProperties(stateObj, ownKeysObj, cb) {\n        Object.assign(stateObj, ownKeysObj);\n        var vals = internalStateObjPropsToIgnore.map(function (prop) {\n          var tmp = stateObj[prop];\n          delete stateObj[prop];\n          return tmp;\n        }); // eslint-disable-next-line node/callback-return\n\n        cb();\n        internalStateObjPropsToIgnore.forEach(function (prop, i) {\n          stateObj[prop] = vals[i];\n        });\n      }\n      /**\n       *\n       * @param {string} keypath\n       * @param {any} value\n       * @param {boolean} cyclic\n       * @param {PlainObject} stateObj\n       * @param {boolean} promisesData\n       * @param {boolean} resolvingTypesonPromise\n       * @param {string} detectedType\n       * @returns {any}\n       */\n\n\n      function _encapsulate(keypath, value, cyclic, stateObj, promisesData, resolvingTypesonPromise, detectedType) {\n        var ret;\n        var observerData = {};\n\n        var $typeof = _typeof(value);\n\n        var runObserver = encapsulateObserver ? function (obj) {\n          var type = detectedType || stateObj.type || Typeson.getJSONType(value);\n          encapsulateObserver(Object.assign(obj || observerData, {\n            keypath: keypath,\n            value: value,\n            cyclic: cyclic,\n            stateObj: stateObj,\n            promisesData: promisesData,\n            resolvingTypesonPromise: resolvingTypesonPromise,\n            awaitingTypesonPromise: hasConstructorOf(value, TypesonPromise)\n          }, {\n            type: type\n          }));\n        } : null;\n\n        if (['string', 'boolean', 'number', 'undefined'].includes($typeof)) {\n          if (value === undefined || Number.isNaN(value) || value === Number.NEGATIVE_INFINITY || value === Number.POSITIVE_INFINITY) {\n            ret = stateObj.replaced ? value : replace(keypath, value, stateObj, promisesData, false, resolvingTypesonPromise, runObserver);\n\n            if (ret !== value) {\n              observerData = {\n                replaced: ret\n              };\n            }\n          } else {\n            ret = value;\n          }\n\n          if (runObserver) {\n            runObserver();\n          }\n\n          return ret;\n        }\n\n        if (value === null) {\n          if (runObserver) {\n            runObserver();\n          }\n\n          return value;\n        }\n\n        if (cyclic && !stateObj.iterateIn && !stateObj.iterateUnsetNumeric && value && _typeof(value) === 'object') {\n          // Options set to detect cyclic references and be able\n          //   to rewrite them.\n          var refIndex = refObjs.indexOf(value);\n\n          if (refIndex < 0) {\n            if (cyclic === true) {\n              refObjs.push(value);\n              refKeys.push(keypath);\n            }\n          } else {\n            types[keypath] = '#';\n\n            if (runObserver) {\n              runObserver({\n                cyclicKeypath: refKeys[refIndex]\n              });\n            }\n\n            return '#' + refKeys[refIndex];\n          }\n        }\n\n        var isPlainObj = isPlainObject(value);\n        var isArr = isArray(value);\n        var replaced = // Running replace will cause infinite loop as will test\n        //   positive again\n        (isPlainObj || isArr) && (!that.plainObjectReplacers.length || stateObj.replaced) || stateObj.iterateIn ? // Optimization: if plain object and no plain-object\n        //   replacers, don't try finding a replacer\n        value : replace(keypath, value, stateObj, promisesData, isPlainObj || isArr, null, runObserver);\n        var clone;\n\n        if (replaced !== value) {\n          ret = replaced;\n          observerData = {\n            replaced: replaced\n          };\n        } else {\n          // eslint-disable-next-line no-lonely-if\n          if (keypath === '' && hasConstructorOf(value, TypesonPromise)) {\n            promisesData.push([keypath, value, cyclic, stateObj, undefined, undefined, stateObj.type]);\n            ret = value;\n          } else if (isArr && stateObj.iterateIn !== 'object' || stateObj.iterateIn === 'array') {\n            // eslint-disable-next-line unicorn/no-new-array -- Sparse\n            clone = new Array(value.length);\n            observerData = {\n              clone: clone\n            };\n          } else if (!['function', 'symbol'].includes(_typeof(value)) && !('toJSON' in value) && !hasConstructorOf(value, TypesonPromise) && !hasConstructorOf(value, Promise) && !hasConstructorOf(value, ArrayBuffer) || isPlainObj || stateObj.iterateIn === 'object') {\n            clone = {};\n\n            if (stateObj.addLength) {\n              clone.length = value.length;\n            }\n\n            observerData = {\n              clone: clone\n            };\n          } else {\n            ret = value; // Only clone vanilla objects and arrays\n          }\n        }\n\n        if (runObserver) {\n          runObserver();\n        }\n\n        if (opts.iterateNone) {\n          return clone || ret;\n        }\n\n        if (!clone) {\n          return ret;\n        } // Iterate object or array\n\n\n        if (stateObj.iterateIn) {\n          var _loop = function _loop(key) {\n            var ownKeysObj = {\n              ownKeys: hasOwn.call(value, key)\n            };\n\n            _adaptBuiltinStateObjectProperties(stateObj, ownKeysObj, function () {\n              var kp = keypath + (keypath ? '.' : '') + escapeKeyPathComponent(key);\n\n              var val = _encapsulate(kp, value[key], Boolean(cyclic), stateObj, promisesData, resolvingTypesonPromise);\n\n              if (hasConstructorOf(val, TypesonPromise)) {\n                promisesData.push([kp, val, Boolean(cyclic), stateObj, clone, key, stateObj.type]);\n              } else if (val !== undefined) {\n                clone[key] = val;\n              }\n            });\n          };\n\n          // eslint-disable-next-line guard-for-in\n          for (var key in value) {\n            _loop(key);\n          }\n\n          if (runObserver) {\n            runObserver({\n              endIterateIn: true,\n              end: true\n            });\n          }\n        } else {\n          // Note: Non-indexes on arrays won't survive stringify so\n          //  somewhat wasteful for arrays, but so too is iterating\n          //  all numeric indexes on sparse arrays when not wanted\n          //  or filtering own keys for positive integers\n          keys(value).forEach(function (key) {\n            var kp = keypath + (keypath ? '.' : '') + escapeKeyPathComponent(key);\n            var ownKeysObj = {\n              ownKeys: true\n            };\n\n            _adaptBuiltinStateObjectProperties(stateObj, ownKeysObj, function () {\n              var val = _encapsulate(kp, value[key], Boolean(cyclic), stateObj, promisesData, resolvingTypesonPromise);\n\n              if (hasConstructorOf(val, TypesonPromise)) {\n                promisesData.push([kp, val, Boolean(cyclic), stateObj, clone, key, stateObj.type]);\n              } else if (val !== undefined) {\n                clone[key] = val;\n              }\n            });\n          });\n\n          if (runObserver) {\n            runObserver({\n              endIterateOwn: true,\n              end: true\n            });\n          }\n        } // Iterate array for non-own numeric properties (we can't\n        //   replace the prior loop though as it iterates non-integer\n        //   keys)\n\n\n        if (stateObj.iterateUnsetNumeric) {\n          var vl = value.length;\n\n          var _loop2 = function _loop2(i) {\n            if (!(i in value)) {\n              // No need to escape numeric\n              var kp = keypath + (keypath ? '.' : '') + i;\n              var ownKeysObj = {\n                ownKeys: false\n              };\n\n              _adaptBuiltinStateObjectProperties(stateObj, ownKeysObj, function () {\n                var val = _encapsulate(kp, undefined, Boolean(cyclic), stateObj, promisesData, resolvingTypesonPromise);\n\n                if (hasConstructorOf(val, TypesonPromise)) {\n                  promisesData.push([kp, val, Boolean(cyclic), stateObj, clone, i, stateObj.type]);\n                } else if (val !== undefined) {\n                  clone[i] = val;\n                }\n              });\n            }\n          };\n\n          for (var i = 0; i < vl; i++) {\n            _loop2(i);\n          }\n\n          if (runObserver) {\n            runObserver({\n              endIterateUnsetNumeric: true,\n              end: true\n            });\n          }\n        }\n\n        return clone;\n      }\n      /**\n      * @typedef {PlainObject} KeyPathEvent\n      * @property {string} cyclicKeypath\n      */\n\n      /**\n      * @typedef {PlainObject} EndIterateInEvent\n      * @property {boolean} endIterateIn\n      * @property {boolean} end\n      */\n\n      /**\n      * @typedef {PlainObject} EndIterateUnsetNumericEvent\n      * @property {boolean} endIterateUnsetNumeric\n      * @property {boolean} end\n      */\n\n      /**\n      * @typedef {PlainObject} TypeDetectedEvent\n      * @property {boolean} typeDetected\n      */\n\n      /**\n      * @typedef {PlainObject} ReplacingEvent\n      * @property {boolean} replacing\n      */\n\n      /**\n      * @callback Observer\n      * @param {KeyPathEvent|EndIterateInEvent|EndIterateUnsetNumericEvent|\n      * TypeDetectedEvent|ReplacingEvent} [event]\n      * @returns {void}\n      */\n\n      /**\n       *\n       * @param {string} keypath\n       * @param {any} value\n       * @param {PlainObject} stateObj\n       * @param {GenericArray} promisesData\n       * @param {boolean} plainObject\n       * @param {boolean} resolvingTypesonPromise\n       * @param {Observer} [runObserver]\n       * @returns {any}\n       */\n\n\n      function replace(keypath, value, stateObj, promisesData, plainObject, resolvingTypesonPromise, runObserver) {\n        // Encapsulate registered types\n        var replacers = plainObject ? that.plainObjectReplacers : that.nonplainObjectReplacers;\n        var i = replacers.length;\n\n        while (i--) {\n          var replacer = replacers[i];\n\n          if (replacer.test(value, stateObj)) {\n            var type = replacer.type;\n\n            if (that.revivers[type]) {\n              // Record the type only if a corresponding reviver\n              //   exists. This is to support specs where only\n              //   replacement is done.\n              // For example, ensuring deep cloning of the object,\n              //   or replacing a type to its equivalent without\n              //   the need to revive it.\n              var existing = types[keypath]; // type can comprise an array of types (see test\n              //   \"should support intermediate types\")\n\n              types[keypath] = existing ? [type].concat(existing) : type;\n            }\n\n            Object.assign(stateObj, {\n              type: type,\n              replaced: true\n            });\n\n            if ((sync || !replacer.replaceAsync) && !replacer.replace) {\n              if (runObserver) {\n                runObserver({\n                  typeDetected: true\n                });\n              }\n\n              return _encapsulate(keypath, value, cyclic && 'readonly', stateObj, promisesData, resolvingTypesonPromise, type);\n            }\n\n            if (runObserver) {\n              runObserver({\n                replacing: true\n              });\n            } // Now, also traverse the result in case it contains its\n            //   own types to replace\n\n\n            var replaceMethod = sync || !replacer.replaceAsync ? 'replace' : 'replaceAsync';\n            return _encapsulate(keypath, replacer[replaceMethod](value, stateObj), cyclic && 'readonly', stateObj, promisesData, resolvingTypesonPromise, type);\n          }\n        }\n\n        return value;\n      }\n\n      return promisesDataRoot.length ? sync && opts.throwOnBadSyncType ? function () {\n        throw new TypeError('Sync method requested but async result obtained');\n      }() : Promise.resolve(checkPromises(ret, promisesDataRoot)).then(finish) : !sync && opts.throwOnBadSyncType ? function () {\n        throw new TypeError('Async method requested but sync result obtained');\n      }() // If this is a synchronous request for stringification, yet\n      //   a promise is the result, we don't want to resolve leading\n      //   to an async result, so we return an array to avoid\n      //   ambiguity\n      : opts.stringification && sync ? [finish(ret)] : sync ? finish(ret) : Promise.resolve(finish(ret));\n    }\n    /**\n     * Also sync but throws on non-sync result.\n     * @param {any} obj\n     * @param {StateObject} stateObj\n     * @param {TypesonOptions} opts\n     * @returns {any}\n     */\n\n  }, {\n    key: \"encapsulateSync\",\n    value: function encapsulateSync(obj, stateObj, opts) {\n      return this.encapsulate(obj, stateObj, _objectSpread2(_objectSpread2({\n        throwOnBadSyncType: true\n      }, opts), {}, {\n        sync: true\n      }));\n    }\n    /**\n     * @param {any} obj\n     * @param {StateObject} stateObj\n     * @param {TypesonOptions} opts\n     * @returns {any}\n     */\n\n  }, {\n    key: \"encapsulateAsync\",\n    value: function encapsulateAsync(obj, stateObj, opts) {\n      return this.encapsulate(obj, stateObj, _objectSpread2(_objectSpread2({\n        throwOnBadSyncType: true\n      }, opts), {}, {\n        sync: false\n      }));\n    }\n    /**\n     * Revive an encapsulated object.\n     * This method is used internally by `Typeson.parse()`.\n     * @param {PlainObject} obj - Object to revive. If it has `$types` member,\n     *   the properties that are listed there will be replaced with its true\n     *   type instead of just plain objects.\n     * @param {TypesonOptions} opts\n     * @throws TypeError If mismatch between sync/async type and result\n     * @returns {Promise<any>|any} If async, returns a Promise that resolves\n     * to `any`.\n     */\n\n  }, {\n    key: \"revive\",\n    value: function revive(obj, opts) {\n      var types = obj && obj.$types; // No type info added. Revival not needed.\n\n      if (!types) {\n        return obj;\n      } // Object happened to have own `$types` property but with\n      //   no actual types, so we unescape and return that object\n\n\n      if (types === true) {\n        return obj.$;\n      }\n\n      opts = _objectSpread2(_objectSpread2({\n        sync: true\n      }, this.options), opts);\n      var _opts3 = opts,\n          sync = _opts3.sync;\n      var keyPathResolutions = [];\n      var stateObj = {};\n      var ignore$Types = true; // Special when root object is not a trivial Object, it will\n      //   be encapsulated in `$`. It will also be encapsulated in\n      //   `$` if it has its own `$` property to avoid ambiguity\n\n      if (types.$ && isPlainObject(types.$)) {\n        obj = obj.$;\n        types = types.$;\n        ignore$Types = false;\n      }\n\n      var that = this;\n      /**\n       * @callback RevivalReducer\n       * @param {any} value\n       * @param {string} type\n       * @returns {any}\n       */\n\n      /**\n       *\n       * @param {string} type\n       * @param {any} val\n       * @throws {Error}\n       * @returns {any}\n       */\n\n      function executeReviver(type, val) {\n        var _ref = that.revivers[type] || [],\n            _ref2 = _slicedToArray(_ref, 1),\n            reviver = _ref2[0];\n\n        if (!reviver) {\n          throw new Error('Unregistered type: ' + type);\n        } // Only `sync` expected here, as problematic async would\n        //  be missing both `reviver` and `reviverAsync`, and\n        //  encapsulation shouldn't have added types, so\n        //  should have made an early exit\n\n\n        if (sync && !('revive' in reviver)) {\n          // Just return value as is\n          return val;\n        }\n\n        return reviver[sync && reviver.revive ? 'revive' : !sync && reviver.reviveAsync ? 'reviveAsync' : 'revive'](val, stateObj);\n      }\n      /**\n       *\n       * @returns {void|TypesonPromise<void>}\n       */\n\n\n      function revivePlainObjects() {\n        // const references = [];\n        // const reviveTypes = [];\n        var plainObjectTypes = [];\n        Object.entries(types).forEach(function (_ref3) {\n          var _ref4 = _slicedToArray(_ref3, 2),\n              keypath = _ref4[0],\n              type = _ref4[1];\n\n          if (type === '#') {\n            /*\n            references.push({\n                keypath,\n                reference: getByKeyPath(obj, keypath)\n            });\n            */\n            return;\n          }\n\n          [].concat(type).forEach(function (type) {\n            var _ref5 = that.revivers[type] || [null, {}],\n                _ref6 = _slicedToArray(_ref5, 2),\n                plain = _ref6[1].plain;\n\n            if (!plain) {\n              // reviveTypes.push({keypath, type});\n              return;\n            }\n\n            plainObjectTypes.push({\n              keypath: keypath,\n              type: type\n            });\n            delete types[keypath]; // Avoid repeating\n          });\n        });\n\n        if (!plainObjectTypes.length) {\n          return undefined;\n        } // console.log(plainObjectTypes.sort(nestedPathsFirst));\n\n        /**\n        * @typedef {PlainObject} PlainObjectType\n        * @property {string} keypath\n        * @property {string} type\n        */\n\n\n        return plainObjectTypes.sort(nestedPathsFirst).reduce(function reducer(possibleTypesonPromise, _ref7) {\n          var keypath = _ref7.keypath,\n              type = _ref7.type;\n\n          if (isThenable(possibleTypesonPromise)) {\n            return possibleTypesonPromise.then(function (val) {\n              return reducer(val, {\n                keypath: keypath,\n                type: type\n              });\n            });\n          } // console.log('obj', JSON.stringify(keypath), obj);\n\n\n          var val = getByKeyPath(obj, keypath);\n          val = executeReviver(type, val);\n\n          if (hasConstructorOf(val, TypesonPromise)) {\n            return val.then(function (v) {\n              var newVal = setAtKeyPath(obj, keypath, v);\n\n              if (newVal === v) {\n                obj = newVal;\n              }\n\n              return undefined;\n            });\n          }\n\n          var newVal = setAtKeyPath(obj, keypath, val);\n\n          if (newVal === val) {\n            obj = newVal;\n          }\n\n          return undefined;\n        }, undefined // This argument must be explicit\n        ); // references.forEach(({keypath, reference}) => {});\n        // reviveTypes.sort(nestedPathsFirst).forEach(() => {});\n      }\n\n      var revivalPromises = [];\n      /**\n       *\n       * @param {string} keypath\n       * @param {any} value\n       * @param {?(GenericArray|PlainObject)} target\n       * @param {GenericArray|PlainObject} [clone]\n       * @param {string} [key]\n       * @returns {any}\n       */\n\n      function _revive(keypath, value, target, clone, key) {\n        if (ignore$Types && keypath === '$types') {\n          return undefined;\n        }\n\n        var type = types[keypath];\n        var isArr = isArray(value);\n\n        if (isArr || isPlainObject(value)) {\n          // eslint-disable-next-line unicorn/no-new-array -- Sparse\n          var _clone = isArr ? new Array(value.length) : {}; // Iterate object or array\n\n\n          keys(value).forEach(function (k) {\n            var val = _revive(keypath + (keypath ? '.' : '') + escapeKeyPathComponent(k), value[k], target || _clone, _clone, k);\n\n            var set = function set(v) {\n              if (hasConstructorOf(v, Undefined)) {\n                _clone[k] = undefined;\n              } else if (v !== undefined) {\n                _clone[k] = v;\n              }\n\n              return v;\n            };\n\n            if (hasConstructorOf(val, TypesonPromise)) {\n              revivalPromises.push(val.then(function (ret) {\n                return set(ret);\n              }));\n            } else {\n              set(val);\n            }\n          });\n          value = _clone; // Try to resolve cyclic reference as soon as available\n\n          while (keyPathResolutions.length) {\n            var _keyPathResolutions$ = _slicedToArray(keyPathResolutions[0], 4),\n                _target = _keyPathResolutions$[0],\n                keyPath = _keyPathResolutions$[1],\n                _clone2 = _keyPathResolutions$[2],\n                k = _keyPathResolutions$[3];\n\n            var val = getByKeyPath(_target, keyPath); // Typeson.Undefined not expected here as not cyclic or\n            //   `undefined`\n\n            if (val !== undefined) {\n              _clone2[k] = val;\n            } else {\n              break;\n            }\n\n            keyPathResolutions.splice(0, 1);\n          }\n        }\n\n        if (!type) {\n          return value;\n        }\n\n        if (type === '#') {\n          var _ret = getByKeyPath(target, value.slice(1));\n\n          if (_ret === undefined) {\n            // Cyclic reference not yet available\n            keyPathResolutions.push([target, value.slice(1), clone, key]);\n          }\n\n          return _ret;\n        } // `type` can be an array here\n\n\n        return [].concat(type).reduce(function reducer(val, typ) {\n          if (hasConstructorOf(val, TypesonPromise)) {\n            return val.then(function (v) {\n              // TypesonPromise here too\n              return reducer(v, typ);\n            });\n          }\n\n          return executeReviver(typ, val);\n        }, value);\n      }\n      /**\n       *\n       * @param {any} retrn\n       * @returns {undefined|any}\n       */\n\n\n      function checkUndefined(retrn) {\n        return hasConstructorOf(retrn, Undefined) ? undefined : retrn;\n      }\n\n      var possibleTypesonPromise = revivePlainObjects();\n      var ret;\n\n      if (hasConstructorOf(possibleTypesonPromise, TypesonPromise)) {\n        ret = possibleTypesonPromise.then(function () {\n          return obj;\n        });\n      } else {\n        ret = _revive('', obj, null);\n\n        if (revivalPromises.length) {\n          // Ensure children resolved\n          ret = TypesonPromise.resolve(ret).then(function (r) {\n            return TypesonPromise.all([// May be a TypesonPromise or not\n            r].concat(revivalPromises));\n          }).then(function (_ref8) {\n            var _ref9 = _slicedToArray(_ref8, 1),\n                r = _ref9[0];\n\n            return r;\n          });\n        }\n      }\n\n      return isThenable(ret) ? sync && opts.throwOnBadSyncType ? function () {\n        throw new TypeError('Sync method requested but async result obtained');\n      }() : hasConstructorOf(ret, TypesonPromise) ? ret.p.then(checkUndefined) : ret : !sync && opts.throwOnBadSyncType ? function () {\n        throw new TypeError('Async method requested but sync result obtained');\n      }() : sync ? checkUndefined(ret) : Promise.resolve(checkUndefined(ret));\n    }\n    /**\n     * Also sync but throws on non-sync result.\n     * @param {any} obj\n     * @param {TypesonOptions} opts\n     * @returns {any}\n     */\n\n  }, {\n    key: \"reviveSync\",\n    value: function reviveSync(obj, opts) {\n      return this.revive(obj, _objectSpread2(_objectSpread2({\n        throwOnBadSyncType: true\n      }, opts), {}, {\n        sync: true\n      }));\n    }\n    /**\n    * @param {any} obj\n    * @param {TypesonOptions} opts\n    * @returns {Promise<any>}\n    */\n\n  }, {\n    key: \"reviveAsync\",\n    value: function reviveAsync(obj, opts) {\n      return this.revive(obj, _objectSpread2(_objectSpread2({\n        throwOnBadSyncType: true\n      }, opts), {}, {\n        sync: false\n      }));\n    }\n    /**\n    * @typedef {Tester|Replacer|Reviver} Spec\n    */\n\n    /**\n     * Register types.\n     * For examples on how to use this method, see\n     *   {@link https://github.com/dfahlander/typeson-registry/tree/master/types}.\n     * @param {object<string,Spec[]>[]} typeSpecSets -\n     * Types and their functions [test, encapsulate, revive];\n     * @param {TypesonOptions} opts\n     * @returns {Typeson}\n     */\n\n  }, {\n    key: \"register\",\n    value: function register(typeSpecSets, opts) {\n      opts = opts || {};\n      [].concat(typeSpecSets).forEach(function R(typeSpec) {\n        var _this = this;\n\n        // Allow arrays of arrays of arrays...\n        if (isArray(typeSpec)) {\n          return typeSpec.map(function (typSpec) {\n            return R.call(_this, typSpec);\n          });\n        }\n\n        typeSpec && keys(typeSpec).forEach(function (typeId) {\n          if (typeId === '#') {\n            throw new TypeError('# cannot be used as a type name as it is reserved ' + 'for cyclic objects');\n          } else if (Typeson.JSON_TYPES.includes(typeId)) {\n            throw new TypeError('Plain JSON object types are reserved as type names');\n          }\n\n          var spec = typeSpec[typeId];\n          var replacers = spec && spec.testPlainObjects ? this.plainObjectReplacers : this.nonplainObjectReplacers;\n          var existingReplacer = replacers.filter(function (r) {\n            return r.type === typeId;\n          });\n\n          if (existingReplacer.length) {\n            // Remove existing spec and replace with this one.\n            replacers.splice(replacers.indexOf(existingReplacer[0]), 1);\n            delete this.revivers[typeId];\n            delete this.types[typeId];\n          }\n\n          if (typeof spec === 'function') {\n            // Support registering just a class without replacer/reviver\n            var Class = spec;\n            spec = {\n              test: function test(x) {\n                return x && x.constructor === Class;\n              },\n              replace: function replace(x) {\n                return _objectSpread2({}, x);\n              },\n              revive: function revive(x) {\n                return Object.assign(Object.create(Class.prototype), x);\n              }\n            };\n          } else if (isArray(spec)) {\n            var _spec = spec,\n                _spec2 = _slicedToArray(_spec, 3),\n                test = _spec2[0],\n                replace = _spec2[1],\n                revive = _spec2[2];\n\n            spec = {\n              test: test,\n              replace: replace,\n              revive: revive\n            };\n          }\n\n          if (!spec || !spec.test) {\n            return;\n          }\n\n          var replacerObj = {\n            type: typeId,\n            test: spec.test.bind(spec)\n          };\n\n          if (spec.replace) {\n            replacerObj.replace = spec.replace.bind(spec);\n          }\n\n          if (spec.replaceAsync) {\n            replacerObj.replaceAsync = spec.replaceAsync.bind(spec);\n          }\n\n          var start = typeof opts.fallback === 'number' ? opts.fallback : opts.fallback ? 0 : Number.POSITIVE_INFINITY;\n\n          if (spec.testPlainObjects) {\n            this.plainObjectReplacers.splice(start, 0, replacerObj);\n          } else {\n            this.nonplainObjectReplacers.splice(start, 0, replacerObj);\n          } // Todo: We might consider a testAsync type\n\n\n          if (spec.revive || spec.reviveAsync) {\n            var reviverObj = {};\n\n            if (spec.revive) {\n              reviverObj.revive = spec.revive.bind(spec);\n            }\n\n            if (spec.reviveAsync) {\n              reviverObj.reviveAsync = spec.reviveAsync.bind(spec);\n            }\n\n            this.revivers[typeId] = [reviverObj, {\n              plain: spec.testPlainObjects\n            }];\n          } // Record to be retrieved via public types property.\n\n\n          this.types[typeId] = spec;\n        }, this);\n      }, this);\n      return this;\n    }\n  }]);\n\n  return Typeson;\n}();\n/**\n * We keep this function minimized so if using two instances of this\n * library, where one is minimized and one is not, it will still work\n * with `hasConstructorOf`.\n * @class\n */\n\n\nvar Undefined = function Undefined() {\n  _classCallCheck(this, Undefined);\n}; // eslint-disable-line space-before-blocks\n\n\nUndefined.__typeson__type__ = 'TypesonUndefined'; // The following provide classes meant to avoid clashes with other values\n// To insist `undefined` should be added\n\nTypeson.Undefined = Undefined; // To support async encapsulation/stringification\n\nTypeson.Promise = TypesonPromise; // Some fundamental type-checking utilities\n\nTypeson.isThenable = isThenable;\nTypeson.toStringTag = toStringTag;\nTypeson.hasConstructorOf = hasConstructorOf;\nTypeson.isObject = isObject;\nTypeson.isPlainObject = isPlainObject;\nTypeson.isUserObject = isUserObject;\nTypeson.escapeKeyPathComponent = escapeKeyPathComponent;\nTypeson.unescapeKeyPathComponent = unescapeKeyPathComponent;\nTypeson.getByKeyPath = getByKeyPath;\nTypeson.getJSONType = getJSONType;\nTypeson.JSON_TYPES = ['null', 'boolean', 'number', 'string', 'array', 'object'];\n\nexport default Typeson;\n", "// This does not preserve `undefined` in sparse arrays; see the `undefined`\n//  or `sparse-undefined` preset\nimport Typeson from 'typeson';\n\nconst undef = {\n    undef: {\n        test (x, stateObj) {\n            return typeof x === 'undefined' &&\n                (stateObj.ownKeys || !('ownKeys' in stateObj));\n        },\n        replace (n) { return 0; },\n        revive (s) {\n            // Will add `undefined` (returning `undefined` would instead\n            //   avoid explicitly setting)\n            return new Typeson.Undefined();\n        }\n    }\n};\n\nexport default undef;\n", "// This module is for objectified primitives (such as `new Number(3)` or\n//      `new String(\"foo\")`)\n/* eslint-disable no-new-wrappers, unicorn/new-for-builtins */\nimport Typeson from 'typeson';\n\nconst primitiveObjects = {\n    // String Object (not primitive string which need no type spec)\n    StringObject: {\n        test (x) {\n            return Typeson.toStringTag(x) === 'String' && typeof x === 'object';\n        },\n        replace (s) { return String(s); }, // convert to primitive string\n        revive (s) { return new String(s); } // Revive to an objectified string\n    },\n    // Boolean Object (not primitive boolean which need no type spec)\n    BooleanObject: {\n        test (x) {\n            return Typeson.toStringTag(x) === 'Boolean' &&\n                typeof x === 'object';\n        },\n        replace (b) { return Boolean(b); }, // convert to primitive boolean\n        revive (b) {\n            // Revive to an objectified Boolean\n            return new Boolean(b);\n        }\n    },\n    // Number Object (not primitive number which need no type spec)\n    NumberObject: {\n        test (x) {\n            return Typeson.toStringTag(x) === 'Number' && typeof x === 'object';\n        },\n        replace (n) { return Number(n); }, // convert to primitive number\n        revive (n) { return new Number(n); } // Revive to an objectified number\n    }\n};\n/* eslint-enable no-new-wrappers, unicorn/new-for-builtins */\n\nexport default primitiveObjects;\n", "import nan from '../types/nan.js';\nimport infinity from '../types/infinity.js';\nimport NegativeInfinity from '../types/negative-infinity.js';\n\nconst specialNumbers = [\n    nan,\n    infinity,\n    NegativeInfinity\n];\n\nexport default specialNumbers;\n", "const nan = {\n    nan: {\n        test (x) { return Number.isNaN(x); },\n        replace (n) { return 'NaN'; },\n        revive (s) { return Number.NaN; }\n    }\n};\n\nexport default nan;\n", "const infinity = {\n    infinity: {\n        test (x) { return x === Number.POSITIVE_INFINITY; },\n        replace (n) { return 'Infinity'; },\n        revive (s) { return Number.POSITIVE_INFINITY; }\n    }\n};\n\nexport default infinity;\n", "const negativeInfinity = {\n    negativeInfinity: {\n        test (x) { return x === Number.NEGATIVE_INFINITY; },\n        replace (n) { return '-Infinity'; },\n        revive (s) { return Number.NEGATIVE_INFINITY; }\n    }\n};\n\nexport default negativeInfinity;\n", "import Typeson from 'typeson';\n\nconst date = {\n    date: {\n        test (x) { return Typeson.toStringTag(x) === 'Date'; },\n        replace (dt) {\n            const time = dt.getTime();\n            if (Number.isNaN(time)) {\n                return 'NaN';\n            }\n            return time;\n        },\n        revive (time) {\n            if (time === 'NaN') {\n                return new Date(Number.NaN);\n            }\n            return new Date(time);\n        }\n    }\n};\n\nexport default date;\n", "import Typeson from 'typeson';\n\nconst error = {\n    error: {\n        test (x) { return Typeson.toStringTag(x) === 'Error'; },\n        replace ({name, message}) {\n            return {name, message};\n        },\n        revive ({name, message}) {\n            const e = new Error(message);\n            e.name = name;\n            return e;\n        }\n    }\n};\n// See also errors.js that may be registered after having registered this type.\n\nexport default error;\n", "/* eslint-env browser, node */\nimport Typeson from 'typeson';\n\n/* istanbul ignore next */\nconst _global = typeof self === 'undefined' ? global : self;\n\nconst errors = {};\n// Comprises all built-in errors.\n[\n    'TypeError',\n    'RangeError',\n    'SyntaxError',\n    'ReferenceError',\n    'EvalError',\n    'URIError',\n    'InternalError' // non-standard\n].forEach((errName) => {\n    const Cnstrctr = _global[errName];\n    if (Cnstrctr) {\n        errors[errName.toLowerCase()] = {\n            test (x) { return Typeson.hasConstructorOf(x, Cnstrctr); },\n            replace (e) { return e.message; },\n            revive (message) { return new Cnstrctr(message); }\n        };\n    }\n});\n\nexport default errors;\n", "/*\n * base64-arraybuffer\n * https://github.com/niklasvh/base64-arraybuffer\n *\n * Copyright (c) 2017 <PERSON>, 2012 <PERSON><PERSON>\n * Licensed under the MIT license.\n */\nvar chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'; // Use a lookup table to find the index.\n\nvar lookup = new Uint8Array(256);\n\nfor (var i = 0; i < chars.length; i++) {\n  lookup[chars.charCodeAt(i)] = i;\n}\n/**\n * @param {ArrayBuffer} arraybuffer\n * @param {Integer} byteOffset\n * @param {Integer} lngth\n * @returns {string}\n */\n\n\nvar encode = function encode(arraybuffer, byteOffset, lngth) {\n  if (lngth === null || lngth === undefined) {\n    lngth = arraybuffer.byteLength; // Needed for Safari\n  }\n\n  var bytes = new Uint8Array(arraybuffer, byteOffset || 0, // Default needed for Safari\n  lngth);\n  var len = bytes.length;\n  var base64 = '';\n\n  for (var _i = 0; _i < len; _i += 3) {\n    base64 += chars[bytes[_i] >> 2];\n    base64 += chars[(bytes[_i] & 3) << 4 | bytes[_i + 1] >> 4];\n    base64 += chars[(bytes[_i + 1] & 15) << 2 | bytes[_i + 2] >> 6];\n    base64 += chars[bytes[_i + 2] & 63];\n  }\n\n  if (len % 3 === 2) {\n    base64 = base64.slice(0, -1) + '=';\n  } else if (len % 3 === 1) {\n    base64 = base64.slice(0, -2) + '==';\n  }\n\n  return base64;\n};\n/**\n * @param {string} base64\n * @returns {ArrayBuffer}\n */\n\nvar decode = function decode(base64) {\n  var len = base64.length;\n  var bufferLength = base64.length * 0.75;\n  var p = 0;\n  var encoded1, encoded2, encoded3, encoded4;\n\n  if (base64[base64.length - 1] === '=') {\n    bufferLength--;\n\n    if (base64[base64.length - 2] === '=') {\n      bufferLength--;\n    }\n  }\n\n  var arraybuffer = new ArrayBuffer(bufferLength),\n      bytes = new Uint8Array(arraybuffer);\n\n  for (var _i2 = 0; _i2 < len; _i2 += 4) {\n    encoded1 = lookup[base64.charCodeAt(_i2)];\n    encoded2 = lookup[base64.charCodeAt(_i2 + 1)];\n    encoded3 = lookup[base64.charCodeAt(_i2 + 2)];\n    encoded4 = lookup[base64.charCodeAt(_i2 + 3)];\n    bytes[p++] = encoded1 << 2 | encoded2 >> 4;\n    bytes[p++] = (encoded2 & 15) << 4 | encoded3 >> 2;\n    bytes[p++] = (encoded3 & 3) << 6 | encoded4 & 63;\n  }\n\n  return arraybuffer;\n};\n\nexport { decode, encode };\n", "import Typeson from 'typeson';\n\nconst regexp = {\n    regexp: {\n        test (x) { return Typeson.toStringTag(x) === 'RegExp'; },\n        replace (rexp) {\n            return {\n                source: rexp.source,\n                flags: (rexp.global ? 'g' : '') +\n                    (rexp.ignoreCase ? 'i' : '') +\n                    (rexp.multiline ? 'm' : '') +\n                    (rexp.sticky ? 'y' : '') +\n                    (rexp.unicode ? 'u' : '')\n            };\n        },\n        revive ({source, flags}) { return new RegExp(source, flags); }\n    }\n};\n\nexport default regexp;\n", "import Typeson from 'typeson';\n\nconst map = {\n    map: {\n        test (x) { return Typeson.toStringTag(x) === 'Map'; },\n        replace (mp) { return [...mp.entries()]; },\n        revive (entries) { return new Map(entries); }\n    }\n};\n\nexport default map;\n", "import Typeson from 'typeson';\n\nconst set = {\n    set: {\n        test (x) { return Typeson.toStringTag(x) === 'Set'; },\n        replace (st) {\n            return [...st.values()];\n        },\n        revive (values) { return new Set(values); }\n    }\n};\n\nexport default set;\n", "import Typeson from 'typeson';\nimport {encode, decode} from 'base64-arraybuffer-es6';\n\nconst arraybuffer = {\n    arraybuffer: {\n        test (x) { return Typeson.toStringTag(x) === 'ArrayBuffer'; },\n        replace (b, stateObj) {\n            if (!stateObj.buffers) {\n                stateObj.buffers = [];\n            }\n            const index = stateObj.buffers.indexOf(b);\n            if (index > -1) {\n                return {index};\n            }\n            stateObj.buffers.push(b);\n            return encode(b);\n        },\n        revive (b64, stateObj) {\n            if (!stateObj.buffers) {\n                stateObj.buffers = [];\n            }\n            if (typeof b64 === 'object') {\n                return stateObj.buffers[b64.index];\n            }\n            const buffer = decode(b64);\n            stateObj.buffers.push(buffer);\n            return buffer;\n        }\n    }\n};\n\nexport default arraybuffer;\n\n// See also typed-arrays!\n", "/* eslint-env browser, node */\nimport Typeson from 'typeson';\nimport {encode, decode} from 'base64-arraybuffer-es6';\n\n/* istanbul ignore next */\nconst _global = typeof self === 'undefined' ? global : self;\n\nconst typedArrays = {};\n[\n    'Int8Array',\n    'Uint8Array',\n    'Uint8ClampedArray',\n    'Int16Array',\n    'Uint16Array',\n    'Int32Array',\n    'Uint32Array',\n    'Float32Array',\n    'Float64Array'\n].forEach(function (typeName) {\n    const arrType = typeName;\n    const TypedArray = _global[arrType];\n    /* istanbul ignore if */\n    if (!TypedArray) {\n        return;\n    }\n    typedArrays[typeName.toLowerCase()] = {\n        test (x) { return Typeson.toStringTag(x) === arrType; },\n        replace ({buffer, byteOffset, length: l}, stateObj) {\n            if (!stateObj.buffers) {\n                stateObj.buffers = [];\n            }\n            const index = stateObj.buffers.indexOf(buffer);\n            if (index > -1) {\n                return {index, byteOffset, length: l};\n            }\n            stateObj.buffers.push(buffer);\n            return {\n                encoded: encode(buffer),\n                byteOffset,\n                length: l\n            };\n        },\n        revive (b64Obj, stateObj) {\n            if (!stateObj.buffers) {\n                stateObj.buffers = [];\n            }\n            const {byteOffset, length: len, encoded, index} = b64Obj;\n            let buffer;\n            if ('index' in b64Obj) {\n                buffer = stateObj.buffers[index];\n            } else {\n                buffer = decode(encoded);\n                stateObj.buffers.push(buffer);\n            }\n            return new TypedArray(buffer, byteOffset, len);\n        }\n    };\n});\n\nexport default typedArrays;\n", "import Typeson from 'typeson';\nimport {encode, decode} from 'base64-arraybuffer-es6';\n\nconst dataview = {\n    dataview: {\n        test (x) { return Typeson.toStringTag(x) === 'DataView'; },\n        replace ({buffer, byteOffset, byteLength}, stateObj) {\n            if (!stateObj.buffers) {\n                stateObj.buffers = [];\n            }\n            const index = stateObj.buffers.indexOf(buffer);\n            if (index > -1) {\n                return {index, byteOffset, byteLength};\n            }\n            stateObj.buffers.push(buffer);\n            return {\n                encoded: encode(buffer),\n                byteOffset,\n                byteLength\n            };\n        },\n        revive (b64Obj, stateObj) {\n            if (!stateObj.buffers) {\n                stateObj.buffers = [];\n            }\n            const {byteOffset, byteLength, encoded, index} = b64Obj;\n            let buffer;\n            if ('index' in b64Obj) {\n                buffer = stateObj.buffers[index];\n            } else {\n                buffer = decode(encoded);\n                stateObj.buffers.push(buffer);\n            }\n            return new DataView(buffer, byteOffset, byteLength);\n        }\n    }\n};\n\nexport default dataview;\n", "import Typeson from 'typeson';\n\nconst IntlCollator = {\n    test (x) { return Typeson.hasConstructorOf(x, Intl.Collator); },\n    replace (c) { return c.resolvedOptions(); },\n    revive (options) { return new Intl.Collator(options.locale, options); }\n};\n\nconst IntlDateTimeFormat = {\n    test (x) { return Typeson.hasConstructorOf(x, Intl.DateTimeFormat); },\n    replace (dtf) { return dtf.resolvedOptions(); },\n    revive (options) {\n        return new Intl.DateTimeFormat(options.locale, options);\n    }\n};\n\nconst IntlNumberFormat = {\n    test (x) { return Typeson.hasConstructorOf(x, Intl.NumberFormat); },\n    replace (nf) { return nf.resolvedOptions(); },\n    revive (options) { return new Intl.NumberFormat(options.locale, options); }\n};\n\nconst intlTypes = {\n    IntlCollator,\n    IntlDateTimeFormat,\n    IntlNumberFormat\n};\n\nexport default intlTypes;\n", "/* globals BigInt */\n\nconst bigint = {\n    bigint: {\n        test (x) {\n            return typeof x === 'bigint';\n        },\n        replace (n) { return String(n); },\n        revive (s) { return BigInt(s); }\n    }\n};\n\nexport default bigint;\n", "/* globals BigInt */\nimport Typeson from 'typeson';\n\nconst bigintObject = {\n    bigintObject: {\n        test (x) {\n            return typeof x === 'object' && Typeson.hasConstructorOf(x, BigInt);\n        },\n        replace (n) { return String(n); },\n        revive (s) {\n            // Filed this to avoid error: https://github.com/eslint/eslint/issues/11810\n            // eslint-disable-next-line no-new-object\n            return new Object(BigInt(s));\n        }\n    }\n};\n\nexport default bigintObject;\n", "/* This preset includes types that are built-in into the JavaScript\n    language itself, this should work universally.\n\n  Types that were added in ES6 or beyond will be checked before inclusion\n   so that this module can be consumed by both ES5 and ES6 environments.\n\n  Some types cannot be encapsulated because their inner state is private:\n    `WeakMap`, `WeakSet`.\n\n  The Function type is not included because their closures would not be\n    serialized, so a revived Function that uses closures would not behave\n    as expected.\n\n  Symbols are similarly not included.\n*/\n\nimport arrayNonindexKeys from './array-nonindex-keys.js';\nimport undef from '../types/undef.js';\nimport primitiveObjects from '../types/primitive-objects.js';\nimport specialNumbers from './special-numbers.js';\nimport date from '../types/date.js';\nimport error from '../types/error.js';\nimport errors from '../types/errors.js';\nimport regexp from '../types/regexp.js';\nimport map from '../types/map.js';\nimport set from '../types/set.js';\nimport arraybuffer from '../types/arraybuffer.js';\nimport typedArrays from '../types/typed-arrays.js';\nimport dataview from '../types/dataview.js';\nimport intlTypes from '../types/intl-types.js';\nimport bigint from '../types/bigint.js';\nimport bigintObject from '../types/bigint-object.js';\n\nconst expObj = [\n    undef,\n    // ES5\n    arrayNonindexKeys, primitiveObjects, specialNumbers,\n    date, error, errors, regexp\n].concat(\n    // ES2015 (ES6)\n    /* istanbul ignore next */\n    typeof Map === 'function' ? map : [],\n    /* istanbul ignore next */\n    typeof Set === 'function' ? set : [],\n    /* istanbul ignore next */\n    typeof ArrayBuffer === 'function' ? arraybuffer : [],\n    /* istanbul ignore next */\n    typeof Uint8Array === 'function' ? typedArrays : [],\n    /* istanbul ignore next */\n    typeof DataView === 'function' ? dataview : [],\n    /* istanbul ignore next */\n    typeof Intl !== 'undefined' ? intlTypes : [],\n\n    /* istanbul ignore next */\n    typeof BigInt !== 'undefined' ? [bigint, bigintObject] : []\n);\nexport default expObj;\n", "/* eslint-env browser, node */\nimport Typeson from 'typeson';\n\n/* istanbul ignore next */\nconst _global = typeof self === 'undefined' ? global : self;\n\n// Support all kinds of typed arrays (views of ArrayBuffers)\nconst typedArraysSocketIO = {};\n[\n    'Int8Array',\n    'Uint8Array',\n    'Uint8ClampedArray',\n    'Int16Array',\n    'Uint16Array',\n    'Int32Array',\n    'Uint32Array',\n    'Float32Array',\n    'Float64Array'\n].forEach(function (typeName) {\n    const arrType = typeName;\n    const TypedArray = _global[typeName];\n    /* istanbul ignore if */\n    if (!TypedArray) {\n        return;\n    }\n    typedArraysSocketIO[typeName.toLowerCase()] = {\n        test (x) { return Typeson.toStringTag(x) === arrType; },\n        replace (a) {\n            return (a.byteOffset === 0 &&\n                a.byteLength === a.buffer.byteLength\n                ? a\n                // socket.io supports streaming ArrayBuffers.\n                // If we have a typed array representing a portion\n                //   of the buffer, we need to clone\n                //   the buffer before leaving it to socket.io.\n                : a.slice(0)).buffer;\n        },\n        revive (buf) {\n            // One may configure socket.io to revive binary data as\n            //    Buffer or Blob.\n            // We should therefore not rely on that the instance we\n            //   get here is an ArrayBuffer\n            // If not, let's assume user wants to receive it as\n            //   configured with socket.io.\n            return Typeson.toStringTag(buf) === 'ArrayBuffer'\n                ? new TypedArray(buf)\n                : buf;\n        }\n    };\n});\n\nexport default typedArraysSocketIO;\n", "import builtin from './builtin.js';\nimport typedArraysSocketIO from '../types/typed-arrays-socketio.js';\n\nconst socketio = [\n    builtin,\n    // Leave ArrayBuffer as is, and let socket.io stream it instead.\n    {arraybuffer: null},\n    // Encapsulate TypedArrays in ArrayBuffers instead of base64 strings.\n    typedArraysSocketIO\n];\n\nexport default socketio;\n"], "names": ["arrayNonindexKeys", "testPlainObjects", "test", "x", "stateObj", "Array", "isArray", "Object", "keys", "some", "k", "String", "Number", "parseInt", "iterateIn", "add<PERSON><PERSON><PERSON>", "replace", "a", "iterateUnsetNumeric", "revive", "o", "arr", "for<PERSON>ach", "key", "val", "sparseUndefined", "ownKeys", "n", "s", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "_defineProperty", "value", "object", "enumerableOnly", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "push", "apply", "_objectSpread2", "arguments", "source", "getOwnPropertyDescriptors", "defineProperties", "_slicedToArray", "_arrayWithHoles", "_iterableToArrayLimit", "_arr", "_n", "_d", "_e", "undefined", "_s", "_i", "next", "done", "err", "_unsupportedIterableToArray", "_nonIterableRest", "_toConsumableArray", "_arrayWithoutHoles", "_arrayLikeToArray", "_iterableToArray", "iter", "from", "_nonIterableSpread", "minLen", "toString", "call", "slice", "name", "len", "arr2", "TypesonPromise", "f", "this", "p", "Promise", "__typeson__type__", "toStringTag", "then", "onFulfilled", "onRejected", "_this", "typesonResolve", "typesonReject", "res", "reject", "resolve", "v", "meth", "promArr", "map", "prom", "toStr", "hasOwn$1", "hasOwnProperty", "getProto", "getPrototypeOf", "fnToString", "isThenable", "catch<PERSON><PERSON><PERSON>", "isObject", "hasConstructorOf", "b", "proto", "Ctor", "isPlainObject", "escapeKeyPathComponent", "keyPathComponent", "unescapeKeyPathComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keyP<PERSON>", "period", "indexOf", "innerObj", "set<PERSON>t<PERSON>ey<PERSON>ath", "_await", "direct", "hasOwn", "internalStateObjPropsToIgnore", "_async", "args", "e", "nested<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keypath", "as", "match", "bs", "<PERSON>son", "options", "plainObjectReplacers", "nonplainObjectReplacers", "revivers", "types", "_createClass", "protoProps", "staticProps", "stringify", "replacer", "space", "opts", "stringification", "encapsulated", "encapsulate", "JSON", "stringifySync", "throwOnBadSyncType", "sync", "stringifyAsync", "parse", "text", "reviver", "parseSync", "parseAsync", "specialTypeNames", "returnTypeNames", "rootTypeName", "iterateNone", "checkPromises", "ret", "promisesData", "all", "pd", "promResults", "promResult", "_exit", "newPromisesData", "_promisesData$splice2", "splice", "_prData", "cyclic", "parentObj", "detectedType", "encaps", "_encapsulate", "isTypesonPromise", "_invoke", "body", "result", "encaps2", "_result", "that", "refObjs", "refKeys", "promisesDataRoot", "encapsulateObserver", "finish", "typeNames", "values", "getJSONType", "Set", "$types", "$", "_adaptBuiltinStateObjectProperties", "ownKeysObj", "cb", "assign", "vals", "prop", "tmp", "resolvingTypesonPromise", "observerData", "$typeof", "runObserver", "type", "awaitingTypesonPromise", "includes", "isNaN", "NEGATIVE_INFINITY", "POSITIVE_INFINITY", "replaced", "refIndex", "cyclicKeypath", "clone", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isArr", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_loop", "kp", "Boolean", "endIterateIn", "end", "endIterateOwn", "vl", "_loop2", "endIterateUnsetNumeric", "plainObject", "replacers", "existing", "concat", "replaceAsync", "replacing", "typeDetected", "encapsulateSync", "encapsulateAsync", "keyPathResolutions", "ignore$Types", "executeReviver", "Error", "reviveAsync", "revivalPromises", "checkUndefined", "retrn", "Undefined", "possibleTypesonPromise", "revivePlainObjects", "plainObjectTypes", "entries", "_ref3", "_ref4", "plain", "sort", "reduce", "reducer", "_ref7", "newVal", "_revive", "_clone", "set", "_keyPathResolutions$", "_target", "_clone2", "_ret", "typ", "r", "_ref8", "reviveSync", "register", "typeSpecSets", "R", "typeSpec", "typSpec", "typeId", "JSON_TYPES", "spec", "existingReplacer", "Class", "create", "_spec2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bind", "start", "fallback", "reviver<PERSON><PERSON><PERSON>", "isUserObject", "undef", "primitiveObjects", "StringObject", "BooleanObject", "NumberObject", "specialNumbers", "nan", "NaN", "infinity", "negativeInfinity", "date", "dt", "time", "getTime", "Date", "error", "message", "_global", "self", "global", "errors", "err<PERSON><PERSON>", "Cnstrctr", "toLowerCase", "regexp", "rexp", "flags", "ignoreCase", "multiline", "sticky", "unicode", "RegExp", "mp", "Map", "st", "chars", "lookup", "Uint8Array", "charCodeAt", "encode", "arraybuffer", "byteOffset", "lngth", "byteLength", "bytes", "base64", "decode", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "_i2", "buffers", "index", "b64", "buffer", "typedArrays", "typeName", "arrType", "TypedArray", "l", "encoded", "b64Obj", "dataview", "DataView", "IntlCollator", "Intl", "Collator", "c", "resolvedOptions", "locale", "IntlDateTimeFormat", "DateTimeFormat", "dtf", "IntlNumberFormat", "NumberFormat", "nf", "bigint", "BigInt", "bigintObject", "expObj", "typedArraysSocketIO", "buf", "builtin"], "mappings": "2TAAA,IAAMA,EAAoB,CACtB,CACIA,kBAAmB,CACfC,kBAAkB,EAClBC,mBAAMC,EAAGC,WACDC,MAAMC,QAAQH,KAMVI,OAAOC,KAAKL,GAAGM,MAAK,SAACC,UAQVC,OAAOC,OAAOC,SAASH,MAAQA,OAG1CN,EAASU,UAAY,SACrBV,EAASW,WAAY,IAElB,IAIfC,yBAASC,EAAGb,UAERA,EAASc,qBAAsB,EACxBD,GAEXE,uBAAQC,MACAf,MAAMC,QAAQc,UACPA,MAELC,EAAM,UAKZd,OAAOC,KAAKY,GAAGE,SAAQ,SAACC,OACdC,EAAMJ,EAAEG,GACdF,EAAIE,GAAOC,KAERH,KAInB,CACII,gBAAiB,CACbvB,mBAAMC,EAAGC,eACe,IAAND,IAA0C,IAArBC,EAASsB,SAEhDV,yBAASW,UAAY,GACrBR,uBAAQS,QCzDpB,SAASC,UAAQC,UAIbD,UADoB,mBAAXE,QAAoD,iBAApBA,OAAOC,SACtC,iBAAUF,iBACJA,GAGN,iBAAUA,UACXA,GAAyB,mBAAXC,QAAyBD,EAAIG,cAAgBF,QAAUD,IAAQC,OAAOG,UAAY,gBAAkBJ,IAI9GA,GAGjB,SAASK,gBAAgBC,EAAUC,QAC3BD,aAAoBC,SAClB,IAAIC,UAAU,qCAIxB,SAASC,kBAAkBC,EAAQC,OAC5B,IAAIC,EAAI,EAAGA,EAAID,EAAME,OAAQD,IAAK,KACjCE,EAAaH,EAAMC,GACvBE,EAAWC,WAAaD,EAAWC,aAAc,EACjDD,EAAWE,cAAe,EACtB,UAAWF,IAAYA,EAAWG,UAAW,GACjDxC,OAAOyC,eAAeR,EAAQI,EAAWrB,IAAKqB,IAUlD,SAASK,gBAAgBnB,EAAKP,EAAK2B,UAC7B3B,KAAOO,EACTvB,OAAOyC,eAAelB,EAAKP,EAAK,CAC9B2B,MAAOA,EACPL,YAAY,EACZC,cAAc,EACdC,UAAU,IAGZjB,EAAIP,GAAO2B,EAGNpB,EAGT,SAASJ,QAAQyB,EAAQC,OACnB5C,EAAOD,OAAOC,KAAK2C,MAEnB5C,OAAO8C,sBAAuB,KAC5BC,EAAU/C,OAAO8C,sBAAsBF,GACvCC,IAAgBE,EAAUA,EAAQC,QAAO,SAAUC,UAC9CjD,OAAOkD,yBAAyBN,EAAQK,GAAKX,eAEtDrC,EAAKkD,KAAKC,MAAMnD,EAAM8C,UAGjB9C,EAGT,SAASoD,eAAepB,OACjB,IAAIE,EAAI,EAAGA,EAAImB,UAAUlB,OAAQD,IAAK,KACrCoB,EAAyB,MAAhBD,UAAUnB,GAAamB,UAAUnB,GAAK,GAE/CA,EAAI,EACNhB,QAAQnB,OAAOuD,IAAS,GAAMxC,SAAQ,SAAUC,GAC9C0B,gBAAgBT,EAAQjB,EAAKuC,EAAOvC,OAE7BhB,OAAOwD,0BAChBxD,OAAOyD,iBAAiBxB,EAAQjC,OAAOwD,0BAA0BD,IAEjEpC,QAAQnB,OAAOuD,IAASxC,SAAQ,SAAUC,GACxChB,OAAOyC,eAAeR,EAAQjB,EAAKhB,OAAOkD,yBAAyBK,EAAQvC,cAK1EiB,EAGT,SAASyB,eAAe5C,EAAKqB,UAY7B,SAASwB,gBAAgB7C,MACnBhB,MAAMC,QAAQe,GAAM,OAAOA,EAZxB6C,CAAgB7C,IAmBzB,SAAS8C,sBAAsB9C,EAAKqB,MACZ,oBAAXX,UAA4BA,OAAOC,YAAYzB,OAAOc,IAAO,WACpE+C,EAAO,GACPC,GAAK,EACLC,GAAK,EACLC,OAAKC,UAGF,IAAiCC,EAA7BC,EAAKrD,EAAIU,OAAOC,cAAmBqC,GAAMI,EAAKC,EAAGC,QAAQC,QAChER,EAAKV,KAAKe,EAAGvB,QAETR,GAAK0B,EAAKzB,SAAWD,GAH8C2B,GAAK,IAK9E,MAAOQ,GACPP,GAAK,EACLC,EAAKM,cAGER,GAAsB,MAAhBK,EAAE,QAAoBA,EAAE,oBAE/BJ,EAAI,MAAMC,UAIXH,EA3CwBD,CAAsB9C,EAAKqB,IAAMoC,8BAA4BzD,EAAKqB,IAmEnG,SAASqC,yBACD,IAAIzC,UAAU,6IApEmFyC,GAGzG,SAASC,qBAAmB3D,UAI5B,SAAS4D,qBAAmB5D,MACtBhB,MAAMC,QAAQe,GAAM,OAAO6D,oBAAkB7D,GAJ1C4D,CAAmB5D,IAW5B,SAAS8D,mBAAiBC,MACF,oBAAXrD,QAA0BA,OAAOC,YAAYzB,OAAO6E,GAAO,OAAO/E,MAAMgF,KAAKD,GAZtDD,CAAiB9D,IAAQyD,8BAA4BzD,IA2DzF,SAASiE,6BACD,IAAIhD,UAAU,wIA5D2EgD,GA0CjG,SAASR,8BAA4B1D,EAAGmE,MACjCnE,MACY,iBAANA,EAAgB,OAAO8D,oBAAkB9D,EAAGmE,OACnD5D,EAAIpB,OAAO2B,UAAUsD,SAASC,KAAKrE,GAAGsE,MAAM,GAAI,SAC1C,WAAN/D,GAAkBP,EAAEa,cAAaN,EAAIP,EAAEa,YAAY0D,MAC7C,QAANhE,GAAqB,QAANA,EAAoBtB,MAAMgF,KAAKjE,GACxC,cAANO,GAAqB,2CAA2CzB,KAAKyB,GAAWuD,oBAAkB9D,EAAGmE,WAG3G,SAASL,oBAAkB7D,EAAKuE,IACnB,MAAPA,GAAeA,EAAMvE,EAAIsB,UAAQiD,EAAMvE,EAAIsB,YAE1C,IAAID,EAAI,EAAGmD,EAAO,IAAIxF,MAAMuF,GAAMlD,EAAIkD,EAAKlD,IAAKmD,EAAKnD,GAAKrB,EAAIqB,UAE5DmD,EAoDT,IAAIC,EAAiB,SAASA,eAAeC,GAC3C5D,gBAAgB6D,KAAMF,qBAEjBG,EAAI,IAAIC,QAAQH,IASvBD,EAAeK,kBAAoB,iBAIb,oBAAXpE,SAET+D,EAAe5D,UAAUH,OAAOqE,aAAe,kBAUjDN,EAAe5D,UAAUmE,KAAO,SAAUC,EAAaC,OACjDC,EAAQR,YAEL,IAAIF,GAAe,SAAUW,EAAgBC,GAElDF,EAAMP,EAAEI,MAAK,SAAUM,GAErBF,EAAeH,EAAcA,EAAYK,GAAOA,MAFlD,OAGY,SAAUA,UACbJ,EAAaA,EAAWI,GAAOT,QAAQU,OAAOD,MACpDN,KAAKI,EAAgBC,OAU5BZ,EAAe5D,UAAf,MAAoC,SAAUqE,UACrCP,KAAKK,KAAK,KAAME,IASzBT,EAAee,QAAU,SAAUC,UAC1B,IAAIhB,GAAe,SAAUW,GAClCA,EAAeK,OAUnBhB,EAAec,OAAS,SAAUE,UACzB,IAAIhB,GAAe,SAAUW,EAAgBC,GAClDA,EAAcI,OAIlB,CAAC,MAAO,QAAQxF,SAAQ,SAAUyF,GAMhCjB,EAAeiB,GAAQ,SAAUC,UACxB,IAAIlB,GAAe,SAAUW,EAAgBC,GAElDR,QAAQa,GAAMC,EAAQC,KAAI,SAAUC,UAC3BA,GAAQA,EAAKjF,aAAsD,mBAAvCiF,EAAKjF,YAAYkE,kBAAyCe,EAAKjB,EAAIiB,MACpGb,KAAKI,EAAgBC,UAK/B,IACIS,EADO,GACM3B,SACb4B,EAAW,GAAGC,eACdC,EAAW/G,OAAOgH,eAClBC,EAAaJ,EAAS5B,SAQ1B,SAASiC,WAAWX,EAAGY,UACdC,SAASb,IAAwB,mBAAXA,EAAET,QAAyBqB,GAAoC,mBAAfZ,EAAC,OAShF,SAASV,YAAY5E,UACZ2F,EAAM1B,KAAKjE,GAAKkE,MAAM,GAAI,GAWnC,SAASkC,iBAAiB3G,EAAG4G,OACtB5G,GAAoB,WAAfY,UAAQZ,UACT,MAGL6G,EAAQR,EAASrG,OAEhB6G,SACU,OAAND,MAGLE,EAAOX,EAAS3B,KAAKqC,EAAO,gBAAkBA,EAAM7F,kBAEpC,mBAAT8F,EACI,OAANF,EAGLA,IAAME,IAIA,OAANF,GAAcL,EAAW/B,KAAKsC,KAAUP,EAAW/B,KAAKoC,IAI3C,mBAANA,GAAsD,iBAA3BE,EAAK5B,mBAAkC4B,EAAK5B,oBAAsB0B,EAAE1B,mBAa5G,SAAS6B,cAAcxG,YAEhBA,GAA4B,WAArB4E,YAAY5E,OAIZ8F,EAAS9F,IAOdoG,iBAAiBpG,EAAKjB,SA8B/B,SAASoH,SAASb,UACTA,GAAoB,WAAfjF,UAAQiF,GAStB,SAASmB,uBAAuBC,UACvBA,EAAiBlH,QAAQ,KAAM,MAAMA,QAAQ,MAAO,MAS7D,SAASmH,yBAAyBD,UACzBA,EAAiBlH,QAAQ,MAAO,KAAKA,QAAQ,MAAO,KAS7D,SAASoH,aAAatG,EAAKuG,MACT,KAAZA,SACKvG,MAGLwG,EAASD,EAAQE,QAAQ,QAEzBD,GAAU,EAAG,KACXE,EAAW1G,EAAIqG,yBAAyBE,EAAQ3C,MAAM,EAAG4C,iBACzC9D,IAAbgE,OAAyBhE,EAAY4D,aAAaI,EAAUH,EAAQ3C,MAAM4C,EAAS,WAGrFxG,EAAIqG,yBAAyBE,IAWtC,SAASI,aAAa3G,EAAKuG,EAASnF,MAClB,KAAZmF,SACKnF,MAGLoF,EAASD,EAAQE,QAAQ,YAEzBD,GAAU,EAELG,aADQ3G,EAAIqG,yBAAyBE,EAAQ3C,MAAM,EAAG4C,KAC/BD,EAAQ3C,MAAM4C,EAAS,GAAIpF,IAG3DpB,EAAIqG,yBAAyBE,IAAYnF,EAClCpB,GAcT,SAAS4G,OAAOxF,EAAOmD,EAAMsC,UACvBA,EACKtC,EAAOA,EAAKnD,GAASA,GAGzBA,GAAUA,EAAMmD,OACnBnD,EAAQgD,QAAQW,QAAQ3D,IAGnBmD,EAAOnD,EAAMmD,KAAKA,GAAQnD,GAGnC,IAAI1C,EAAOD,OAAOC,KACdF,EAAUD,MAAMC,QAChBsI,EAAS,GAAGvB,eACZwB,EAAgC,CAAC,OAAQ,WAAY,YAAa,uBAUtE,SAASC,OAAO/C,UACP,eACA,IAAIgD,EAAO,GAAIrG,EAAI,EAAGA,EAAImB,UAAUlB,OAAQD,IAC/CqG,EAAKrG,GAAKmB,UAAUnB,cAIbwD,QAAQW,QAAQd,EAAEpC,MAAMqC,KAAM+C,IACrC,MAAOC,UACA9C,QAAQU,OAAOoC,KAmD5B,SAASC,iBAAiBhI,EAAG4G,MACT,KAAd5G,EAAEiI,eACI,MAGNC,EAAKlI,EAAEiI,QAAQE,MAAM,QAAU,EAC/BC,EAAKxB,EAAEqB,QAAQE,MAAM,QAAU,SAE/BD,IACFA,EAAKA,EAAGxG,QAGN0G,IACFA,EAAKA,EAAG1G,QAGHwG,EAAKE,GAAM,EAAIF,EAAKE,EAAK,EAAIpI,EAAEiI,QAAUrB,EAAEqB,SAAW,EAAIjI,EAAEiI,QAAUrB,EAAEqB,QAGjF,IAAII,EAAuB,oBAIhBA,QAAQC,GACfpH,gBAAgB6D,KAAMsD,cAEjBC,QAAUA,OAGVC,qBAAuB,QACvBC,wBAA0B,QAG1BC,SAAW,QAGXC,MAAQ,UAhkBjB,SAASC,aAAavH,EAAawH,EAAYC,UACzCD,GAAYtH,kBAAkBF,EAAYH,UAAW2H,GACrDC,GAAavH,kBAAkBF,EAAayH,GACzCzH,EAulBPuH,CAAaN,QAAS,CAAC,CACrB/H,IAAK,YACL2B,MAAO,SAAS6G,UAAUjI,EAAKkI,EAAUC,EAAOC,GAC9CA,EAAOtG,eAAeA,eAAeA,eAAe,GAAIoC,KAAKuD,SAAUW,GAAO,GAAI,CAChFC,iBAAiB,QAEfC,EAAepE,KAAKqE,YAAYvI,EAAK,KAAMoI,UAE3C5J,EAAQ8J,GACHE,KAAKP,UAAUK,EAAa,GAAIJ,EAAUC,GAG5CG,EAAa/D,MAAK,SAAUM,UAC1B2D,KAAKP,UAAUpD,EAAKqD,EAAUC,QAYxC,CACD1I,IAAK,gBACL2B,MAAO,SAASqH,cAAczI,EAAKkI,EAAUC,EAAOC,UAC3ClE,KAAK+D,UAAUjI,EAAKkI,EAAUC,EAAOrG,eAAeA,eAAe,CACxE4G,oBAAoB,GACnBN,GAAO,GAAI,CACZO,MAAM,OAYT,CACDlJ,IAAK,iBACL2B,MAAO,SAASwH,eAAe5I,EAAKkI,EAAUC,EAAOC,UAC5ClE,KAAK+D,UAAUjI,EAAKkI,EAAUC,EAAOrG,eAAeA,eAAe,CACxE4G,oBAAoB,GACnBN,GAAO,GAAI,CACZO,MAAM,OAoBT,CACDlJ,IAAK,QACL2B,MAAO,SAASyH,MAAMC,EAAMC,EAASX,UACnCA,EAAOtG,eAAeA,eAAeA,eAAe,GAAIoC,KAAKuD,SAAUW,GAAO,GAAI,CAChFS,OAAO,IAEF3E,KAAK7E,OAAOmJ,KAAKK,MAAMC,EAAMC,GAAUX,KAW/C,CACD3I,IAAK,YACL2B,MAAO,SAAS4H,UAAUF,EAAMC,EAASX,UAChClE,KAAK2E,MAAMC,EAAMC,EAASjH,eAAeA,eAAe,CAC7D4G,oBAAoB,GACnBN,GAAO,GAAI,CACZO,MAAM,OAWT,CACDlJ,IAAK,aACL2B,MAAO,SAAS6H,WAAWH,EAAMC,EAASX,UACjClE,KAAK2E,MAAMC,EAAMC,EAASjH,eAAeA,eAAe,CAC7D4G,oBAAoB,GACnBN,GAAO,GAAI,CACZO,MAAM,OAeT,CACDlJ,IAAK,mBACL2B,MAAO,SAAS8H,iBAAiBlJ,EAAK1B,OAChC8J,EAAOrG,UAAUlB,OAAS,QAAsB6B,IAAjBX,UAAU,GAAmBA,UAAU,GAAK,UAC/EqG,EAAKe,iBAAkB,EAChBjF,KAAKqE,YAAYvI,EAAK1B,EAAU8J,KAUxC,CACD3I,IAAK,eACL2B,MAAO,SAASgI,aAAapJ,EAAK1B,OAC5B8J,EAAOrG,UAAUlB,OAAS,QAAsB6B,IAAjBX,UAAU,GAAmBA,UAAU,GAAK,UAC/EqG,EAAKiB,aAAc,EACZnF,KAAKqE,YAAYvI,EAAK1B,EAAU8J,KAaxC,CACD3I,IAAK,cACL2B,MAAO,SAASmH,YAAYvI,EAAK1B,EAAU8J,OAOrCkB,EAAgBtC,QAAO,SAAUuC,EAAKC,UACjC5C,OAAOxC,QAAQqF,IAAID,EAAarE,KAAI,SAAUuE,UAC5CA,EAAG,GAAGvF,OACV,SAAUwF,UACN/C,OAAOxC,QAAQqF,IAAIE,EAAYxE,IAAI6B,QAAO,SAAU4C,OACrDC,GAAQ,EACRC,EAAkB,GAGlBC,EAAwB5H,eADDqH,EAAaQ,OAAO,EAAG,GACe,GAG7DC,EAAU9H,eAFD4H,EAAsB,GAEE,GACjCxD,EAAU0D,EAAQ,GAClBC,EAASD,EAAQ,GACjB3L,EAAW2L,EAAQ,GACnBE,EAAYF,EAAQ,GACpBxK,EAAMwK,EAAQ,GACdG,EAAeH,EAAQ,GAEvBI,EAASC,aAAa/D,EAASqD,EAAYM,EAAQ5L,EAAUwL,GAAiB,EAAMM,GAEpFG,EAAmBzE,iBAAiBuE,EAAQrG,UApQ5D,SAASwG,QAAQC,EAAMlG,OACjBmG,EAASD,WAETC,GAAUA,EAAOnG,KACZmG,EAAOnG,KAAKA,GAGdA,EAAKmG,GAgQKF,EAAQ,cACTjE,GAAWgE,SACN3D,OAAOyD,EAAOlG,GAAG,SAAUwG,UAChCR,EAAU1K,GAAOkL,EACjBd,GAAQ,EACDP,EAAcC,EAAKO,SAG7B,SAAUc,UACPf,EAAce,GAEdrE,EACF4D,EAAU1K,GAAO4K,EAEjBd,EADSgB,EACHF,EAAOlG,EASPkG,EAGDf,EAAcC,EAAKO,aAExB,kBACGP,WA2BTZ,GAJJP,EAAOtG,eAAeA,eAAe,CACnC6G,MAAM,GACLzE,KAAKuD,SAAUW,IAEDO,KACbkC,EAAO3G,KACP2D,EAAQ,GACRiD,EAAU,GAEdC,EAAU,GAEVC,EAAmB,GAGfd,IAAS,WAAY9B,IAAOA,EAAK8B,OAEjCe,EADS7C,EACoB6C,oBAE7B1B,EAAMe,aAAa,GAAItK,EAAKkK,EAAQ5L,GAAY,GAAI0M,YAQ/CE,OAAO3B,OAGV4B,EAAY1M,OAAO2M,OAAOvD,MAE1BO,EAAKiB,mBACH8B,EAAUtK,OACLsK,EAAU,GAGZ3D,QAAQ6D,YAAY9B,MAGzB4B,EAAUtK,OAAQ,IAChBuH,EAAKe,uBACAjG,qBAAmB,IAAIoI,IAAIH,IAK/B5B,GAAQrD,cAAcqD,KAE3BzC,EAAOnD,KAAK4F,EAAK,UAQfA,EAAIgC,OAAS1D,EAPb0B,EAAM,CACJiC,EAAGjC,EACHgC,OAAQ,CACNC,EAAG3D,SAOAhC,SAAS0D,IAAQzC,EAAOnD,KAAK4F,EAAK,YAC3CA,EAAM,CACJiC,EAAGjC,EACHgC,QAAQ,WAIRnD,EAAKe,iBAIFI,WAGAkC,mCAAmCnN,EAAUoN,EAAYC,GAChElN,OAAOmN,OAAOtN,EAAUoN,OACpBG,EAAO9E,EAA8B5B,KAAI,SAAU2G,OACjDC,EAAMzN,EAASwN,iBACZxN,EAASwN,GACTC,KAGTJ,IACA5E,EAA8BvH,SAAQ,SAAUsM,EAAMlL,GACpDtC,EAASwN,GAAQD,EAAKjL,eAgBjB0J,aAAalD,EAAShG,EAAO8I,EAAQ5L,EAAUkL,EAAcwC,EAAyB5B,OACzFb,EACA0C,EAAe,GAEfC,EAAUnM,UAAQqB,GAElB+K,EAAclB,EAAsB,SAAUjL,OAC5CoM,EAAOhC,GAAgB9L,EAAS8N,MAAQ5E,QAAQ6D,YAAYjK,GAChE6J,EAAoBxM,OAAOmN,OAAO5L,GAAOiM,EAAc,CACrD7E,QAASA,EACThG,MAAOA,EACP8I,OAAQA,EACR5L,SAAUA,EACVkL,aAAcA,EACdwC,wBAAyBA,EACzBK,uBAAwBvG,iBAAiB1E,EAAO4C,IAC/C,CACDoI,KAAMA,MAEN,QAEA,CAAC,SAAU,UAAW,SAAU,aAAaE,SAASJ,eAC1CxJ,IAAVtB,GAAuBtC,OAAOyN,MAAMnL,IAAUA,IAAUtC,OAAO0N,mBAAqBpL,IAAUtC,OAAO2N,mBACvGlD,EAAMjL,EAASoO,SAAWtL,EAAQlC,QAAQkI,EAAShG,EAAO9C,EAAUkL,GAAc,EAAOwC,EAAyBG,MAEtG/K,IACV6K,EAAe,CACbS,SAAUnD,IAIdA,EAAMnI,EAGJ+K,GACFA,IAGK5C,KAGK,OAAVnI,SACE+K,GACFA,IAGK/K,KAGL8I,IAAW5L,EAASU,YAAcV,EAASc,qBAAuBgC,GAA4B,WAAnBrB,UAAQqB,GAAqB,KAGtGuL,EAAW7B,EAAQrE,QAAQrF,QAE3BuL,EAAW,UAMb9E,EAAMT,GAAW,IAEb+E,GACFA,EAAY,CACVS,cAAe7B,EAAQ4B,KAIpB,IAAM5B,EAAQ4B,IAbN,IAAXzC,IACFY,EAAQlJ,KAAKR,GACb2J,EAAQnJ,KAAKwF,QAsBfyF,EAPAC,EAAa5G,cAAc9E,GAC3B2L,EAAQvO,EAAQ4C,GAChBsL,GAEHI,GAAcC,MAAYlC,EAAKnD,qBAAqB7G,QAAUvC,EAASoO,WAAapO,EAASU,UAE9FoC,EAAQlC,QAAQkI,EAAShG,EAAO9C,EAAUkL,EAAcsD,GAAcC,EAAO,KAAMZ,MAG/EO,IAAatL,GACfmI,EAAMmD,EACNT,EAAe,CACbS,SAAUA,IAII,KAAZtF,GAAkBtB,iBAAiB1E,EAAO4C,IAC5CwF,EAAa5H,KAAK,CAACwF,EAAShG,EAAO8I,EAAQ5L,OAAUoE,OAAWA,EAAWpE,EAAS8N,OACpF7C,EAAMnI,GACG2L,GAAgC,WAAvBzO,EAASU,WAAiD,UAAvBV,EAASU,WAE9D6N,EAAQ,IAAItO,MAAM6C,EAAMP,QACxBoL,EAAe,CACbY,MAAOA,KAEC,CAAC,WAAY,UAAUP,SAASvM,UAAQqB,KAAa,WAAYA,GAAW0E,iBAAiB1E,EAAO4C,IAAoB8B,iBAAiB1E,EAAOgD,UAAa0B,iBAAiB1E,EAAO4L,gBAAgBF,GAAqC,WAAvBxO,EAASU,UAWtOuK,EAAMnI,GAVNyL,EAAQ,GAEJvO,EAASW,YACX4N,EAAMhM,OAASO,EAAMP,QAGvBoL,EAAe,CACbY,MAAOA,IAOTV,GACFA,IAGE/D,EAAKiB,mBACAwD,GAAStD,MAGbsD,SACItD,KAILjL,EAASU,UAAW,KAClBiO,EAAQ,SAASA,MAAMxN,OACrBiM,EAAa,CACf9L,QAASkH,EAAOnD,KAAKvC,EAAO3B,IAG9BgM,mCAAmCnN,EAAUoN,GAAY,eACnDwB,EAAK9F,GAAWA,EAAU,IAAM,IAAMjB,uBAAuB1G,GAE7DC,EAAM4K,aAAa4C,EAAI9L,EAAM3B,GAAM0N,QAAQjD,GAAS5L,EAAUkL,EAAcwC,GAE5ElG,iBAAiBpG,EAAKsE,GACxBwF,EAAa5H,KAAK,CAACsL,EAAIxN,EAAKyN,QAAQjD,GAAS5L,EAAUuO,EAAOpN,EAAKnB,EAAS8N,YAC3D1J,IAARhD,IACTmN,EAAMpN,GAAOC,WAMd,IAAID,KAAO2B,EACd6L,EAAMxN,GAGJ0M,GACFA,EAAY,CACViB,cAAc,EACdC,KAAK,SAQT3O,EAAK0C,GAAO5B,SAAQ,SAAUC,OACxByN,EAAK9F,GAAWA,EAAU,IAAM,IAAMjB,uBAAuB1G,GAKjEgM,mCAAmCnN,EAJlB,CACfsB,SAAS,IAG8C,eACnDF,EAAM4K,aAAa4C,EAAI9L,EAAM3B,GAAM0N,QAAQjD,GAAS5L,EAAUkL,EAAcwC,GAE5ElG,iBAAiBpG,EAAKsE,GACxBwF,EAAa5H,KAAK,CAACsL,EAAIxN,EAAKyN,QAAQjD,GAAS5L,EAAUuO,EAAOpN,EAAKnB,EAAS8N,YAC3D1J,IAARhD,IACTmN,EAAMpN,GAAOC,SAKfyM,GACFA,EAAY,CACVmB,eAAe,EACfD,KAAK,OAQP/O,EAASc,oBAAqB,SAC5BmO,EAAKnM,EAAMP,OAEX2M,EAAS,SAASA,OAAO5M,QACrBA,KAAKQ,GAAQ,KAEb8L,EAAK9F,GAAWA,EAAU,IAAM,IAAMxG,EAK1C6K,mCAAmCnN,EAJlB,CACfsB,SAAS,IAG8C,eACnDF,EAAM4K,aAAa4C,OAAIxK,EAAWyK,QAAQjD,GAAS5L,EAAUkL,EAAcwC,GAE3ElG,iBAAiBpG,EAAKsE,GACxBwF,EAAa5H,KAAK,CAACsL,EAAIxN,EAAKyN,QAAQjD,GAAS5L,EAAUuO,EAAOjM,EAAGtC,EAAS8N,YACzD1J,IAARhD,IACTmN,EAAMjM,GAAKlB,QAMVkB,EAAI,EAAGA,EAAI2M,EAAI3M,IACtB4M,EAAO5M,GAGLuL,GACFA,EAAY,CACVsB,wBAAwB,EACxBJ,KAAK,WAKJR,WAiDA3N,QAAQkI,EAAShG,EAAO9C,EAAUkL,EAAckE,EAAa1B,EAAyBG,WAEzFwB,EAAYD,EAAc7C,EAAKnD,qBAAuBmD,EAAKlD,wBAC3D/G,EAAI+M,EAAU9M,OAEXD,KAAK,KACNsH,EAAWyF,EAAU/M,MAErBsH,EAAS9J,KAAKgD,EAAO9C,GAAW,KAC9B8N,EAAOlE,EAASkE,QAEhBvB,EAAKjD,SAASwE,GAAO,KAOnBwB,EAAW/F,EAAMT,GAGrBS,EAAMT,GAAWwG,EAAW,CAACxB,GAAMyB,OAAOD,GAAYxB,SAGxD3N,OAAOmN,OAAOtN,EAAU,CACtB8N,KAAMA,EACNM,UAAU,KAGP/D,GAAST,EAAS4F,cAAkB5F,EAAShJ,SAU9CiN,GACFA,EAAY,CACV4B,WAAW,IAORzD,aAAalD,EAASc,EADTS,IAAST,EAAS4F,aAAe,UAAY,gBACZ1M,EAAO9C,GAAW4L,GAAU,WAAY5L,EAAUkL,EAAcwC,EAAyBI,KAlBxID,GACFA,EAAY,CACV6B,cAAc,IAIX1D,aAAalD,EAAShG,EAAO8I,GAAU,WAAY5L,EAAUkL,EAAcwC,EAAyBI,YAgB1GhL,SAGF4J,EAAiBnK,OAAS8H,GAAQP,EAAKM,mBAAqB,iBAC3D,IAAIlI,UAAU,mDAD6C,GAE7D4D,QAAQW,QAAQuE,EAAcC,EAAKyB,IAAmBzG,KAAK2G,SAAWvC,GAAQP,EAAKM,mBAAqB,iBACtG,IAAIlI,UAAU,mDADwF,GAM5G4H,EAAKC,iBAAmBM,EAAO,CAACuC,OAAO3B,IAAQZ,EAAOuC,OAAO3B,GAAOnF,QAAQW,QAAQmG,OAAO3B,MAU9F,CACD9J,IAAK,kBACL2B,MAAO,SAAS6M,gBAAgBjO,EAAK1B,EAAU8J,UACtClE,KAAKqE,YAAYvI,EAAK1B,EAAUwD,eAAeA,eAAe,CACnE4G,oBAAoB,GACnBN,GAAO,GAAI,CACZO,MAAM,OAUT,CACDlJ,IAAK,mBACL2B,MAAO,SAAS8M,iBAAiBlO,EAAK1B,EAAU8J,UACvClE,KAAKqE,YAAYvI,EAAK1B,EAAUwD,eAAeA,eAAe,CACnE4G,oBAAoB,GACnBN,GAAO,GAAI,CACZO,MAAM,OAeT,CACDlJ,IAAK,SACL2B,MAAO,SAAS/B,OAAOW,EAAKoI,OACtBP,EAAQ7H,GAAOA,EAAIuL,WAElB1D,SACI7H,MAKK,IAAV6H,SACK7H,EAAIwL,MAOT7C,GAJJP,EAAOtG,eAAeA,eAAe,CACnC6G,MAAM,GACLzE,KAAKuD,SAAUW,IAEAO,KACdwF,EAAqB,GACrB7P,EAAW,GACX8P,GAAe,EAIfvG,EAAM2D,GAAKtF,cAAc2B,EAAM2D,KACjCxL,EAAMA,EAAIwL,EACV3D,EAAQA,EAAM2D,EACd4C,GAAe,OAGbvD,EAAO3G,cAgBFmK,eAAejC,EAAM1M,OAGxBqJ,EADQ5G,eADD0I,EAAKjD,SAASwE,IAAS,GACD,GACb,OAEfrD,QACG,IAAIuF,MAAM,sBAAwBlC,UAOtCzD,KAAU,WAAYI,GAEjBrJ,EAGFqJ,EAAQJ,GAAQI,EAAQ1J,OAAS,UAAYsJ,GAAQI,EAAQwF,YAAc,cAAgB,UAAU7O,EAAKpB,OAiG/GkQ,EAAkB,YAqGbC,eAAeC,UACf5I,iBAAiB4I,EAAOC,QAAajM,EAAYgM,MAItDnF,EADAqF,WAlMKC,yBAGHC,EAAmB,MACvBrQ,OAAOsQ,QAAQlH,GAAOrI,SAAQ,SAAUwP,OAClCC,EAAQ9M,eAAe6M,EAAO,GAC9B5H,EAAU6H,EAAM,GAChB7C,EAAO6C,EAAM,GAEJ,MAAT7C,MAUDyB,OAAOzB,GAAM5M,SAAQ,SAAU4M,GAEpBjK,eADA0I,EAAKjD,SAASwE,IAAS,CAAC,KAAM,IACR,GAChB,GAAG8C,QAOrBJ,EAAiBlN,KAAK,CACpBwF,QAASA,EACTgF,KAAMA,WAEDvE,EAAMT,UAIZ0H,EAAiBjO,cAWfiO,EAAiBK,KAAKhI,kBAAkBiI,QAAO,SAASC,QAAQT,EAAwBU,OACzFlI,EAAUkI,EAAMlI,QAChBgF,EAAOkD,EAAMlD,QAEbzG,WAAWiJ,UACNA,EAAuBrK,MAAK,SAAU7E,UACpC2P,QAAQ3P,EAAK,CAClB0H,QAASA,EACTgF,KAAMA,WAMR1M,EAAM4G,aAAatG,EAAKoH,MAGxBtB,iBAFJpG,EAAM2O,eAAejC,EAAM1M,GAEDsE,UACjBtE,EAAI6E,MAAK,SAAUS,OACpBuK,EAAS5I,aAAa3G,EAAKoH,EAASpC,GAEpCuK,IAAWvK,IACbhF,EAAMuP,UAORA,EAAS5I,aAAa3G,EAAKoH,EAAS1H,GAEpC6P,IAAW7P,IACbM,EAAMuP,UAIP7M,GA8GwBmM,UAGzB/I,iBAAiB8I,EAAwB5K,GAC3CuF,EAAMqF,EAAuBrK,MAAK,kBACzBvE,MAGTuJ,WAtGOiG,QAAQpI,EAAShG,EAAOV,EAAQmM,EAAOpN,OAC1C2O,GAA4B,WAAZhH,OAIhBgF,EAAOvE,EAAMT,GACb2F,EAAQvO,EAAQ4C,MAEhB2L,GAAS7G,cAAc9E,GAAQ,KAE7BqO,EAAS1C,EAAQ,IAAIxO,MAAM6C,EAAMP,QAAU,OAG/CnC,EAAK0C,GAAO5B,SAAQ,SAAUZ,OACxBc,EAAM8P,QAAQpI,GAAWA,EAAU,IAAM,IAAMjB,uBAAuBvH,GAAIwC,EAAMxC,GAAI8B,GAAU+O,EAAQA,EAAQ7Q,GAE9G8Q,EAAM,SAASA,IAAI1K,UACjBc,iBAAiBd,EAAG2J,GACtBc,EAAO7Q,QAAK8D,OACGA,IAANsC,IACTyK,EAAO7Q,GAAKoG,GAGPA,GAGLc,iBAAiBpG,EAAKsE,GACxBwK,EAAgB5M,KAAKlC,EAAI6E,MAAK,SAAUgF,UAC/BmG,EAAInG,OAGbmG,EAAIhQ,MAGR0B,EAAQqO,EAEDtB,EAAmBtN,QAAQ,KAC5B8O,EAAuBxN,eAAegM,EAAmB,GAAI,GAC7DyB,EAAUD,EAAqB,GAC/BpJ,EAAUoJ,EAAqB,GAC/BE,EAAUF,EAAqB,GAC/B/Q,EAAI+Q,EAAqB,GAEzBjQ,EAAM4G,aAAasJ,EAASrJ,WAGpB7D,IAARhD,QACFmQ,EAAQjR,GAAKc,EAKfyO,EAAmBnE,OAAO,EAAG,QAI5BoC,SACIhL,KAGI,MAATgL,EAAc,KACZ0D,EAAOxJ,aAAa5F,EAAQU,EAAMwC,MAAM,gBAE/BlB,IAAToN,GAEF3B,EAAmBvM,KAAK,CAAClB,EAAQU,EAAMwC,MAAM,GAAIiJ,EAAOpN,IAGnDqQ,QAIF,GAAGjC,OAAOzB,GAAMgD,QAAO,SAASC,QAAQ3P,EAAKqQ,UAC9CjK,iBAAiBpG,EAAKsE,GACjBtE,EAAI6E,MAAK,SAAUS,UAEjBqK,QAAQrK,EAAG+K,MAIf1B,eAAe0B,EAAKrQ,KAC1B0B,IAqBGoO,CAAQ,GAAIxP,EAAK,MAEnBwO,EAAgB3N,SAElB0I,EAAMvF,EAAee,QAAQwE,GAAKhF,MAAK,SAAUyL,UACxChM,EAAeyF,IAAI,CAC1BuG,GAAGnC,OAAOW,OACTjK,MAAK,SAAU0L,UACJ9N,eAAe8N,EAAO,GACpB,QAObtK,WAAW4D,GAAOZ,GAAQP,EAAKM,mBAAqB,iBACnD,IAAIlI,UAAU,mDADqC,GAErDsF,iBAAiByD,EAAKvF,GAAkBuF,EAAIpF,EAAEI,KAAKkK,gBAAkBlF,GAAOZ,GAAQP,EAAKM,mBAAqB,iBAC5G,IAAIlI,UAAU,mDAD8F,GAE9GmI,EAAO8F,eAAelF,GAAOnF,QAAQW,QAAQ0J,eAAelF,MASnE,CACD9J,IAAK,aACL2B,MAAO,SAAS8O,WAAWlQ,EAAKoI,UACvBlE,KAAK7E,OAAOW,EAAK8B,eAAeA,eAAe,CACpD4G,oBAAoB,GACnBN,GAAO,GAAI,CACZO,MAAM,OAST,CACDlJ,IAAK,cACL2B,MAAO,SAASmN,YAAYvO,EAAKoI,UACxBlE,KAAK7E,OAAOW,EAAK8B,eAAeA,eAAe,CACpD4G,oBAAoB,GACnBN,GAAO,GAAI,CACZO,MAAM,OAiBT,CACDlJ,IAAK,WACL2B,MAAO,SAAS+O,SAASC,EAAchI,UACrCA,EAAOA,GAAQ,MACZyF,OAAOuC,GAAc5Q,SAAQ,SAAS6Q,EAAEC,OACrC5L,EAAQR,QAGR1F,EAAQ8R,UACHA,EAASnL,KAAI,SAAUoL,UACrBF,EAAE1M,KAAKe,EAAO6L,MAIzBD,GAAY5R,EAAK4R,GAAU9Q,SAAQ,SAAUgR,MAC5B,MAAXA,QACI,IAAIhQ,UAAU,wEACf,GAAIgH,QAAQiJ,WAAWnE,SAASkE,SAC/B,IAAIhQ,UAAU,0DAGlBkQ,EAAOJ,EAASE,GAChB7C,EAAY+C,GAAQA,EAAKvS,iBAAmB+F,KAAKwD,qBAAuBxD,KAAKyD,wBAC7EgJ,EAAmBhD,EAAUlM,QAAO,SAAUuO,UACzCA,EAAE5D,OAASoE,QAGhBG,EAAiB9P,SAEnB8M,EAAU3D,OAAO2D,EAAUlH,QAAQkK,EAAiB,IAAK,UAClDzM,KAAK0D,SAAS4I,UACdtM,KAAK2D,MAAM2I,IAGA,mBAATE,EAAqB,KAE1BE,EAAQF,EACZA,EAAO,CACLtS,KAAM,SAASA,KAAKC,UACXA,GAAKA,EAAE8B,cAAgByQ,GAEhC1R,QAAS,SAASA,QAAQb,UACjByD,eAAe,GAAIzD,IAE5BgB,OAAQ,SAASA,OAAOhB,UACfI,OAAOmN,OAAOnN,OAAOoS,OAAOD,EAAMxQ,WAAY/B,UAGpD,GAAIG,EAAQkS,GAAO,KAEpBI,EAAS3O,eADDuO,EACuB,GAKnCA,EAAO,CACLtS,KALS0S,EAAO,GAMhB5R,QALY4R,EAAO,GAMnBzR,OALWyR,EAAO,OASjBJ,GAASA,EAAKtS,UAIf2S,EAAc,CAChB3E,KAAMoE,EACNpS,KAAMsS,EAAKtS,KAAK4S,KAAKN,IAGnBA,EAAKxR,UACP6R,EAAY7R,QAAUwR,EAAKxR,QAAQ8R,KAAKN,IAGtCA,EAAK5C,eACPiD,EAAYjD,aAAe4C,EAAK5C,aAAakD,KAAKN,QAGhDO,EAAiC,iBAAlB7I,EAAK8I,SAAwB9I,EAAK8I,SAAW9I,EAAK8I,SAAW,EAAIpS,OAAO2N,qBAEvFiE,EAAKvS,sBACFuJ,qBAAqBsC,OAAOiH,EAAO,EAAGF,QAEtCpJ,wBAAwBqC,OAAOiH,EAAO,EAAGF,GAI5CL,EAAKrR,QAAUqR,EAAKnC,YAAa,KAC/B4C,EAAa,GAEbT,EAAKrR,SACP8R,EAAW9R,OAASqR,EAAKrR,OAAO2R,KAAKN,IAGnCA,EAAKnC,cACP4C,EAAW5C,YAAcmC,EAAKnC,YAAYyC,KAAKN,SAG5C9I,SAAS4I,GAAU,CAACW,EAAY,CACnCjC,MAAOwB,EAAKvS,wBAKX0J,MAAM2I,GAAUE,KACpBxM,QACFA,MACIA,SAIJsD,QAtsCkB,GAgtCvBmH,EAAY,SAASA,YACvBtO,gBAAgB6D,KAAMyK,YAIxBA,EAAUtK,kBAAoB,mBAG9BmD,EAAQmH,UAAYA,EAEpBnH,EAAQpD,QAAUJ,EAElBwD,EAAQ7B,WAAaA,WACrB6B,EAAQlD,YAAcA,YACtBkD,EAAQ1B,iBAAmBA,iBAC3B0B,EAAQ3B,SAAWA,SACnB2B,EAAQtB,cAAgBA,cACxBsB,EAAQ4J,aA96CR,SAASA,aAAa1R,OACfA,GAA4B,WAArB4E,YAAY5E,UACf,MAGLsG,EAAQR,EAAS9F,UAEhBsG,IAKEF,iBAAiBpG,EAAKjB,SAAW2S,aAAapL,KAm6CvDwB,EAAQrB,uBAAyBA,uBACjCqB,EAAQnB,yBAA2BA,yBACnCmB,EAAQlB,aAAeA,aACvBkB,EAAQ6D,YAj1CR,SAASA,YAAYjK,UACF,OAAVA,EAAiB,OAAS7C,MAAMC,QAAQ4C,GAAS,QAAUrB,UAAQqB,IAi1C5EoG,EAAQiJ,WAAa,CAAC,OAAQ,UAAW,SAAU,SAAU,QAAS,UCjzDtE,IAAMY,EAAQ,CACVA,MAAO,CACHjT,mBAAMC,EAAGC,eACe,IAAND,IACTC,EAASsB,WAAa,YAAatB,KAE5CY,yBAASW,UAAY,GACrBR,uBAAQS,UAGG,IAAI0H,EAAQmH,4oCCT/B,IAAM2C,EAAmB,CAErBC,aAAc,CACVnT,mBAAMC,SACgC,WAA3BmJ,EAAQlD,YAAYjG,IAAgC,WAAb0B,QAAO1B,IAEzDa,yBAASY,UAAYjB,OAAOiB,IAC5BT,uBAAQS,UAAY,IAAIjB,OAAOiB,KAGnC0R,cAAe,CACXpT,mBAAMC,SACgC,YAA3BmJ,EAAQlD,YAAYjG,IACV,WAAb0B,QAAO1B,IAEfa,yBAAS6G,UAAYoH,QAAQpH,IAC7B1G,uBAAQ0G,UAEG,IAAIoH,QAAQpH,KAI3B0L,aAAc,CACVrT,mBAAMC,SACgC,WAA3BmJ,EAAQlD,YAAYjG,IAAgC,WAAb0B,QAAO1B,IAEzDa,yBAASW,UAAYf,OAAOe,IAC5BR,uBAAQQ,UAAY,IAAIf,OAAOe,MC5BjC6R,EAAiB,CCJX,CACRC,IAAK,CACDvT,mBAAMC,UAAYS,OAAOyN,MAAMlO,IAC/Ba,yBAASW,SAAY,OACrBR,uBAAQS,UAAYhB,OAAO8S,OCJlB,CACbC,SAAU,CACNzT,mBAAMC,UAAYA,IAAMS,OAAO2N,mBAC/BvN,yBAASW,SAAY,YACrBR,uBAAQS,UAAYhB,OAAO2N,qBCJV,CACrBqF,iBAAkB,CACd1T,mBAAMC,UAAYA,IAAMS,OAAO0N,mBAC/BtN,yBAASW,SAAY,aACrBR,uBAAQS,UAAYhB,OAAO0N,sBCF7BuF,EAAO,CACTA,KAAM,CACF3T,mBAAMC,SAAuC,SAA3BmJ,EAAQlD,YAAYjG,IACtCa,yBAAS8S,OACCC,EAAOD,EAAGE,iBACZpT,OAAOyN,MAAM0F,GACN,MAEJA,GAEX5S,uBAAQ4S,SACS,QAATA,EACO,IAAIE,KAAKrT,OAAO8S,KAEpB,IAAIO,KAAKF,MCdtBG,EAAQ,CACVA,MAAO,CACHhU,mBAAMC,SAAuC,UAA3BmJ,EAAQlD,YAAYjG,IACtCa,kCACW,CAAC2E,OADFA,KACQwO,UADFA,UAGhBhT,8BAASwE,IAAAA,KAAMwO,IAAAA,QACLnL,EAAI,IAAIoH,MAAM+D,UACpBnL,EAAErD,KAAOA,EACFqD,KCPboL,EAA0B,oBAATC,KAAuBC,OAASD,KAEjDE,EAAS,GAEf,CACI,YACA,aACA,cACA,iBACA,YACA,WACA,iBACFjT,SAAQ,SAACkT,OACDC,EAAWL,EAAQI,GACrBC,IACAF,EAAOC,EAAQE,eAAiB,CAC5BxU,mBAAMC,UAAYmJ,EAAQ1B,iBAAiBzH,EAAGsU,IAC9CzT,yBAASgI,UAAYA,EAAEmL,SACvBhT,uBAAQgT,UAAkB,IAAIM,EAASN,SCXnD,ICTA,IAAMQ,EAAS,CACXA,OAAQ,CACJzU,mBAAMC,SAAuC,WAA3BmJ,EAAQlD,YAAYjG,IACtCa,yBAAS4T,SACE,CACH9Q,OAAQ8Q,EAAK9Q,OACb+Q,OAAQD,EAAKN,OAAS,IAAM,KACvBM,EAAKE,WAAa,IAAM,KACxBF,EAAKG,UAAY,IAAM,KACvBH,EAAKI,OAAS,IAAM,KACpBJ,EAAKK,QAAU,IAAM,MAGlC9T,8BAAS2C,IAAAA,OAAQ+Q,IAAAA,aAAiB,IAAIK,OAAOpR,EAAQ+Q,MCbvD5N,EAAM,CACRA,IAAK,CACD/G,mBAAMC,SAAuC,QAA3BmJ,EAAQlD,YAAYjG,IACtCa,yBAASmU,6BAAiBA,EAAGtE,YAC7B1P,uBAAQ0P,UAAkB,IAAIuE,IAAIvE,MCJpCW,EAAM,CACRA,IAAK,CACDtR,mBAAMC,SAAuC,QAA3BmJ,EAAQlD,YAAYjG,IACtCa,yBAASqU,6BACMA,EAAGnI,WAElB/L,uBAAQ+L,UAAiB,IAAIE,IAAIF,MHDrCoI,EAAQ,mEAERC,EAAS,IAAIC,WAAW,KAEnB9S,EAAI,EAAGA,EAAI4S,EAAM3S,OAAQD,IAChC6S,EAAOD,EAAMG,WAAW/S,IAAMA,EAUhC,IAAIgT,EAAS,SAASA,OAAOC,EAAaC,EAAYC,GAChDA,MAAAA,IACFA,EAAQF,EAAYG,oBAGlBC,EAAQ,IAAIP,WAAWG,EAAaC,GAAc,EACtDC,GACIjQ,EAAMmQ,EAAMpT,OACZqT,EAAS,GAEJtR,EAAK,EAAGA,EAAKkB,EAAKlB,GAAM,EAC/BsR,GAAUV,EAAMS,EAAMrR,IAAO,GAC7BsR,GAAUV,GAAmB,EAAZS,EAAMrR,KAAY,EAAIqR,EAAMrR,EAAK,IAAM,GACxDsR,GAAUV,GAAuB,GAAhBS,EAAMrR,EAAK,KAAY,EAAIqR,EAAMrR,EAAK,IAAM,GAC7DsR,GAAUV,EAAsB,GAAhBS,EAAMrR,EAAK,WAGzBkB,EAAM,GAAM,EACdoQ,EAASA,EAAOtQ,MAAM,GAAI,GAAK,IACtBE,EAAM,GAAM,IACrBoQ,EAASA,EAAOtQ,MAAM,GAAI,GAAK,MAG1BsQ,GAOLC,EAAS,SAASA,OAAOD,OAIvBE,EAAUC,EAAUC,EAAUC,EAH9BzQ,EAAMoQ,EAAOrT,OACb2T,EAA+B,IAAhBN,EAAOrT,OACtBsD,EAAI,EAG0B,MAA9B+P,EAAOA,EAAOrT,OAAS,KACzB2T,IAEkC,MAA9BN,EAAOA,EAAOrT,OAAS,IACzB2T,aAIAX,EAAc,IAAI7G,YAAYwH,GAC9BP,EAAQ,IAAIP,WAAWG,GAElBY,EAAM,EAAGA,EAAM3Q,EAAK2Q,GAAO,EAClCL,EAAWX,EAAOS,EAAOP,WAAWc,IACpCJ,EAAWZ,EAAOS,EAAOP,WAAWc,EAAM,IAC1CH,EAAWb,EAAOS,EAAOP,WAAWc,EAAM,IAC1CF,EAAWd,EAAOS,EAAOP,WAAWc,EAAM,IAC1CR,EAAM9P,KAAOiQ,GAAY,EAAIC,GAAY,EACzCJ,EAAM9P,MAAmB,GAAXkQ,IAAkB,EAAIC,GAAY,EAChDL,EAAM9P,MAAmB,EAAXmQ,IAAiB,EAAe,GAAXC,SAG9BV,GI5EHA,EAAc,CAChBA,YAAa,CACTzV,mBAAMC,SAAuC,gBAA3BmJ,EAAQlD,YAAYjG,IACtCa,yBAAS6G,EAAGzH,GACHA,EAASoW,UACVpW,EAASoW,QAAU,QAEjBC,EAAQrW,EAASoW,QAAQjO,QAAQV,UACnC4O,GAAS,EACF,CAACA,MAAAA,IAEZrW,EAASoW,QAAQ9S,KAAKmE,GACf6N,EAAO7N,KAElB1G,uBAAQuV,EAAKtW,MACJA,EAASoW,UACVpW,EAASoW,QAAU,IAEJ,WAAf3U,QAAO6U,UACAtW,EAASoW,QAAQE,EAAID,WAE1BE,EAASV,EAAOS,UACtBtW,EAASoW,QAAQ9S,KAAKiT,GACfA,KCrBbvC,EAA0B,oBAATC,KAAuBC,OAASD,KAEjDuC,EAAc,GACpB,CACI,YACA,aACA,oBACA,aACA,cACA,aACA,cACA,eACA,gBACFtV,SAAQ,SAAUuV,OACVC,EAAUD,EACVE,EAAa3C,EAAQ0C,GAEtBC,IAGLH,EAAYC,EAASnC,eAAiB,CAClCxU,mBAAMC,UAAYmJ,EAAQlD,YAAYjG,KAAO2W,GAC7C9V,2BAA0CZ,OAAhCuW,IAAAA,OAAQf,IAAAA,WAAoBoB,IAARrU,OACrBvC,EAASoW,UACVpW,EAASoW,QAAU,QAEjBC,EAAQrW,EAASoW,QAAQjO,QAAQoO,UACnCF,GAAS,EACF,CAACA,MAAAA,EAAOb,WAAAA,EAAYjT,OAAQqU,IAEvC5W,EAASoW,QAAQ9S,KAAKiT,GACf,CACHM,QAASvB,EAAOiB,GAChBf,WAAAA,EACAjT,OAAQqU,KAGhB7V,uBAAQ+V,EAAQ9W,GACPA,EAASoW,UACVpW,EAASoW,QAAU,QAGnBG,EADGf,EAA2CsB,EAA3CtB,WAAoBhQ,EAAuBsR,EAA/BvU,OAAasU,EAAkBC,EAAlBD,QAASR,EAASS,EAATT,YAErC,UAAWS,EACXP,EAASvW,EAASoW,QAAQC,IAE1BE,EAASV,EAAOgB,GAChB7W,EAASoW,QAAQ9S,KAAKiT,IAEnB,IAAII,EAAWJ,EAAQf,EAAYhQ,SCnDtD,IAAMuR,EAAW,CACbA,SAAU,CACNjX,mBAAMC,SAAuC,aAA3BmJ,EAAQlD,YAAYjG,IACtCa,2BAA2CZ,OAAjCuW,IAAAA,OAAQf,IAAAA,WAAYE,IAAAA,WACrB1V,EAASoW,UACVpW,EAASoW,QAAU,QAEjBC,EAAQrW,EAASoW,QAAQjO,QAAQoO,UACnCF,GAAS,EACF,CAACA,MAAAA,EAAOb,WAAAA,EAAYE,WAAAA,IAE/B1V,EAASoW,QAAQ9S,KAAKiT,GACf,CACHM,QAASvB,EAAOiB,GAChBf,WAAAA,EACAE,WAAAA,KAGR3U,uBAAQ+V,EAAQ9W,GACPA,EAASoW,UACVpW,EAASoW,QAAU,QAGnBG,EADGf,EAA0CsB,EAA1CtB,WAAYE,EAA8BoB,EAA9BpB,WAAYmB,EAAkBC,EAAlBD,QAASR,EAASS,EAATT,YAEpC,UAAWS,EACXP,EAASvW,EAASoW,QAAQC,IAE1BE,EAASV,EAAOgB,GAChB7W,EAASoW,QAAQ9S,KAAKiT,IAEnB,IAAIS,SAAST,EAAQf,EAAYE,MC/B9CuB,EAAe,CACjBnX,mBAAMC,UAAYmJ,EAAQ1B,iBAAiBzH,EAAGmX,KAAKC,WACnDvW,yBAASwW,UAAYA,EAAEC,mBACvBtW,uBAAQoI,UAAkB,IAAI+N,KAAKC,SAAShO,EAAQmO,OAAQnO,KAG1DoO,EAAqB,CACvBzX,mBAAMC,UAAYmJ,EAAQ1B,iBAAiBzH,EAAGmX,KAAKM,iBACnD5W,yBAAS6W,UAAcA,EAAIJ,mBAC3BtW,uBAAQoI,UACG,IAAI+N,KAAKM,eAAerO,EAAQmO,OAAQnO,KAIjDuO,EAAmB,CACrB5X,mBAAMC,UAAYmJ,EAAQ1B,iBAAiBzH,EAAGmX,KAAKS,eACnD/W,yBAASgX,UAAaA,EAAGP,mBACzBtW,uBAAQoI,UAAkB,IAAI+N,KAAKS,aAAaxO,EAAQmO,OAAQnO,KCjB9D0O,EAAS,CACXA,OAAQ,CACJ/X,mBAAMC,SACkB,iBAANA,GAElBa,yBAASW,UAAYhB,OAAOgB,IAC5BR,uBAAQS,UAAYsW,OAAOtW,MCL7BuW,EAAe,CACjBA,aAAc,CACVjY,mBAAMC,SACkB,WAAb0B,QAAO1B,IAAkBmJ,EAAQ1B,iBAAiBzH,EAAG+X,SAEhElX,yBAASW,UAAYhB,OAAOgB,IAC5BR,uBAAQS,UAGG,IAAIrB,OAAO2X,OAAOtW,OCqB/BwW,EAAS,CACXjF,EAEAnT,EAAmBoT,EAAkBI,EACrCK,EAAMK,EAAOK,EAAQI,GACvBhF,OAGiB,mBAARyF,IAAqBnO,EAAM,GAEnB,mBAARmG,IAAqBoE,EAAM,GAEX,mBAAhB1C,YAA6B6G,EAAc,GAE5B,mBAAfH,WAA4BoB,EAAc,GAE7B,mBAAbQ,SAA0BD,EAAW,GAE5B,oBAATG,KH7BO,CACdD,aAAAA,EACAM,mBAAAA,EACAG,iBAAAA,GG0B0C,GAGxB,oBAAXI,OAAyB,CAACD,EAAQE,GAAgB,IClDvD/D,EAA0B,oBAATC,KAAuBC,OAASD,KAGjDgE,EAAsB,SAC5B,CACI,YACA,aACA,oBACA,aACA,cACA,aACA,cACA,eACA,gBACF/W,SAAQ,SAAUuV,OACVC,EAAUD,EACVE,EAAa3C,EAAQyC,GAEtBE,IAGLsB,EAAoBxB,EAASnC,eAAiB,CAC1CxU,mBAAMC,UAAYmJ,EAAQlD,YAAYjG,KAAO2W,GAC7C9V,yBAASC,UACoB,IAAjBA,EAAE2U,YACN3U,EAAE6U,aAAe7U,EAAE0V,OAAOb,WACxB7U,EAKAA,EAAEyE,MAAM,IAAIiR,QAEtBxV,uBAAQmX,SAOgC,gBAA7BhP,EAAQlD,YAAYkS,GACrB,IAAIvB,EAAWuB,GACfA,QC3CD,CACbC,EAEA,CAAC5C,YAAa,MAEd0C"}