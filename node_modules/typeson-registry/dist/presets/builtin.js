!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):((e="undefined"!=typeof globalThis?globalThis:e||self).Typeson=e.Typeson||{},e.Typeson.presets=e.Typeson.presets||{},e.Typeson.presets.builtin=t())}(this,(function(){"use strict";var e=[{arrayNonindexKeys:{testPlainObjects:!0,test:function test(e,t){return!!Array.isArray(e)&&(Object.keys(e).some((function(e){return String(Number.parseInt(e))!==e}))&&(t.iterateIn="object",t.addLength=!0),!0)},replace:function replace(e,t){return t.iterateUnsetNumeric=!0,e},revive:function revive(e){if(Array.isArray(e))return e;var t=[];return Object.keys(e).forEach((function(r){var n=e[r];t[r]=n})),t}}},{sparseUndefined:{test:function test(e,t){return void 0===e&&!1===t.ownKeys},replace:function replace(e){return 0},revive:function revive(e){}}}];function _typeof$1(e){return(_typeof$1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function _typeof(e){return typeof e}:function _typeof(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function _defineProperty(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){_defineProperty(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function _slicedToArray(e,t){return function _arrayWithHoles(e){if(Array.isArray(e))return e}(e)||function _iterableToArrayLimit(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=e[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!t||r.length!==t);n=!0);}catch(e){o=!0,i=e}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}(e,t)||_unsupportedIterableToArray$1(e,t)||function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _toConsumableArray$1(e){return function _arrayWithoutHoles$1(e){if(Array.isArray(e))return _arrayLikeToArray$1(e)}(e)||function _iterableToArray$1(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||_unsupportedIterableToArray$1(e)||function _nonIterableSpread$1(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _unsupportedIterableToArray$1(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray$1(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray$1(e,t):void 0}}function _arrayLikeToArray$1(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var t=function TypesonPromise(e){_classCallCheck(this,TypesonPromise),this.p=new Promise(e)};t.__typeson__type__="TypesonPromise","undefined"!=typeof Symbol&&(t.prototype[Symbol.toStringTag]="TypesonPromise"),t.prototype.then=function(e,r){var n=this;return new t((function(t,o){n.p.then((function(r){t(e?e(r):r)})).catch((function(e){return r?r(e):Promise.reject(e)})).then(t,o)}))},t.prototype.catch=function(e){return this.then(null,e)},t.resolve=function(e){return new t((function(t){t(e)}))},t.reject=function(e){return new t((function(t,r){r(e)}))},["all","race"].forEach((function(e){t[e]=function(r){return new t((function(t,n){Promise[e](r.map((function(e){return e&&e.constructor&&"TypesonPromise"===e.constructor.__typeson__type__?e.p:e}))).then(t,n)}))}}));var r={}.toString,n={}.hasOwnProperty,o=Object.getPrototypeOf,i=n.toString;function isThenable(e,t){return isObject(e)&&"function"==typeof e.then&&(!t||"function"==typeof e.catch)}function toStringTag(e){return r.call(e).slice(8,-1)}function hasConstructorOf(e,t){if(!e||"object"!==_typeof$1(e))return!1;var r=o(e);if(!r)return null===t;var a=n.call(r,"constructor")&&r.constructor;return"function"!=typeof a?null===t:t===a||(null!==t&&i.call(a)===i.call(t)||"function"==typeof t&&"string"==typeof a.__typeson__type__&&a.__typeson__type__===t.__typeson__type__)}function isPlainObject(e){return!(!e||"Object"!==toStringTag(e))&&(!o(e)||hasConstructorOf(e,Object))}function isObject(e){return e&&"object"===_typeof$1(e)}function escapeKeyPathComponent(e){return e.replace(/~/g,"~0").replace(/\./g,"~1")}function unescapeKeyPathComponent(e){return e.replace(/~1/g,".").replace(/~0/g,"~")}function getByKeyPath(e,t){if(""===t)return e;var r=t.indexOf(".");if(r>-1){var n=e[unescapeKeyPathComponent(t.slice(0,r))];return void 0===n?void 0:getByKeyPath(n,t.slice(r+1))}return e[unescapeKeyPathComponent(t)]}function setAtKeyPath(e,t,r){if(""===t)return r;var n=t.indexOf(".");return n>-1?setAtKeyPath(e[unescapeKeyPathComponent(t.slice(0,n))],t.slice(n+1),r):(e[unescapeKeyPathComponent(t)]=r,e)}function _await(e,t,r){return r?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var a=Object.keys,c=Array.isArray,u={}.hasOwnProperty,s=["type","replaced","iterateIn","iterateUnsetNumeric"];function _async(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}function nestedPathsFirst(e,t){if(""===e.keypath)return-1;var r=e.keypath.match(/\./g)||0,n=t.keypath.match(/\./g)||0;return r&&(r=r.length),n&&(n=n.length),r>n?-1:r<n?1:e.keypath<t.keypath?-1:e.keypath>t.keypath}var f=function(){function Typeson(e){_classCallCheck(this,Typeson),this.options=e,this.plainObjectReplacers=[],this.nonplainObjectReplacers=[],this.revivers={},this.types={}}return function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),e}(Typeson,[{key:"stringify",value:function stringify(e,t,r,n){n=_objectSpread2(_objectSpread2(_objectSpread2({},this.options),n),{},{stringification:!0});var o=this.encapsulate(e,null,n);return c(o)?JSON.stringify(o[0],t,r):o.then((function(e){return JSON.stringify(e,t,r)}))}},{key:"stringifySync",value:function stringifySync(e,t,r,n){return this.stringify(e,t,r,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},n),{},{sync:!0}))}},{key:"stringifyAsync",value:function stringifyAsync(e,t,r,n){return this.stringify(e,t,r,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},n),{},{sync:!1}))}},{key:"parse",value:function parse(e,t,r){return r=_objectSpread2(_objectSpread2(_objectSpread2({},this.options),r),{},{parse:!0}),this.revive(JSON.parse(e,t),r)}},{key:"parseSync",value:function parseSync(e,t,r){return this.parse(e,t,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},r),{},{sync:!0}))}},{key:"parseAsync",value:function parseAsync(e,t,r){return this.parse(e,t,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},r),{},{sync:!1}))}},{key:"specialTypeNames",value:function specialTypeNames(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r.returnTypeNames=!0,this.encapsulate(e,t,r)}},{key:"rootTypeName",value:function rootTypeName(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r.iterateNone=!0,this.encapsulate(e,t,r)}},{key:"encapsulate",value:function encapsulate(e,r,n){var o=_async((function(e,r){return _await(Promise.all(r.map((function(e){return e[1].p}))),(function(n){return _await(Promise.all(n.map(_async((function(n){var i=!1,a=[],c=_slicedToArray(r.splice(0,1),1),u=_slicedToArray(c[0],7),s=u[0],f=u[2],l=u[3],p=u[4],y=u[5],v=u[6],b=_encapsulate(s,n,f,l,a,!0,v),h=hasConstructorOf(b,t);return function _invoke(e,t){var r=e();return r&&r.then?r.then(t):t(r)}((function(){if(s&&h)return _await(b.p,(function(t){return p[y]=t,i=!0,o(e,a)}))}),(function(t){return i?t:(s?p[y]=b:e=h?b.p:b,o(e,a))}))})))),(function(){return e}))}))})),i=(n=_objectSpread2(_objectSpread2({sync:!0},this.options),n)).sync,f=this,l={},p=[],y=[],v=[],b=!("cyclic"in n)||n.cyclic,h=n.encapsulateObserver,d=_encapsulate("",e,b,r||{},v);function finish(e){var t=Object.values(l);if(n.iterateNone)return t.length?t[0]:Typeson.getJSONType(e);if(t.length){if(n.returnTypeNames)return _toConsumableArray$1(new Set(t));e&&isPlainObject(e)&&!u.call(e,"$types")?e.$types=l:e={$:e,$types:{$:l}}}else isObject(e)&&u.call(e,"$types")&&(e={$:e,$types:!0});return!n.returnTypeNames&&e}function _adaptBuiltinStateObjectProperties(e,t,r){Object.assign(e,t);var n=s.map((function(t){var r=e[t];return delete e[t],r}));r(),s.forEach((function(t,r){e[t]=n[r]}))}function _encapsulate(e,r,o,i,s,v,b){var d,g={},m=_typeof$1(r),_=h?function(n){var a=b||i.type||Typeson.getJSONType(r);h(Object.assign(n||g,{keypath:e,value:r,cyclic:o,stateObj:i,promisesData:s,resolvingTypesonPromise:v,awaitingTypesonPromise:hasConstructorOf(r,t)},{type:a}))}:null;if(["string","boolean","number","undefined"].includes(m))return void 0===r||Number.isNaN(r)||r===Number.NEGATIVE_INFINITY||r===Number.POSITIVE_INFINITY?(d=i.replaced?r:replace(e,r,i,s,!1,v,_))!==r&&(g={replaced:d}):d=r,_&&_(),d;if(null===r)return _&&_(),r;if(o&&!i.iterateIn&&!i.iterateUnsetNumeric&&r&&"object"===_typeof$1(r)){var O=p.indexOf(r);if(!(O<0))return l[e]="#",_&&_({cyclicKeypath:y[O]}),"#"+y[O];!0===o&&(p.push(r),y.push(e))}var S,j=isPlainObject(r),T=c(r),A=(j||T)&&(!f.plainObjectReplacers.length||i.replaced)||i.iterateIn?r:replace(e,r,i,s,j||T,null,_);if(A!==r?(d=A,g={replaced:A}):""===e&&hasConstructorOf(r,t)?(s.push([e,r,o,i,void 0,void 0,i.type]),d=r):T&&"object"!==i.iterateIn||"array"===i.iterateIn?(S=new Array(r.length),g={clone:S}):(["function","symbol"].includes(_typeof$1(r))||"toJSON"in r||hasConstructorOf(r,t)||hasConstructorOf(r,Promise)||hasConstructorOf(r,ArrayBuffer))&&!j&&"object"!==i.iterateIn?d=r:(S={},i.addLength&&(S.length=r.length),g={clone:S}),_&&_(),n.iterateNone)return S||d;if(!S)return d;if(i.iterateIn){var w=function _loop(n){var a={ownKeys:u.call(r,n)};_adaptBuiltinStateObjectProperties(i,a,(function(){var a=e+(e?".":"")+escapeKeyPathComponent(n),c=_encapsulate(a,r[n],Boolean(o),i,s,v);hasConstructorOf(c,t)?s.push([a,c,Boolean(o),i,S,n,i.type]):void 0!==c&&(S[n]=c)}))};for(var P in r)w(P);_&&_({endIterateIn:!0,end:!0})}else a(r).forEach((function(n){var a=e+(e?".":"")+escapeKeyPathComponent(n);_adaptBuiltinStateObjectProperties(i,{ownKeys:!0},(function(){var e=_encapsulate(a,r[n],Boolean(o),i,s,v);hasConstructorOf(e,t)?s.push([a,e,Boolean(o),i,S,n,i.type]):void 0!==e&&(S[n]=e)}))})),_&&_({endIterateOwn:!0,end:!0});if(i.iterateUnsetNumeric){for(var I=r.length,N=function _loop2(n){if(!(n in r)){var a=e+(e?".":"")+n;_adaptBuiltinStateObjectProperties(i,{ownKeys:!1},(function(){var e=_encapsulate(a,void 0,Boolean(o),i,s,v);hasConstructorOf(e,t)?s.push([a,e,Boolean(o),i,S,n,i.type]):void 0!==e&&(S[n]=e)}))}},C=0;C<I;C++)N(C);_&&_({endIterateUnsetNumeric:!0,end:!0})}return S}function replace(e,t,r,n,o,a,c){for(var u=o?f.plainObjectReplacers:f.nonplainObjectReplacers,s=u.length;s--;){var p=u[s];if(p.test(t,r)){var y=p.type;if(f.revivers[y]){var v=l[e];l[e]=v?[y].concat(v):y}return Object.assign(r,{type:y,replaced:!0}),!i&&p.replaceAsync||p.replace?(c&&c({replacing:!0}),_encapsulate(e,p[i||!p.replaceAsync?"replace":"replaceAsync"](t,r),b&&"readonly",r,n,a,y)):(c&&c({typeDetected:!0}),_encapsulate(e,t,b&&"readonly",r,n,a,y))}}return t}return v.length?i&&n.throwOnBadSyncType?function(){throw new TypeError("Sync method requested but async result obtained")}():Promise.resolve(o(d,v)).then(finish):!i&&n.throwOnBadSyncType?function(){throw new TypeError("Async method requested but sync result obtained")}():n.stringification&&i?[finish(d)]:i?finish(d):Promise.resolve(finish(d))}},{key:"encapsulateSync",value:function encapsulateSync(e,t,r){return this.encapsulate(e,t,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},r),{},{sync:!0}))}},{key:"encapsulateAsync",value:function encapsulateAsync(e,t,r){return this.encapsulate(e,t,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},r),{},{sync:!1}))}},{key:"revive",value:function revive(e,r){var n=e&&e.$types;if(!n)return e;if(!0===n)return e.$;var o=(r=_objectSpread2(_objectSpread2({sync:!0},this.options),r)).sync,i=[],u={},s=!0;n.$&&isPlainObject(n.$)&&(e=e.$,n=n.$,s=!1);var f=this;function executeReviver(e,t){var r=_slicedToArray(f.revivers[e]||[],1)[0];if(!r)throw new Error("Unregistered type: "+e);return o&&!("revive"in r)?t:r[o&&r.revive?"revive":!o&&r.reviveAsync?"reviveAsync":"revive"](t,u)}var p=[];function checkUndefined(e){return hasConstructorOf(e,l)?void 0:e}var y,v=function revivePlainObjects(){var r=[];if(Object.entries(n).forEach((function(e){var t=_slicedToArray(e,2),o=t[0],i=t[1];"#"!==i&&[].concat(i).forEach((function(e){_slicedToArray(f.revivers[e]||[null,{}],2)[1].plain&&(r.push({keypath:o,type:e}),delete n[o])}))})),r.length)return r.sort(nestedPathsFirst).reduce((function reducer(r,n){var o=n.keypath,i=n.type;if(isThenable(r))return r.then((function(e){return reducer(e,{keypath:o,type:i})}));var a=getByKeyPath(e,o);if(hasConstructorOf(a=executeReviver(i,a),t))return a.then((function(t){var r=setAtKeyPath(e,o,t);r===t&&(e=r)}));var c=setAtKeyPath(e,o,a);c===a&&(e=c)}),void 0)}();return hasConstructorOf(v,t)?y=v.then((function(){return e})):(y=function _revive(e,r,o,u,f){if(!s||"$types"!==e){var y=n[e],v=c(r);if(v||isPlainObject(r)){var b=v?new Array(r.length):{};for(a(r).forEach((function(n){var i=_revive(e+(e?".":"")+escapeKeyPathComponent(n),r[n],o||b,b,n),a=function set(e){return hasConstructorOf(e,l)?b[n]=void 0:void 0!==e&&(b[n]=e),e};hasConstructorOf(i,t)?p.push(i.then((function(e){return a(e)}))):a(i)})),r=b;i.length;){var h=_slicedToArray(i[0],4),d=h[0],g=h[1],m=h[2],_=h[3],O=getByKeyPath(d,g);if(void 0===O)break;m[_]=O,i.splice(0,1)}}if(!y)return r;if("#"===y){var S=getByKeyPath(o,r.slice(1));return void 0===S&&i.push([o,r.slice(1),u,f]),S}return[].concat(y).reduce((function reducer(e,r){return hasConstructorOf(e,t)?e.then((function(e){return reducer(e,r)})):executeReviver(r,e)}),r)}}("",e,null),p.length&&(y=t.resolve(y).then((function(e){return t.all([e].concat(p))})).then((function(e){return _slicedToArray(e,1)[0]})))),isThenable(y)?o&&r.throwOnBadSyncType?function(){throw new TypeError("Sync method requested but async result obtained")}():hasConstructorOf(y,t)?y.p.then(checkUndefined):y:!o&&r.throwOnBadSyncType?function(){throw new TypeError("Async method requested but sync result obtained")}():o?checkUndefined(y):Promise.resolve(checkUndefined(y))}},{key:"reviveSync",value:function reviveSync(e,t){return this.revive(e,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},t),{},{sync:!0}))}},{key:"reviveAsync",value:function reviveAsync(e,t){return this.revive(e,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},t),{},{sync:!1}))}},{key:"register",value:function register(e,t){return t=t||{},[].concat(e).forEach((function R(e){var r=this;if(c(e))return e.map((function(e){return R.call(r,e)}));e&&a(e).forEach((function(r){if("#"===r)throw new TypeError("# cannot be used as a type name as it is reserved for cyclic objects");if(Typeson.JSON_TYPES.includes(r))throw new TypeError("Plain JSON object types are reserved as type names");var n=e[r],o=n&&n.testPlainObjects?this.plainObjectReplacers:this.nonplainObjectReplacers,i=o.filter((function(e){return e.type===r}));if(i.length&&(o.splice(o.indexOf(i[0]),1),delete this.revivers[r],delete this.types[r]),"function"==typeof n){var a=n;n={test:function test(e){return e&&e.constructor===a},replace:function replace(e){return _objectSpread2({},e)},revive:function revive(e){return Object.assign(Object.create(a.prototype),e)}}}else if(c(n)){var u=_slicedToArray(n,3);n={test:u[0],replace:u[1],revive:u[2]}}if(n&&n.test){var s={type:r,test:n.test.bind(n)};n.replace&&(s.replace=n.replace.bind(n)),n.replaceAsync&&(s.replaceAsync=n.replaceAsync.bind(n));var f="number"==typeof t.fallback?t.fallback:t.fallback?0:Number.POSITIVE_INFINITY;if(n.testPlainObjects?this.plainObjectReplacers.splice(f,0,s):this.nonplainObjectReplacers.splice(f,0,s),n.revive||n.reviveAsync){var l={};n.revive&&(l.revive=n.revive.bind(n)),n.reviveAsync&&(l.reviveAsync=n.reviveAsync.bind(n)),this.revivers[r]=[l,{plain:n.testPlainObjects}]}this.types[r]=n}}),this)}),this),this}}]),Typeson}(),l=function Undefined(){_classCallCheck(this,Undefined)};l.__typeson__type__="TypesonUndefined",f.Undefined=l,f.Promise=t,f.isThenable=isThenable,f.toStringTag=toStringTag,f.hasConstructorOf=hasConstructorOf,f.isObject=isObject,f.isPlainObject=isPlainObject,f.isUserObject=function isUserObject(e){if(!e||"Object"!==toStringTag(e))return!1;var t=o(e);return!t||(hasConstructorOf(e,Object)||isUserObject(t))},f.escapeKeyPathComponent=escapeKeyPathComponent,f.unescapeKeyPathComponent=unescapeKeyPathComponent,f.getByKeyPath=getByKeyPath,f.getJSONType=function getJSONType(e){return null===e?"null":Array.isArray(e)?"array":_typeof$1(e)},f.JSON_TYPES=["null","boolean","number","string","array","object"];var p={undef:{test:function test(e,t){return void 0===e&&(t.ownKeys||!("ownKeys"in t))},replace:function replace(e){return 0},revive:function revive(e){return new f.Undefined}}};function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _toConsumableArray(e){return function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}(e)||function _iterableToArray(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function _unsupportedIterableToArray(e,t){if(!e)return;if("string"==typeof e)return _arrayLikeToArray(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return _arrayLikeToArray(e,t)}(e)||function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var y={StringObject:{test:function test(e){return"String"===f.toStringTag(e)&&"object"===_typeof(e)},replace:function replace(e){return String(e)},revive:function revive(e){return new String(e)}},BooleanObject:{test:function test(e){return"Boolean"===f.toStringTag(e)&&"object"===_typeof(e)},replace:function replace(e){return Boolean(e)},revive:function revive(e){return new Boolean(e)}},NumberObject:{test:function test(e){return"Number"===f.toStringTag(e)&&"object"===_typeof(e)},replace:function replace(e){return Number(e)},revive:function revive(e){return new Number(e)}}},v=[{nan:{test:function test(e){return Number.isNaN(e)},replace:function replace(e){return"NaN"},revive:function revive(e){return Number.NaN}}},{infinity:{test:function test(e){return e===Number.POSITIVE_INFINITY},replace:function replace(e){return"Infinity"},revive:function revive(e){return Number.POSITIVE_INFINITY}}},{negativeInfinity:{test:function test(e){return e===Number.NEGATIVE_INFINITY},replace:function replace(e){return"-Infinity"},revive:function revive(e){return Number.NEGATIVE_INFINITY}}}],b={date:{test:function test(e){return"Date"===f.toStringTag(e)},replace:function replace(e){var t=e.getTime();return Number.isNaN(t)?"NaN":t},revive:function revive(e){return"NaN"===e?new Date(Number.NaN):new Date(e)}}},h={error:{test:function test(e){return"Error"===f.toStringTag(e)},replace:function replace(e){return{name:e.name,message:e.message}},revive:function revive(e){var t=e.name,r=e.message,n=new Error(r);return n.name=t,n}}},d="undefined"==typeof self?global:self,g={};["TypeError","RangeError","SyntaxError","ReferenceError","EvalError","URIError","InternalError"].forEach((function(e){var t=d[e];t&&(g[e.toLowerCase()]={test:function test(e){return f.hasConstructorOf(e,t)},replace:function replace(e){return e.message},revive:function revive(e){return new t(e)}})}));for(var m={regexp:{test:function test(e){return"RegExp"===f.toStringTag(e)},replace:function replace(e){return{source:e.source,flags:(e.global?"g":"")+(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.sticky?"y":"")+(e.unicode?"u":"")}},revive:function revive(e){var t=e.source,r=e.flags;return new RegExp(t,r)}}},_={map:{test:function test(e){return"Map"===f.toStringTag(e)},replace:function replace(e){return _toConsumableArray(e.entries())},revive:function revive(e){return new Map(e)}}},O={set:{test:function test(e){return"Set"===f.toStringTag(e)},replace:function replace(e){return _toConsumableArray(e.values())},revive:function revive(e){return new Set(e)}}},S="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",j=new Uint8Array(256),T=0;T<S.length;T++)j[S.charCodeAt(T)]=T;var A=function encode(e,t,r){null==r&&(r=e.byteLength);for(var n=new Uint8Array(e,t||0,r),o=n.length,i="",a=0;a<o;a+=3)i+=S[n[a]>>2],i+=S[(3&n[a])<<4|n[a+1]>>4],i+=S[(15&n[a+1])<<2|n[a+2]>>6],i+=S[63&n[a+2]];return o%3==2?i=i.slice(0,-1)+"=":o%3==1&&(i=i.slice(0,-2)+"=="),i},w=function decode(e){var t,r,n,o,i=e.length,a=.75*e.length,c=0;"="===e[e.length-1]&&(a--,"="===e[e.length-2]&&a--);for(var u=new ArrayBuffer(a),s=new Uint8Array(u),f=0;f<i;f+=4)t=j[e.charCodeAt(f)],r=j[e.charCodeAt(f+1)],n=j[e.charCodeAt(f+2)],o=j[e.charCodeAt(f+3)],s[c++]=t<<2|r>>4,s[c++]=(15&r)<<4|n>>2,s[c++]=(3&n)<<6|63&o;return u},P={arraybuffer:{test:function test(e){return"ArrayBuffer"===f.toStringTag(e)},replace:function replace(e,t){t.buffers||(t.buffers=[]);var r=t.buffers.indexOf(e);return r>-1?{index:r}:(t.buffers.push(e),A(e))},revive:function revive(e,t){if(t.buffers||(t.buffers=[]),"object"===_typeof(e))return t.buffers[e.index];var r=w(e);return t.buffers.push(r),r}}},I="undefined"==typeof self?global:self,N={};["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array"].forEach((function(e){var t=e,r=I[t];r&&(N[e.toLowerCase()]={test:function test(e){return f.toStringTag(e)===t},replace:function replace(e,t){var r=e.buffer,n=e.byteOffset,o=e.length;t.buffers||(t.buffers=[]);var i=t.buffers.indexOf(r);return i>-1?{index:i,byteOffset:n,length:o}:(t.buffers.push(r),{encoded:A(r),byteOffset:n,length:o})},revive:function revive(e,t){t.buffers||(t.buffers=[]);var n,o=e.byteOffset,i=e.length,a=e.encoded,c=e.index;return"index"in e?n=t.buffers[c]:(n=w(a),t.buffers.push(n)),new r(n,o,i)}})}));var C={dataview:{test:function test(e){return"DataView"===f.toStringTag(e)},replace:function replace(e,t){var r=e.buffer,n=e.byteOffset,o=e.byteLength;t.buffers||(t.buffers=[]);var i=t.buffers.indexOf(r);return i>-1?{index:i,byteOffset:n,byteLength:o}:(t.buffers.push(r),{encoded:A(r),byteOffset:n,byteLength:o})},revive:function revive(e,t){t.buffers||(t.buffers=[]);var r,n=e.byteOffset,o=e.byteLength,i=e.encoded,a=e.index;return"index"in e?r=t.buffers[a]:(r=w(i),t.buffers.push(r)),new DataView(r,n,o)}}},k={test:function test(e){return f.hasConstructorOf(e,Intl.Collator)},replace:function replace(e){return e.resolvedOptions()},revive:function revive(e){return new Intl.Collator(e.locale,e)}},E={test:function test(e){return f.hasConstructorOf(e,Intl.DateTimeFormat)},replace:function replace(e){return e.resolvedOptions()},revive:function revive(e){return new Intl.DateTimeFormat(e.locale,e)}},B={test:function test(e){return f.hasConstructorOf(e,Intl.NumberFormat)},replace:function replace(e){return e.resolvedOptions()},revive:function revive(e){return new Intl.NumberFormat(e.locale,e)}},$={bigint:{test:function test(e){return"bigint"==typeof e},replace:function replace(e){return String(e)},revive:function revive(e){return BigInt(e)}}},K={bigintObject:{test:function test(e){return"object"===_typeof(e)&&f.hasConstructorOf(e,BigInt)},replace:function replace(e){return String(e)},revive:function revive(e){return new Object(BigInt(e))}}};return[p,e,y,v,b,h,g,m].concat("function"==typeof Map?_:[],"function"==typeof Set?O:[],"function"==typeof ArrayBuffer?P:[],"function"==typeof Uint8Array?N:[],"function"==typeof DataView?C:[],"undefined"!=typeof Intl?{IntlCollator:k,IntlDateTimeFormat:E,IntlNumberFormat:B}:[],"undefined"!=typeof BigInt?[$,K]:[])}));
//# sourceMappingURL=builtin.js.map
