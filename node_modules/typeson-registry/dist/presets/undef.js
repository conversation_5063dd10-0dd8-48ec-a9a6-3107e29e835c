!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):((e="undefined"!=typeof globalThis?globalThis:e||self).Typeson=e.Typeson||{},e.Typeson.presets=e.Typeson.presets||{},e.Typeson.presets.undef=t())}(this,(function(){"use strict";var e=[{sparseArrays:{testPlainObjects:!0,test:function test(e){return Array.isArray(e)},replace:function replace(e,t){return t.iterateUnsetNumeric=!0,e}}},{sparseUndefined:{test:function test(e,t){return void 0===e&&!1===t.ownKeys},replace:function replace(e){return 0},revive:function revive(e){}}}];function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function _typeof(e){return typeof e}:function _typeof(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function _defineProperty(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){_defineProperty(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function _slicedToArray(e,t){return function _arrayWithHoles(e){if(Array.isArray(e))return e}(e)||function _iterableToArrayLimit(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,o=!1,i=void 0;try{for(var a,c=e[Symbol.iterator]();!(r=(a=c.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){o=!0,i=e}finally{try{r||null==c.return||c.return()}finally{if(o)throw i}}return n}(e,t)||_unsupportedIterableToArray(e,t)||function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _toConsumableArray(e){return function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}(e)||function _iterableToArray(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||_unsupportedIterableToArray(e)||function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var t=function TypesonPromise(e){_classCallCheck(this,TypesonPromise),this.p=new Promise(e)};t.__typeson__type__="TypesonPromise","undefined"!=typeof Symbol&&(t.prototype[Symbol.toStringTag]="TypesonPromise"),t.prototype.then=function(e,n){var r=this;return new t((function(t,o){r.p.then((function(n){t(e?e(n):n)})).catch((function(e){return n?n(e):Promise.reject(e)})).then(t,o)}))},t.prototype.catch=function(e){return this.then(null,e)},t.resolve=function(e){return new t((function(t){t(e)}))},t.reject=function(e){return new t((function(t,n){n(e)}))},["all","race"].forEach((function(e){t[e]=function(n){return new t((function(t,r){Promise[e](n.map((function(e){return e&&e.constructor&&"TypesonPromise"===e.constructor.__typeson__type__?e.p:e}))).then(t,r)}))}}));var n={}.toString,r={}.hasOwnProperty,o=Object.getPrototypeOf,i=r.toString;function isThenable(e,t){return isObject(e)&&"function"==typeof e.then&&(!t||"function"==typeof e.catch)}function toStringTag(e){return n.call(e).slice(8,-1)}function hasConstructorOf(e,t){if(!e||"object"!==_typeof(e))return!1;var n=o(e);if(!n)return null===t;var a=r.call(n,"constructor")&&n.constructor;return"function"!=typeof a?null===t:t===a||(null!==t&&i.call(a)===i.call(t)||"function"==typeof t&&"string"==typeof a.__typeson__type__&&a.__typeson__type__===t.__typeson__type__)}function isPlainObject(e){return!(!e||"Object"!==toStringTag(e))&&(!o(e)||hasConstructorOf(e,Object))}function isObject(e){return e&&"object"===_typeof(e)}function escapeKeyPathComponent(e){return e.replace(/~/g,"~0").replace(/\./g,"~1")}function unescapeKeyPathComponent(e){return e.replace(/~1/g,".").replace(/~0/g,"~")}function getByKeyPath(e,t){if(""===t)return e;var n=t.indexOf(".");if(n>-1){var r=e[unescapeKeyPathComponent(t.slice(0,n))];return void 0===r?void 0:getByKeyPath(r,t.slice(n+1))}return e[unescapeKeyPathComponent(t)]}function setAtKeyPath(e,t,n){if(""===t)return n;var r=t.indexOf(".");return r>-1?setAtKeyPath(e[unescapeKeyPathComponent(t.slice(0,r))],t.slice(r+1),n):(e[unescapeKeyPathComponent(t)]=n,e)}function _await(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var a=Object.keys,c=Array.isArray,s={}.hasOwnProperty,u=["type","replaced","iterateIn","iterateUnsetNumeric"];function _async(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}function nestedPathsFirst(e,t){if(""===e.keypath)return-1;var n=e.keypath.match(/\./g)||0,r=t.keypath.match(/\./g)||0;return n&&(n=n.length),r&&(r=r.length),n>r?-1:n<r?1:e.keypath<t.keypath?-1:e.keypath>t.keypath}var p=function(){function Typeson(e){_classCallCheck(this,Typeson),this.options=e,this.plainObjectReplacers=[],this.nonplainObjectReplacers=[],this.revivers={},this.types={}}return function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),e}(Typeson,[{key:"stringify",value:function stringify(e,t,n,r){r=_objectSpread2(_objectSpread2(_objectSpread2({},this.options),r),{},{stringification:!0});var o=this.encapsulate(e,null,r);return c(o)?JSON.stringify(o[0],t,n):o.then((function(e){return JSON.stringify(e,t,n)}))}},{key:"stringifySync",value:function stringifySync(e,t,n,r){return this.stringify(e,t,n,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},r),{},{sync:!0}))}},{key:"stringifyAsync",value:function stringifyAsync(e,t,n,r){return this.stringify(e,t,n,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},r),{},{sync:!1}))}},{key:"parse",value:function parse(e,t,n){return n=_objectSpread2(_objectSpread2(_objectSpread2({},this.options),n),{},{parse:!0}),this.revive(JSON.parse(e,t),n)}},{key:"parseSync",value:function parseSync(e,t,n){return this.parse(e,t,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},n),{},{sync:!0}))}},{key:"parseAsync",value:function parseAsync(e,t,n){return this.parse(e,t,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},n),{},{sync:!1}))}},{key:"specialTypeNames",value:function specialTypeNames(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return n.returnTypeNames=!0,this.encapsulate(e,t,n)}},{key:"rootTypeName",value:function rootTypeName(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return n.iterateNone=!0,this.encapsulate(e,t,n)}},{key:"encapsulate",value:function encapsulate(e,n,r){var o=_async((function(e,n){return _await(Promise.all(n.map((function(e){return e[1].p}))),(function(r){return _await(Promise.all(r.map(_async((function(r){var i=!1,a=[],c=_slicedToArray(n.splice(0,1),1),s=_slicedToArray(c[0],7),u=s[0],p=s[2],y=s[3],l=s[4],f=s[5],h=s[6],v=_encapsulate(u,r,p,y,a,!0,h),d=hasConstructorOf(v,t);return function _invoke(e,t){var n=e();return n&&n.then?n.then(t):t(n)}((function(){if(u&&d)return _await(v.p,(function(t){return l[f]=t,i=!0,o(e,a)}))}),(function(t){return i?t:(u?l[f]=v:e=d?v.p:v,o(e,a))}))})))),(function(){return e}))}))})),i=(r=_objectSpread2(_objectSpread2({sync:!0},this.options),r)).sync,p=this,y={},l=[],f=[],h=[],v=!("cyclic"in r)||r.cyclic,d=r.encapsulateObserver,b=_encapsulate("",e,v,n||{},h);function finish(e){var t=Object.values(y);if(r.iterateNone)return t.length?t[0]:Typeson.getJSONType(e);if(t.length){if(r.returnTypeNames)return _toConsumableArray(new Set(t));e&&isPlainObject(e)&&!s.call(e,"$types")?e.$types=y:e={$:e,$types:{$:y}}}else isObject(e)&&s.call(e,"$types")&&(e={$:e,$types:!0});return!r.returnTypeNames&&e}function _adaptBuiltinStateObjectProperties(e,t,n){Object.assign(e,t);var r=u.map((function(t){var n=e[t];return delete e[t],n}));n(),u.forEach((function(t,n){e[t]=r[n]}))}function _encapsulate(e,n,o,i,u,h,v){var b,_={},O=_typeof(n),j=d?function(r){var a=v||i.type||Typeson.getJSONType(n);d(Object.assign(r||_,{keypath:e,value:n,cyclic:o,stateObj:i,promisesData:u,resolvingTypesonPromise:h,awaitingTypesonPromise:hasConstructorOf(n,t)},{type:a}))}:null;if(["string","boolean","number","undefined"].includes(O))return void 0===n||Number.isNaN(n)||n===Number.NEGATIVE_INFINITY||n===Number.POSITIVE_INFINITY?(b=i.replaced?n:replace(e,n,i,u,!1,h,j))!==n&&(_={replaced:b}):b=n,j&&j(),b;if(null===n)return j&&j(),n;if(o&&!i.iterateIn&&!i.iterateUnsetNumeric&&n&&"object"===_typeof(n)){var m=l.indexOf(n);if(!(m<0))return y[e]="#",j&&j({cyclicKeypath:f[m]}),"#"+f[m];!0===o&&(l.push(n),f.push(e))}var S,g=isPlainObject(n),T=c(n),P=(g||T)&&(!p.plainObjectReplacers.length||i.replaced)||i.iterateIn?n:replace(e,n,i,u,g||T,null,j);if(P!==n?(b=P,_={replaced:P}):""===e&&hasConstructorOf(n,t)?(u.push([e,n,o,i,void 0,void 0,i.type]),b=n):T&&"object"!==i.iterateIn||"array"===i.iterateIn?(S=new Array(n.length),_={clone:S}):(["function","symbol"].includes(_typeof(n))||"toJSON"in n||hasConstructorOf(n,t)||hasConstructorOf(n,Promise)||hasConstructorOf(n,ArrayBuffer))&&!g&&"object"!==i.iterateIn?b=n:(S={},i.addLength&&(S.length=n.length),_={clone:S}),j&&j(),r.iterateNone)return S||b;if(!S)return b;if(i.iterateIn){var w=function _loop(r){var a={ownKeys:s.call(n,r)};_adaptBuiltinStateObjectProperties(i,a,(function(){var a=e+(e?".":"")+escapeKeyPathComponent(r),c=_encapsulate(a,n[r],Boolean(o),i,u,h);hasConstructorOf(c,t)?u.push([a,c,Boolean(o),i,S,r,i.type]):void 0!==c&&(S[r]=c)}))};for(var A in n)w(A);j&&j({endIterateIn:!0,end:!0})}else a(n).forEach((function(r){var a=e+(e?".":"")+escapeKeyPathComponent(r);_adaptBuiltinStateObjectProperties(i,{ownKeys:!0},(function(){var e=_encapsulate(a,n[r],Boolean(o),i,u,h);hasConstructorOf(e,t)?u.push([a,e,Boolean(o),i,S,r,i.type]):void 0!==e&&(S[r]=e)}))})),j&&j({endIterateOwn:!0,end:!0});if(i.iterateUnsetNumeric){for(var C=n.length,k=function _loop2(r){if(!(r in n)){var a=e+(e?".":"")+r;_adaptBuiltinStateObjectProperties(i,{ownKeys:!1},(function(){var e=_encapsulate(a,void 0,Boolean(o),i,u,h);hasConstructorOf(e,t)?u.push([a,e,Boolean(o),i,S,r,i.type]):void 0!==e&&(S[r]=e)}))}},N=0;N<C;N++)k(N);j&&j({endIterateUnsetNumeric:!0,end:!0})}return S}function replace(e,t,n,r,o,a,c){for(var s=o?p.plainObjectReplacers:p.nonplainObjectReplacers,u=s.length;u--;){var l=s[u];if(l.test(t,n)){var f=l.type;if(p.revivers[f]){var h=y[e];y[e]=h?[f].concat(h):f}return Object.assign(n,{type:f,replaced:!0}),!i&&l.replaceAsync||l.replace?(c&&c({replacing:!0}),_encapsulate(e,l[i||!l.replaceAsync?"replace":"replaceAsync"](t,n),v&&"readonly",n,r,a,f)):(c&&c({typeDetected:!0}),_encapsulate(e,t,v&&"readonly",n,r,a,f))}}return t}return h.length?i&&r.throwOnBadSyncType?function(){throw new TypeError("Sync method requested but async result obtained")}():Promise.resolve(o(b,h)).then(finish):!i&&r.throwOnBadSyncType?function(){throw new TypeError("Async method requested but sync result obtained")}():r.stringification&&i?[finish(b)]:i?finish(b):Promise.resolve(finish(b))}},{key:"encapsulateSync",value:function encapsulateSync(e,t,n){return this.encapsulate(e,t,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},n),{},{sync:!0}))}},{key:"encapsulateAsync",value:function encapsulateAsync(e,t,n){return this.encapsulate(e,t,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},n),{},{sync:!1}))}},{key:"revive",value:function revive(e,n){var r=e&&e.$types;if(!r)return e;if(!0===r)return e.$;var o=(n=_objectSpread2(_objectSpread2({sync:!0},this.options),n)).sync,i=[],s={},u=!0;r.$&&isPlainObject(r.$)&&(e=e.$,r=r.$,u=!1);var p=this;function executeReviver(e,t){var n=_slicedToArray(p.revivers[e]||[],1)[0];if(!n)throw new Error("Unregistered type: "+e);return o&&!("revive"in n)?t:n[o&&n.revive?"revive":!o&&n.reviveAsync?"reviveAsync":"revive"](t,s)}var l=[];function checkUndefined(e){return hasConstructorOf(e,y)?void 0:e}var f,h=function revivePlainObjects(){var n=[];if(Object.entries(r).forEach((function(e){var t=_slicedToArray(e,2),o=t[0],i=t[1];"#"!==i&&[].concat(i).forEach((function(e){_slicedToArray(p.revivers[e]||[null,{}],2)[1].plain&&(n.push({keypath:o,type:e}),delete r[o])}))})),n.length)return n.sort(nestedPathsFirst).reduce((function reducer(n,r){var o=r.keypath,i=r.type;if(isThenable(n))return n.then((function(e){return reducer(e,{keypath:o,type:i})}));var a=getByKeyPath(e,o);if(hasConstructorOf(a=executeReviver(i,a),t))return a.then((function(t){var n=setAtKeyPath(e,o,t);n===t&&(e=n)}));var c=setAtKeyPath(e,o,a);c===a&&(e=c)}),void 0)}();return hasConstructorOf(h,t)?f=h.then((function(){return e})):(f=function _revive(e,n,o,s,p){if(!u||"$types"!==e){var f=r[e],h=c(n);if(h||isPlainObject(n)){var v=h?new Array(n.length):{};for(a(n).forEach((function(r){var i=_revive(e+(e?".":"")+escapeKeyPathComponent(r),n[r],o||v,v,r),a=function set(e){return hasConstructorOf(e,y)?v[r]=void 0:void 0!==e&&(v[r]=e),e};hasConstructorOf(i,t)?l.push(i.then((function(e){return a(e)}))):a(i)})),n=v;i.length;){var d=_slicedToArray(i[0],4),b=d[0],_=d[1],O=d[2],j=d[3],m=getByKeyPath(b,_);if(void 0===m)break;O[j]=m,i.splice(0,1)}}if(!f)return n;if("#"===f){var S=getByKeyPath(o,n.slice(1));return void 0===S&&i.push([o,n.slice(1),s,p]),S}return[].concat(f).reduce((function reducer(e,n){return hasConstructorOf(e,t)?e.then((function(e){return reducer(e,n)})):executeReviver(n,e)}),n)}}("",e,null),l.length&&(f=t.resolve(f).then((function(e){return t.all([e].concat(l))})).then((function(e){return _slicedToArray(e,1)[0]})))),isThenable(f)?o&&n.throwOnBadSyncType?function(){throw new TypeError("Sync method requested but async result obtained")}():hasConstructorOf(f,t)?f.p.then(checkUndefined):f:!o&&n.throwOnBadSyncType?function(){throw new TypeError("Async method requested but sync result obtained")}():o?checkUndefined(f):Promise.resolve(checkUndefined(f))}},{key:"reviveSync",value:function reviveSync(e,t){return this.revive(e,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},t),{},{sync:!0}))}},{key:"reviveAsync",value:function reviveAsync(e,t){return this.revive(e,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},t),{},{sync:!1}))}},{key:"register",value:function register(e,t){return t=t||{},[].concat(e).forEach((function R(e){var n=this;if(c(e))return e.map((function(e){return R.call(n,e)}));e&&a(e).forEach((function(n){if("#"===n)throw new TypeError("# cannot be used as a type name as it is reserved for cyclic objects");if(Typeson.JSON_TYPES.includes(n))throw new TypeError("Plain JSON object types are reserved as type names");var r=e[n],o=r&&r.testPlainObjects?this.plainObjectReplacers:this.nonplainObjectReplacers,i=o.filter((function(e){return e.type===n}));if(i.length&&(o.splice(o.indexOf(i[0]),1),delete this.revivers[n],delete this.types[n]),"function"==typeof r){var a=r;r={test:function test(e){return e&&e.constructor===a},replace:function replace(e){return _objectSpread2({},e)},revive:function revive(e){return Object.assign(Object.create(a.prototype),e)}}}else if(c(r)){var s=_slicedToArray(r,3);r={test:s[0],replace:s[1],revive:s[2]}}if(r&&r.test){var u={type:n,test:r.test.bind(r)};r.replace&&(u.replace=r.replace.bind(r)),r.replaceAsync&&(u.replaceAsync=r.replaceAsync.bind(r));var p="number"==typeof t.fallback?t.fallback:t.fallback?0:Number.POSITIVE_INFINITY;if(r.testPlainObjects?this.plainObjectReplacers.splice(p,0,u):this.nonplainObjectReplacers.splice(p,0,u),r.revive||r.reviveAsync){var y={};r.revive&&(y.revive=r.revive.bind(r)),r.reviveAsync&&(y.reviveAsync=r.reviveAsync.bind(r)),this.revivers[n]=[y,{plain:r.testPlainObjects}]}this.types[n]=r}}),this)}),this),this}}]),Typeson}(),y=function Undefined(){_classCallCheck(this,Undefined)};return y.__typeson__type__="TypesonUndefined",p.Undefined=y,p.Promise=t,p.isThenable=isThenable,p.toStringTag=toStringTag,p.hasConstructorOf=hasConstructorOf,p.isObject=isObject,p.isPlainObject=isPlainObject,p.isUserObject=function isUserObject(e){if(!e||"Object"!==toStringTag(e))return!1;var t=o(e);return!t||(hasConstructorOf(e,Object)||isUserObject(t))},p.escapeKeyPathComponent=escapeKeyPathComponent,p.unescapeKeyPathComponent=unescapeKeyPathComponent,p.getByKeyPath=getByKeyPath,p.getJSONType=function getJSONType(e){return null===e?"null":Array.isArray(e)?"array":_typeof(e)},p.JSON_TYPES=["null","boolean","number","string","array","object"],[e,{undef:{test:function test(e,t){return void 0===e&&(t.ownKeys||!("ownKeys"in t))},replace:function replace(e){return 0},revive:function revive(e){return new p.Undefined}}}]}));
//# sourceMappingURL=undef.js.map
