!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):((e="undefined"!=typeof globalThis?globalThis:e||self).Typeson=e.Typeson||{},e.Typeson.presets=e.Typeson.presets||{},e.Typeson.presets.specialNumbers=n())}(this,(function(){"use strict";return[{nan:{test:function test(e){return Number.isNaN(e)},replace:function replace(e){return"NaN"},revive:function revive(e){return Number.NaN}}},{infinity:{test:function test(e){return e===Number.POSITIVE_INFINITY},replace:function replace(e){return"Infinity"},revive:function revive(e){return Number.POSITIVE_INFINITY}}},{negativeInfinity:{test:function test(e){return e===Number.NEGATIVE_INFINITY},replace:function replace(e){return"-Infinity"},revive:function revive(e){return Number.NEGATIVE_INFINITY}}}]}));
//# sourceMappingURL=special-numbers.js.map
