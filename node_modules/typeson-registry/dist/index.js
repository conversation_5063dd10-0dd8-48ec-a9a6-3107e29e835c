function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function _defineProperty(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){_defineProperty(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function _slicedToArray(e,t){return function _arrayWithHoles(e){if(Array.isArray(e))return e}(e)||function _iterableToArrayLimit(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var r=[],n=!0,a=!1,o=void 0;try{for(var i,s=e[Symbol.iterator]();!(n=(i=s.next()).done)&&(r.push(i.value),!t||r.length!==t);n=!0);}catch(e){a=!0,o=e}finally{try{n||null==s.return||s.return()}finally{if(a)throw o}}return r}(e,t)||_unsupportedIterableToArray(e,t)||function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _toConsumableArray(e){return function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}(e)||function _iterableToArray(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||_unsupportedIterableToArray(e)||function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var e=function TypesonPromise(e){_classCallCheck(this,TypesonPromise),this.p=new Promise(e)};e.__typeson__type__="TypesonPromise","undefined"!=typeof Symbol&&(e.prototype[Symbol.toStringTag]="TypesonPromise"),e.prototype.then=function(t,r){var n=this;return new e((function(e,a){n.p.then((function(r){e(t?t(r):r)})).catch((function(e){return r?r(e):Promise.reject(e)})).then(e,a)}))},e.prototype.catch=function(e){return this.then(null,e)},e.resolve=function(t){return new e((function(e){e(t)}))},e.reject=function(t){return new e((function(e,r){r(t)}))},["all","race"].forEach((function(t){e[t]=function(r){return new e((function(e,n){Promise[t](r.map((function(e){return e&&e.constructor&&"TypesonPromise"===e.constructor.__typeson__type__?e.p:e}))).then(e,n)}))}}));var t={}.toString,r={}.hasOwnProperty,n=Object.getPrototypeOf,a=r.toString;function isThenable(e,t){return isObject(e)&&"function"==typeof e.then&&(!t||"function"==typeof e.catch)}function toStringTag(e){return t.call(e).slice(8,-1)}function hasConstructorOf(e,t){if(!e||"object"!==_typeof(e))return!1;var o=n(e);if(!o)return null===t;var i=r.call(o,"constructor")&&o.constructor;return"function"!=typeof i?null===t:t===i||(null!==t&&a.call(i)===a.call(t)||"function"==typeof t&&"string"==typeof i.__typeson__type__&&i.__typeson__type__===t.__typeson__type__)}function isPlainObject(e){return!(!e||"Object"!==toStringTag(e))&&(!n(e)||hasConstructorOf(e,Object))}function isObject(e){return e&&"object"===_typeof(e)}function escapeKeyPathComponent(e){return e.replace(/~/g,"~0").replace(/\./g,"~1")}function unescapeKeyPathComponent(e){return e.replace(/~1/g,".").replace(/~0/g,"~")}function getByKeyPath(e,t){if(""===t)return e;var r=t.indexOf(".");if(r>-1){var n=e[unescapeKeyPathComponent(t.slice(0,r))];return void 0===n?void 0:getByKeyPath(n,t.slice(r+1))}return e[unescapeKeyPathComponent(t)]}function setAtKeyPath(e,t,r){if(""===t)return r;var n=t.indexOf(".");return n>-1?setAtKeyPath(e[unescapeKeyPathComponent(t.slice(0,n))],t.slice(n+1),r):(e[unescapeKeyPathComponent(t)]=r,e)}function _await(e,t,r){return r?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var o=Object.keys,i=Array.isArray,s={}.hasOwnProperty,c=["type","replaced","iterateIn","iterateUnsetNumeric"];function _async(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}function nestedPathsFirst(e,t){if(""===e.keypath)return-1;var r=e.keypath.match(/\./g)||0,n=t.keypath.match(/\./g)||0;return r&&(r=r.length),n&&(n=n.length),r>n?-1:r<n?1:e.keypath<t.keypath?-1:e.keypath>t.keypath}var u=function(){function Typeson(e){_classCallCheck(this,Typeson),this.options=e,this.plainObjectReplacers=[],this.nonplainObjectReplacers=[],this.revivers={},this.types={}}return function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),e}(Typeson,[{key:"stringify",value:function stringify(e,t,r,n){n=_objectSpread2(_objectSpread2(_objectSpread2({},this.options),n),{},{stringification:!0});var a=this.encapsulate(e,null,n);return i(a)?JSON.stringify(a[0],t,r):a.then((function(e){return JSON.stringify(e,t,r)}))}},{key:"stringifySync",value:function stringifySync(e,t,r,n){return this.stringify(e,t,r,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},n),{},{sync:!0}))}},{key:"stringifyAsync",value:function stringifyAsync(e,t,r,n){return this.stringify(e,t,r,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},n),{},{sync:!1}))}},{key:"parse",value:function parse(e,t,r){return r=_objectSpread2(_objectSpread2(_objectSpread2({},this.options),r),{},{parse:!0}),this.revive(JSON.parse(e,t),r)}},{key:"parseSync",value:function parseSync(e,t,r){return this.parse(e,t,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},r),{},{sync:!0}))}},{key:"parseAsync",value:function parseAsync(e,t,r){return this.parse(e,t,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},r),{},{sync:!1}))}},{key:"specialTypeNames",value:function specialTypeNames(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r.returnTypeNames=!0,this.encapsulate(e,t,r)}},{key:"rootTypeName",value:function rootTypeName(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r.iterateNone=!0,this.encapsulate(e,t,r)}},{key:"encapsulate",value:function encapsulate(t,r,n){var a=_async((function(t,r){return _await(Promise.all(r.map((function(e){return e[1].p}))),(function(n){return _await(Promise.all(n.map(_async((function(n){var o=!1,i=[],s=_slicedToArray(r.splice(0,1),1),c=_slicedToArray(s[0],7),u=c[0],l=c[2],f=c[3],y=c[4],p=c[5],d=c[6],b=_encapsulate(u,n,l,f,i,!0,d),v=hasConstructorOf(b,e);return function _invoke(e,t){var r=e();return r&&r.then?r.then(t):t(r)}((function(){if(u&&v)return _await(b.p,(function(e){return y[p]=e,o=!0,a(t,i)}))}),(function(e){return o?e:(u?y[p]=b:t=v?b.p:b,a(t,i))}))})))),(function(){return t}))}))})),u=(n=_objectSpread2(_objectSpread2({sync:!0},this.options),n)).sync,l=this,f={},y=[],p=[],d=[],b=!("cyclic"in n)||n.cyclic,v=n.encapsulateObserver,h=_encapsulate("",t,b,r||{},d);function finish(e){var t=Object.values(f);if(n.iterateNone)return t.length?t[0]:Typeson.getJSONType(e);if(t.length){if(n.returnTypeNames)return _toConsumableArray(new Set(t));e&&isPlainObject(e)&&!s.call(e,"$types")?e.$types=f:e={$:e,$types:{$:f}}}else isObject(e)&&s.call(e,"$types")&&(e={$:e,$types:!0});return!n.returnTypeNames&&e}function _adaptBuiltinStateObjectProperties(e,t,r){Object.assign(e,t);var n=c.map((function(t){var r=e[t];return delete e[t],r}));r(),c.forEach((function(t,r){e[t]=n[r]}))}function _encapsulate(t,r,a,c,u,d,b){var h,g={},m=_typeof(r),O=v?function(n){var o=b||c.type||Typeson.getJSONType(r);v(Object.assign(n||g,{keypath:t,value:r,cyclic:a,stateObj:c,promisesData:u,resolvingTypesonPromise:d,awaitingTypesonPromise:hasConstructorOf(r,e)},{type:o}))}:null;if(["string","boolean","number","undefined"].includes(m))return void 0===r||Number.isNaN(r)||r===Number.NEGATIVE_INFINITY||r===Number.POSITIVE_INFINITY?(h=c.replaced?r:replace(t,r,c,u,!1,d,O))!==r&&(g={replaced:h}):h=r,O&&O(),h;if(null===r)return O&&O(),r;if(a&&!c.iterateIn&&!c.iterateUnsetNumeric&&r&&"object"===_typeof(r)){var S=y.indexOf(r);if(!(S<0))return f[t]="#",O&&O({cyclicKeypath:p[S]}),"#"+p[S];!0===a&&(y.push(r),p.push(t))}var _,j=isPlainObject(r),A=i(r),w=(j||A)&&(!l.plainObjectReplacers.length||c.replaced)||c.iterateIn?r:replace(t,r,c,u,j||A,null,O);if(w!==r?(h=w,g={replaced:w}):""===t&&hasConstructorOf(r,e)?(u.push([t,r,a,c,void 0,void 0,c.type]),h=r):A&&"object"!==c.iterateIn||"array"===c.iterateIn?(_=new Array(r.length),g={clone:_}):(["function","symbol"].includes(_typeof(r))||"toJSON"in r||hasConstructorOf(r,e)||hasConstructorOf(r,Promise)||hasConstructorOf(r,ArrayBuffer))&&!j&&"object"!==c.iterateIn?h=r:(_={},c.addLength&&(_.length=r.length),g={clone:_}),O&&O(),n.iterateNone)return _||h;if(!_)return h;if(c.iterateIn){var T=function _loop(n){var o={ownKeys:s.call(r,n)};_adaptBuiltinStateObjectProperties(c,o,(function(){var o=t+(t?".":"")+escapeKeyPathComponent(n),i=_encapsulate(o,r[n],Boolean(a),c,u,d);hasConstructorOf(i,e)?u.push([o,i,Boolean(a),c,_,n,c.type]):void 0!==i&&(_[n]=i)}))};for(var I in r)T(I);O&&O({endIterateIn:!0,end:!0})}else o(r).forEach((function(n){var o=t+(t?".":"")+escapeKeyPathComponent(n);_adaptBuiltinStateObjectProperties(c,{ownKeys:!0},(function(){var t=_encapsulate(o,r[n],Boolean(a),c,u,d);hasConstructorOf(t,e)?u.push([o,t,Boolean(a),c,_,n,c.type]):void 0!==t&&(_[n]=t)}))})),O&&O({endIterateOwn:!0,end:!0});if(c.iterateUnsetNumeric){for(var P=r.length,N=function _loop2(n){if(!(n in r)){var o=t+(t?".":"")+n;_adaptBuiltinStateObjectProperties(c,{ownKeys:!1},(function(){var t=_encapsulate(o,void 0,Boolean(a),c,u,d);hasConstructorOf(t,e)?u.push([o,t,Boolean(a),c,_,n,c.type]):void 0!==t&&(_[n]=t)}))}},C=0;C<P;C++)N(C);O&&O({endIterateUnsetNumeric:!0,end:!0})}return _}function replace(e,t,r,n,a,o,i){for(var s=a?l.plainObjectReplacers:l.nonplainObjectReplacers,c=s.length;c--;){var y=s[c];if(y.test(t,r)){var p=y.type;if(l.revivers[p]){var d=f[e];f[e]=d?[p].concat(d):p}return Object.assign(r,{type:p,replaced:!0}),!u&&y.replaceAsync||y.replace?(i&&i({replacing:!0}),_encapsulate(e,y[u||!y.replaceAsync?"replace":"replaceAsync"](t,r),b&&"readonly",r,n,o,p)):(i&&i({typeDetected:!0}),_encapsulate(e,t,b&&"readonly",r,n,o,p))}}return t}return d.length?u&&n.throwOnBadSyncType?function(){throw new TypeError("Sync method requested but async result obtained")}():Promise.resolve(a(h,d)).then(finish):!u&&n.throwOnBadSyncType?function(){throw new TypeError("Async method requested but sync result obtained")}():n.stringification&&u?[finish(h)]:u?finish(h):Promise.resolve(finish(h))}},{key:"encapsulateSync",value:function encapsulateSync(e,t,r){return this.encapsulate(e,t,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},r),{},{sync:!0}))}},{key:"encapsulateAsync",value:function encapsulateAsync(e,t,r){return this.encapsulate(e,t,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},r),{},{sync:!1}))}},{key:"revive",value:function revive(t,r){var n=t&&t.$types;if(!n)return t;if(!0===n)return t.$;var a=(r=_objectSpread2(_objectSpread2({sync:!0},this.options),r)).sync,s=[],c={},u=!0;n.$&&isPlainObject(n.$)&&(t=t.$,n=n.$,u=!1);var f=this;function executeReviver(e,t){var r=_slicedToArray(f.revivers[e]||[],1)[0];if(!r)throw new Error("Unregistered type: "+e);return a&&!("revive"in r)?t:r[a&&r.revive?"revive":!a&&r.reviveAsync?"reviveAsync":"revive"](t,c)}var y=[];function checkUndefined(e){return hasConstructorOf(e,l)?void 0:e}var p,d=function revivePlainObjects(){var r=[];if(Object.entries(n).forEach((function(e){var t=_slicedToArray(e,2),a=t[0],o=t[1];"#"!==o&&[].concat(o).forEach((function(e){_slicedToArray(f.revivers[e]||[null,{}],2)[1].plain&&(r.push({keypath:a,type:e}),delete n[a])}))})),r.length)return r.sort(nestedPathsFirst).reduce((function reducer(r,n){var a=n.keypath,o=n.type;if(isThenable(r))return r.then((function(e){return reducer(e,{keypath:a,type:o})}));var i=getByKeyPath(t,a);if(hasConstructorOf(i=executeReviver(o,i),e))return i.then((function(e){var r=setAtKeyPath(t,a,e);r===e&&(t=r)}));var s=setAtKeyPath(t,a,i);s===i&&(t=s)}),void 0)}();return hasConstructorOf(d,e)?p=d.then((function(){return t})):(p=function _revive(t,r,a,c,f){if(!u||"$types"!==t){var p=n[t],d=i(r);if(d||isPlainObject(r)){var b=d?new Array(r.length):{};for(o(r).forEach((function(n){var o=_revive(t+(t?".":"")+escapeKeyPathComponent(n),r[n],a||b,b,n),i=function set(e){return hasConstructorOf(e,l)?b[n]=void 0:void 0!==e&&(b[n]=e),e};hasConstructorOf(o,e)?y.push(o.then((function(e){return i(e)}))):i(o)})),r=b;s.length;){var v=_slicedToArray(s[0],4),h=v[0],g=v[1],m=v[2],O=v[3],S=getByKeyPath(h,g);if(void 0===S)break;m[O]=S,s.splice(0,1)}}if(!p)return r;if("#"===p){var _=getByKeyPath(a,r.slice(1));return void 0===_&&s.push([a,r.slice(1),c,f]),_}return[].concat(p).reduce((function reducer(t,r){return hasConstructorOf(t,e)?t.then((function(e){return reducer(e,r)})):executeReviver(r,t)}),r)}}("",t,null),y.length&&(p=e.resolve(p).then((function(t){return e.all([t].concat(y))})).then((function(e){return _slicedToArray(e,1)[0]})))),isThenable(p)?a&&r.throwOnBadSyncType?function(){throw new TypeError("Sync method requested but async result obtained")}():hasConstructorOf(p,e)?p.p.then(checkUndefined):p:!a&&r.throwOnBadSyncType?function(){throw new TypeError("Async method requested but sync result obtained")}():a?checkUndefined(p):Promise.resolve(checkUndefined(p))}},{key:"reviveSync",value:function reviveSync(e,t){return this.revive(e,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},t),{},{sync:!0}))}},{key:"reviveAsync",value:function reviveAsync(e,t){return this.revive(e,_objectSpread2(_objectSpread2({throwOnBadSyncType:!0},t),{},{sync:!1}))}},{key:"register",value:function register(e,t){return t=t||{},[].concat(e).forEach((function R(e){var r=this;if(i(e))return e.map((function(e){return R.call(r,e)}));e&&o(e).forEach((function(r){if("#"===r)throw new TypeError("# cannot be used as a type name as it is reserved for cyclic objects");if(Typeson.JSON_TYPES.includes(r))throw new TypeError("Plain JSON object types are reserved as type names");var n=e[r],a=n&&n.testPlainObjects?this.plainObjectReplacers:this.nonplainObjectReplacers,o=a.filter((function(e){return e.type===r}));if(o.length&&(a.splice(a.indexOf(o[0]),1),delete this.revivers[r],delete this.types[r]),"function"==typeof n){var s=n;n={test:function test(e){return e&&e.constructor===s},replace:function replace(e){return _objectSpread2({},e)},revive:function revive(e){return Object.assign(Object.create(s.prototype),e)}}}else if(i(n)){var c=_slicedToArray(n,3);n={test:c[0],replace:c[1],revive:c[2]}}if(n&&n.test){var u={type:r,test:n.test.bind(n)};n.replace&&(u.replace=n.replace.bind(n)),n.replaceAsync&&(u.replaceAsync=n.replaceAsync.bind(n));var l="number"==typeof t.fallback?t.fallback:t.fallback?0:Number.POSITIVE_INFINITY;if(n.testPlainObjects?this.plainObjectReplacers.splice(l,0,u):this.nonplainObjectReplacers.splice(l,0,u),n.revive||n.reviveAsync){var f={};n.revive&&(f.revive=n.revive.bind(n)),n.reviveAsync&&(f.reviveAsync=n.reviveAsync.bind(n)),this.revivers[r]=[f,{plain:n.testPlainObjects}]}this.types[r]=n}}),this)}),this),this}}]),Typeson}(),l=function Undefined(){_classCallCheck(this,Undefined)};l.__typeson__type__="TypesonUndefined",u.Undefined=l,u.Promise=e,u.isThenable=isThenable,u.toStringTag=toStringTag,u.hasConstructorOf=hasConstructorOf,u.isObject=isObject,u.isPlainObject=isPlainObject,u.isUserObject=function isUserObject(e){if(!e||"Object"!==toStringTag(e))return!1;var t=n(e);return!t||(hasConstructorOf(e,Object)||isUserObject(t))},u.escapeKeyPathComponent=escapeKeyPathComponent,u.unescapeKeyPathComponent=unescapeKeyPathComponent,u.getByKeyPath=getByKeyPath,u.getJSONType=function getJSONType(e){return null===e?"null":Array.isArray(e)?"array":_typeof(e)},u.JSON_TYPES=["null","boolean","number","string","array","object"];for(var f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",y=new Uint8Array(256),p=0;p<f.length;p++)y[f.charCodeAt(p)]=p;var d=function encode(e,t,r){null==r&&(r=e.byteLength);for(var n=new Uint8Array(e,t||0,r),a=n.length,o="",i=0;i<a;i+=3)o+=f[n[i]>>2],o+=f[(3&n[i])<<4|n[i+1]>>4],o+=f[(15&n[i+1])<<2|n[i+2]>>6],o+=f[63&n[i+2]];return a%3==2?o=o.slice(0,-1)+"=":a%3==1&&(o=o.slice(0,-2)+"=="),o},b=function decode(e){var t,r,n,a,o=e.length,i=.75*e.length,s=0;"="===e[e.length-1]&&(i--,"="===e[e.length-2]&&i--);for(var c=new ArrayBuffer(i),u=new Uint8Array(c),l=0;l<o;l+=4)t=y[e.charCodeAt(l)],r=y[e.charCodeAt(l+1)],n=y[e.charCodeAt(l+2)],a=y[e.charCodeAt(l+3)],u[s++]=t<<2|r>>4,u[s++]=(15&r)<<4|n>>2,u[s++]=(3&n)<<6|63&a;return c};const v={arraybuffer:{test:e=>"ArrayBuffer"===u.toStringTag(e),replace(e,t){t.buffers||(t.buffers=[]);const r=t.buffers.indexOf(e);return r>-1?{index:r}:(t.buffers.push(e),d(e))},revive(e,t){if(t.buffers||(t.buffers=[]),"object"==typeof e)return t.buffers[e.index];const r=b(e);return t.buffers.push(r),r}}},h={bigintObject:{test:e=>"object"==typeof e&&u.hasConstructorOf(e,BigInt),replace:e=>String(e),revive:e=>new Object(BigInt(e))}},g={bigint:{test:e=>"bigint"==typeof e,replace:e=>String(e),revive:e=>BigInt(e)}};function string2arraybuffer(e){const t=new Uint8Array(e.length);for(let r=0;r<e.length;r++)t[r]=e.charCodeAt(r);return t.buffer}const m={blob:{test:e=>"Blob"===u.toStringTag(e),replace(e){const t=new XMLHttpRequest;if(t.overrideMimeType("text/plain; charset=x-user-defined"),t.open("GET",URL.createObjectURL(e),!1),t.send(),200!==t.status&&0!==t.status)throw new Error("Bad Blob access: "+t.status);return{type:e.type,stringContents:t.responseText}},revive:({type:e,stringContents:t})=>new Blob([string2arraybuffer(t)],{type:e}),replaceAsync:e=>new u.Promise(((t,r)=>{const n=new FileReader;n.addEventListener("load",(()=>{t({type:e.type,stringContents:n.result})})),n.addEventListener("error",(()=>{r(n.error)})),n.readAsBinaryString(e)}))}};function generateUUID(){let e=Date.now()+("undefined"!=typeof performance&&"function"==typeof performance.now?performance.now():0);return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/gu,(function(t){const r=Math.trunc((e+16*Math.random())%16);return e=Math.floor(e/16),("x"===t?r:3&r|8).toString(16)}))}const O={},S={cloneable:{test:e=>e&&"object"==typeof e&&"function"==typeof e[Symbol.for("cloneEncapsulate")],replace(e){const t=e[Symbol.for("cloneEncapsulate")](),r=generateUUID();return O[r]=e,{uuid:r,encapsulated:t}},revive:({uuid:e,encapsulated:t})=>O[e][Symbol.for("cloneRevive")](t)}},_={cryptokey:{test:e=>"CryptoKey"===u.toStringTag(e)&&e.extractable,replaceAsync:e=>new u.Promise(((t,r)=>{crypto.subtle.exportKey("jwk",e).catch((e=>{r(e)})).then((r=>{t({jwk:r,algorithm:e.algorithm,usages:e.usages})}))})),revive:({jwk:e,algorithm:t,usages:r})=>crypto.subtle.importKey("jwk",e,t,!0,r)}},j={dataview:{test:e=>"DataView"===u.toStringTag(e),replace({buffer:e,byteOffset:t,byteLength:r},n){n.buffers||(n.buffers=[]);const a=n.buffers.indexOf(e);return a>-1?{index:a,byteOffset:t,byteLength:r}:(n.buffers.push(e),{encoded:d(e),byteOffset:t,byteLength:r})},revive(e,t){t.buffers||(t.buffers=[]);const{byteOffset:r,byteLength:n,encoded:a,index:o}=e;let i;return"index"in e?i=t.buffers[o]:(i=b(a),t.buffers.push(i)),new DataView(i,r,n)}}},A={date:{test:e=>"Date"===u.toStringTag(e),replace(e){const t=e.getTime();return Number.isNaN(t)?"NaN":t},revive:e=>"NaN"===e?new Date(Number.NaN):new Date(e)}},w={error:{test:e=>"Error"===u.toStringTag(e),replace:({name:e,message:t})=>({name:e,message:t}),revive({name:e,message:t}){const r=new Error(t);return r.name=e,r}}},T="undefined"==typeof self?global:self,I={};["TypeError","RangeError","SyntaxError","ReferenceError","EvalError","URIError","InternalError"].forEach((e=>{const t=T[e];t&&(I[e.toLowerCase()]={test:e=>u.hasConstructorOf(e,t),replace:e=>e.message,revive:e=>new t(e)})}));const P={file:{test:e=>"File"===u.toStringTag(e),replace(e){const t=new XMLHttpRequest;if(t.overrideMimeType("text/plain; charset=x-user-defined"),t.open("GET",URL.createObjectURL(e),!1),t.send(),200!==t.status&&0!==t.status)throw new Error("Bad File access: "+t.status);return{type:e.type,stringContents:t.responseText,name:e.name,lastModified:e.lastModified}},revive:({name:e,type:t,stringContents:r,lastModified:n})=>new File([string2arraybuffer(r)],e,{type:t,lastModified:n}),replaceAsync:e=>new u.Promise((function(t,r){const n=new FileReader;n.addEventListener("load",(function(){t({type:e.type,stringContents:n.result,name:e.name,lastModified:e.lastModified})})),n.addEventListener("error",(function(){r(n.error)})),n.readAsBinaryString(e)}))}},N={file:P.file,filelist:{test:e=>"FileList"===u.toStringTag(e),replace(e){const t=[];for(let r=0;r<e.length;r++)t[r]=e.item(r);return t},revive(e){class FileList{constructor(){this._files=arguments[0],this.length=this._files.length}item(e){return this._files[e]}get[Symbol.toStringTag](){return"FileList"}}return new FileList(e)}}},C={imagebitmap:{test:e=>"ImageBitmap"===u.toStringTag(e)||e&&e.dataset&&"ImageBitmap"===e.dataset.toStringTag,replace(e){const t=document.createElement("canvas");return t.getContext("2d").drawImage(e,0,0),t.toDataURL()},revive(e){const t=document.createElement("canvas"),r=t.getContext("2d"),n=document.createElement("img");return n.addEventListener("load",(function(){r.drawImage(n,0,0)})),n.src=e,t},reviveAsync(e){const t=document.createElement("canvas"),r=t.getContext("2d"),n=document.createElement("img");return n.addEventListener("load",(function(){r.drawImage(n,0,0)})),n.src=e,createImageBitmap(t)}}},x={imagedata:{test:e=>"ImageData"===u.toStringTag(e),replace:e=>({array:[...e.data],width:e.width,height:e.height}),revive:e=>new ImageData(new Uint8ClampedArray(e.array),e.width,e.height)}},E={infinity:{test:e=>e===Number.POSITIVE_INFINITY,replace:e=>"Infinity",revive:e=>Number.POSITIVE_INFINITY}},k={IntlCollator:{test:e=>u.hasConstructorOf(e,Intl.Collator),replace:e=>e.resolvedOptions(),revive:e=>new Intl.Collator(e.locale,e)},IntlDateTimeFormat:{test:e=>u.hasConstructorOf(e,Intl.DateTimeFormat),replace:e=>e.resolvedOptions(),revive:e=>new Intl.DateTimeFormat(e.locale,e)},IntlNumberFormat:{test:e=>u.hasConstructorOf(e,Intl.NumberFormat),replace:e=>e.resolvedOptions(),revive:e=>new Intl.NumberFormat(e.locale,e)}},B={map:{test:e=>"Map"===u.toStringTag(e),replace:e=>[...e.entries()],revive:e=>new Map(e)}},U={nan:{test:e=>Number.isNaN(e),replace:e=>"NaN",revive:e=>Number.NaN}},K={negativeInfinity:{test:e=>e===Number.NEGATIVE_INFINITY,replace:e=>"-Infinity",revive:e=>Number.NEGATIVE_INFINITY}},L={nonbuiltinIgnore:{test:e=>e&&"object"==typeof e&&!Array.isArray(e)&&!["Object","Boolean","Number","String","Error","RegExp","Math","Date","Map","Set","JSON","ArrayBuffer","SharedArrayBuffer","DataView","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array","Promise","String Iterator","Array Iterator","Map Iterator","Set Iterator","WeakMap","WeakSet","Atomics","Module"].includes(u.toStringTag(e)),replace(e){}}},F={StringObject:{test:e=>"String"===u.toStringTag(e)&&"object"==typeof e,replace:e=>String(e),revive:e=>new String(e)},BooleanObject:{test:e=>"Boolean"===u.toStringTag(e)&&"object"==typeof e,replace:e=>Boolean(e),revive:e=>new Boolean(e)},NumberObject:{test:e=>"Number"===u.toStringTag(e)&&"object"==typeof e,replace:e=>Number(e),revive:e=>new Number(e)}},D={regexp:{test:e=>"RegExp"===u.toStringTag(e),replace:e=>({source:e.source,flags:(e.global?"g":"")+(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.sticky?"y":"")+(e.unicode?"u":"")}),revive:({source:e,flags:t})=>new RegExp(e,t)}},M={},$={resurrectable:{test:e=>e&&!Array.isArray(e)&&["object","function","symbol"].includes(typeof e),replace(e){const t=generateUUID();return M[t]=e,t},revive:e=>M[e]}},J={set:{test:e=>"Set"===u.toStringTag(e),replace:e=>[...e.values()],revive:e=>new Set(e)}},V="undefined"==typeof self?global:self,Y={};["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array"].forEach((function(e){const t=e,r=V[e];r&&(Y[e.toLowerCase()]={test:e=>u.toStringTag(e)===t,replace:e=>(0===e.byteOffset&&e.byteLength===e.buffer.byteLength?e:e.slice(0)).buffer,revive:e=>"ArrayBuffer"===u.toStringTag(e)?new r(e):e})}));const q="undefined"==typeof self?global:self,W={};["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array"].forEach((function(e){const t=e,r=q[t];r&&(W[e.toLowerCase()]={test:e=>u.toStringTag(e)===t,replace({buffer:e,byteOffset:t,length:r},n){n.buffers||(n.buffers=[]);const a=n.buffers.indexOf(e);return a>-1?{index:a,byteOffset:t,length:r}:(n.buffers.push(e),{encoded:d(e),byteOffset:t,length:r})},revive(e,t){t.buffers||(t.buffers=[]);const{byteOffset:n,length:a,encoded:o,index:i}=e;let s;return"index"in e?s=t.buffers[i]:(s=b(o),t.buffers.push(s)),new r(s,n,a)}})}));const G={undef:{test:(e,t)=>void 0===e&&(t.ownKeys||!("ownKeys"in t)),replace:e=>0,revive:e=>new u.Undefined}},H={userObject:{test:(e,t)=>u.isUserObject(e),replace:e=>({...e}),revive:e=>e}},X=[{arrayNonindexKeys:{testPlainObjects:!0,test:(e,t)=>!!Array.isArray(e)&&(Object.keys(e).some((e=>String(Number.parseInt(e))!==e))&&(t.iterateIn="object",t.addLength=!0),!0),replace:(e,t)=>(t.iterateUnsetNumeric=!0,e),revive(e){if(Array.isArray(e))return e;const t=[];return Object.keys(e).forEach((r=>{const n=e[r];t[r]=n})),t}}},{sparseUndefined:{test:(e,t)=>void 0===e&&!1===t.ownKeys,replace:e=>0,revive(e){}}}],z=[U,E,K],Q=[G,X,F,z,A,w,I,D].concat("function"==typeof Map?B:[],"function"==typeof Set?J:[],"function"==typeof ArrayBuffer?v:[],"function"==typeof Uint8Array?W:[],"function"==typeof DataView?j:[],"undefined"!=typeof Intl?k:[],"undefined"!=typeof BigInt?[g,h]:[]),Z=[w,I],ee=[Q,{arraybuffer:null},Y],te=[{sparseArrays:{testPlainObjects:!0,test:e=>Array.isArray(e),replace:(e,t)=>(t.iterateUnsetNumeric=!0,e)}},{sparseUndefined:{test:(e,t)=>void 0===e&&!1===t.ownKeys,replace:e=>0,revive(e){}}}],re=[H,G,X,F,z,A,D,x,C,P,N,m].concat("function"==typeof Map?B:[],"function"==typeof Set?J:[],"function"==typeof ArrayBuffer?v:[],"function"==typeof Uint8Array?W:[],"function"==typeof DataView?j:[],"undefined"!=typeof Intl?k:[],"undefined"!=typeof crypto?_:[],"undefined"!=typeof BigInt?[g,h]:[]);var ne=re.concat({checkDataCloneException:{test(e){const t={}.toString.call(e).slice(8,-1);if(["symbol","function"].includes(typeof e)||["Arguments","Module","Error","Promise","WeakMap","WeakSet","Event","MessageChannel"].includes(t)||e&&"object"==typeof e&&"number"==typeof e.nodeType&&"function"==typeof e.insertBefore)throw new DOMException("The object cannot be cloned.","DataCloneError");return!1}}});const ae=[te,G],oe=[Q];u.types={arraybuffer:v,bigintObject:h,bigint:g,blob:m,cloneable:S,cryptokey:_,dataview:j,date:A,error:w,errors:I,file:P,filelist:N,imagebitmap:C,imagedata:x,infinity:E,intlTypes:k,map:B,nan:U,negativeInfinity:K,nonbuiltinIgnore:L,primitiveObjects:F,regexp:D,resurrectable:$,set:J,typedArraysSocketio:Y,typedArrays:W,undef:G,userObject:H},u.presets={arrayNonindexKeys:X,builtin:Q,postmessage:Z,socketio:ee,sparseUndefined:te,specialNumbers:z,structuredCloningThrowing:ne,structuredCloning:re,undef:ae,universal:oe};export default u;
//# sourceMappingURL=index.js.map
