{"version": 3, "file": "all.js", "sources": ["../node_modules/typeson/dist/typeson-esm.js", "../node_modules/base64-arraybuffer-es6/dist/base64-arraybuffer-es.js", "../types/arraybuffer.js", "../types/bigint-object.js", "../types/bigint.js", "../utils/stringArrayBuffer.js", "../types/blob.js", "../utils/generateUUID.js", "../types/cloneable.js", "../types/cryptokey.js", "../types/dataview.js", "../types/date.js", "../types/error.js", "../types/errors.js", "../types/file.js", "../types/filelist.js", "../types/imagebitmap.js", "../types/imagedata.js", "../types/infinity.js", "../types/intl-types.js", "../types/map.js", "../types/nan.js", "../types/negative-infinity.js", "../types/nonbuiltin-ignore.js", "../types/primitive-objects.js", "../types/regexp.js", "../types/resurrectable.js", "../types/set.js", "../types/typed-arrays-socketio.js", "../types/typed-arrays.js", "../types/undef.js", "../types/user-object.js", "../presets/array-nonindex-keys.js", "../presets/special-numbers.js", "../presets/builtin.js", "../presets/postmessage.js", "../presets/socketio.js", "../presets/sparse-undefined.js", "../presets/structured-cloning.js", "../presets/structured-cloning-throwing.js", "../presets/undef.js", "../presets/universal.js", "../index.js"], "sourcesContent": ["function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter);\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\n/**\n* @callback TypesonFulfilled\n* @returns {Promise<any>|any}\n*/\n\n/**\n* @callback TypesonRejected\n* @returns {Promise<any>|any}\n*/\n\n/**\n * @callback TypesonResolve\n * @param {any} value\n * @returns {Promise<any>}\n */\n\n/**\n * @callback TypesonReject\n * @param {Error|any} error\n * @returns {Promise<any>}\n */\n\n/**\n * @callback TypesonResolveReject\n * @param {TypesonResolve} typesonResolve\n * @param {TypesonReject} typesonReject\n * @returns {Promise<any>}\n */\n\n/* eslint-disable block-spacing, space-before-function-paren,\n  space-before-blocks, space-infix-ops, semi, promise/avoid-new,\n  jsdoc/require-jsdoc */\n\n/**\n * We keep this function minimized so if using two instances of this\n *   library, where one is minimized and one is not, it will still work\n *   with `hasConstructorOf`.\n * With ES6 classes, we may be able to simply use `class TypesonPromise\n *   extends Promise` and add a string tag for detection.\n * @param {TypesonResolveReject} f\n */\nvar TypesonPromise = function TypesonPromise(f) {\n  _classCallCheck(this, TypesonPromise);\n\n  this.p = new Promise(f);\n};\n/* eslint-enable block-spacing, space-before-function-paren,\n  space-before-blocks, space-infix-ops, semi, promise/avoid-new,\n  jsdoc/require-jsdoc */\n// eslint-disable-next-line max-len\n// class TypesonPromise extends Promise {get[Symbol.toStringTag](){return 'TypesonPromise'};} // eslint-disable-line keyword-spacing, space-before-function-paren, space-before-blocks, block-spacing, semi\n\n\nTypesonPromise.__typeson__type__ = 'TypesonPromise'; // Note: core-js-bundle provides a `Symbol` polyfill\n\n/* istanbul ignore else */\n\nif (typeof Symbol !== 'undefined') {\n  // Ensure `isUserObject` will return `false` for `TypesonPromise`\n  TypesonPromise.prototype[Symbol.toStringTag] = 'TypesonPromise';\n}\n/**\n *\n * @param {TypesonFulfilled} [onFulfilled]\n * @param {TypesonRejected} [onRejected]\n * @returns {TypesonPromise}\n */\n\n\nTypesonPromise.prototype.then = function (onFulfilled, onRejected) {\n  var _this = this;\n\n  return new TypesonPromise(function (typesonResolve, typesonReject) {\n    // eslint-disable-next-line promise/catch-or-return\n    _this.p.then(function (res) {\n      // eslint-disable-next-line promise/always-return\n      typesonResolve(onFulfilled ? onFulfilled(res) : res);\n    })[\"catch\"](function (res) {\n      return onRejected ? onRejected(res) : Promise.reject(res);\n    }).then(typesonResolve, typesonReject);\n  });\n};\n/**\n *\n * @param {TypesonRejected} onRejected\n * @returns {TypesonPromise}\n */\n\n\nTypesonPromise.prototype[\"catch\"] = function (onRejected) {\n  return this.then(null, onRejected);\n};\n/**\n *\n * @param {any} v\n * @returns {TypesonPromise}\n */\n\n\nTypesonPromise.resolve = function (v) {\n  return new TypesonPromise(function (typesonResolve) {\n    typesonResolve(v);\n  });\n};\n/**\n *\n * @param {any} v\n * @returns {TypesonPromise}\n */\n\n\nTypesonPromise.reject = function (v) {\n  return new TypesonPromise(function (typesonResolve, typesonReject) {\n    typesonReject(v);\n  });\n};\n\n['all', 'race'].forEach(function (meth) {\n  /**\n   *\n   * @param {Promise<any>[]} promArr\n   * @returns {TypesonPromise}\n   */\n  TypesonPromise[meth] = function (promArr) {\n    return new TypesonPromise(function (typesonResolve, typesonReject) {\n      // eslint-disable-next-line promise/catch-or-return\n      Promise[meth](promArr.map(function (prom) {\n        return prom && prom.constructor && prom.constructor.__typeson__type__ === 'TypesonPromise' ? prom.p : prom;\n      })).then(typesonResolve, typesonReject);\n    });\n  };\n});\n\nvar _ref = {},\n    toStr = _ref.toString,\n    hasOwn$1 = {}.hasOwnProperty,\n    getProto = Object.getPrototypeOf,\n    fnToString = hasOwn$1.toString;\n/**\n * Second argument not in use internally, but provided for utility.\n * @param {any} v\n * @param {boolean} catchCheck\n * @returns {boolean}\n */\n\nfunction isThenable(v, catchCheck) {\n  return isObject(v) && typeof v.then === 'function' && (!catchCheck || typeof v[\"catch\"] === 'function');\n}\n/**\n *\n * @param {any} val\n * @returns {string}\n */\n\n\nfunction toStringTag(val) {\n  return toStr.call(val).slice(8, -1);\n}\n/**\n * This function is dependent on both constructors\n *   being identical so any minimization is expected of both.\n * @param {any} a\n * @param {GenericFunction} b\n * @returns {boolean}\n */\n\n\nfunction hasConstructorOf(a, b) {\n  if (!a || _typeof(a) !== 'object') {\n    return false;\n  }\n\n  var proto = getProto(a);\n\n  if (!proto) {\n    return b === null;\n  }\n\n  var Ctor = hasOwn$1.call(proto, 'constructor') && proto.constructor;\n\n  if (typeof Ctor !== 'function') {\n    return b === null;\n  }\n\n  if (b === Ctor) {\n    return true;\n  }\n\n  if (b !== null && fnToString.call(Ctor) === fnToString.call(b)) {\n    return true;\n  }\n\n  if (typeof b === 'function' && typeof Ctor.__typeson__type__ === 'string' && Ctor.__typeson__type__ === b.__typeson__type__) {\n    return true;\n  }\n\n  return false;\n}\n/**\n *\n * @param {any} val\n * @returns {boolean}\n */\n\n\nfunction isPlainObject(val) {\n  // Mirrors jQuery's\n  if (!val || toStringTag(val) !== 'Object') {\n    return false;\n  }\n\n  var proto = getProto(val);\n\n  if (!proto) {\n    // `Object.create(null)`\n    return true;\n  }\n\n  return hasConstructorOf(val, Object);\n}\n/**\n *\n * @param {any} val\n * @returns {boolean}\n */\n\n\nfunction isUserObject(val) {\n  if (!val || toStringTag(val) !== 'Object') {\n    return false;\n  }\n\n  var proto = getProto(val);\n\n  if (!proto) {\n    // `Object.create(null)`\n    return true;\n  }\n\n  return hasConstructorOf(val, Object) || isUserObject(proto);\n}\n/**\n *\n * @param {any} v\n * @returns {boolean}\n */\n\n\nfunction isObject(v) {\n  return v && _typeof(v) === 'object';\n}\n/**\n *\n * @param {string} keyPathComponent\n * @returns {string}\n */\n\n\nfunction escapeKeyPathComponent(keyPathComponent) {\n  return keyPathComponent.replace(/~/g, '~0').replace(/\\./g, '~1');\n}\n/**\n *\n * @param {string} keyPathComponent\n * @returns {string}\n */\n\n\nfunction unescapeKeyPathComponent(keyPathComponent) {\n  return keyPathComponent.replace(/~1/g, '.').replace(/~0/g, '~');\n}\n/**\n * @param {PlainObject|GenericArray} obj\n * @param {string} keyPath\n * @returns {any}\n */\n\n\nfunction getByKeyPath(obj, keyPath) {\n  if (keyPath === '') {\n    return obj;\n  }\n\n  var period = keyPath.indexOf('.');\n\n  if (period > -1) {\n    var innerObj = obj[unescapeKeyPathComponent(keyPath.slice(0, period))];\n    return innerObj === undefined ? undefined : getByKeyPath(innerObj, keyPath.slice(period + 1));\n  }\n\n  return obj[unescapeKeyPathComponent(keyPath)];\n}\n/**\n *\n * @param {PlainObject} obj\n * @param {string} keyPath\n * @param {any} value\n * @returns {any}\n */\n\n\nfunction setAtKeyPath(obj, keyPath, value) {\n  if (keyPath === '') {\n    return value;\n  }\n\n  var period = keyPath.indexOf('.');\n\n  if (period > -1) {\n    var innerObj = obj[unescapeKeyPathComponent(keyPath.slice(0, period))];\n    return setAtKeyPath(innerObj, keyPath.slice(period + 1), value);\n  }\n\n  obj[unescapeKeyPathComponent(keyPath)] = value;\n  return obj;\n}\n/**\n *\n * @param {external:JSON} value\n * @returns {\"null\"|\"array\"|\"undefined\"|\"boolean\"|\"number\"|\"string\"|\n *  \"object\"|\"symbol\"}\n */\n\n\nfunction getJSONType(value) {\n  return value === null ? 'null' : Array.isArray(value) ? 'array' : _typeof(value);\n}\n\nfunction _await(value, then, direct) {\n  if (direct) {\n    return then ? then(value) : value;\n  }\n\n  if (!value || !value.then) {\n    value = Promise.resolve(value);\n  }\n\n  return then ? value.then(then) : value;\n}\n\nvar keys = Object.keys,\n    isArray = Array.isArray,\n    hasOwn = {}.hasOwnProperty,\n    internalStateObjPropsToIgnore = ['type', 'replaced', 'iterateIn', 'iterateUnsetNumeric'];\n/**\n * Handle plain object revivers first so reference setting can use\n * revived type (e.g., array instead of object); assumes revived\n * has same structure or will otherwise break subsequent references.\n * @param {PlainObjectType} a\n * @param {PlainObjectType} b\n * @returns {1|-1|boolean}\n */\n\nfunction _async(f) {\n  return function () {\n    for (var args = [], i = 0; i < arguments.length; i++) {\n      args[i] = arguments[i];\n    }\n\n    try {\n      return Promise.resolve(f.apply(this, args));\n    } catch (e) {\n      return Promise.reject(e);\n    }\n  };\n}\n/**\n * @callback Tester\n * @param {any} value\n * @param {StateObject} stateobj\n * @returns {boolean}\n */\n\n/**\n* @callback Replacer\n* @param {any} value\n* @param {StateObject} stateObj\n* @returns {any} Should be JSON-stringifiable\n*/\n\n/**\n* @callback Reviver\n* @param {JSON} value\n* @param {StateObject} stateObj\n* @returns {any}\n*/\n\n/**\n* @typedef {PlainObject} TypesonOptions\n* @property {boolean} stringification Auto-set by `stringify`\n*/\n\n/**\n * An instance of this class can be used to call `stringify()` and `parse()`.\n * Typeson resolves cyclic references by default. Can also be extended to\n * support custom types using the register() method.\n *\n * @class\n * @param {{cyclic: boolean}} [options] - if cyclic (default true),\n *   cyclic references will be handled gracefully.\n */\n\n\nfunction _invoke(body, then) {\n  var result = body();\n\n  if (result && result.then) {\n    return result.then(then);\n  }\n\n  return then(result);\n}\n\nfunction nestedPathsFirst(a, b) {\n  if (a.keypath === '') {\n    return -1;\n  }\n\n  var as = a.keypath.match(/\\./g) || 0;\n  var bs = b.keypath.match(/\\./g) || 0;\n\n  if (as) {\n    as = as.length;\n  }\n\n  if (bs) {\n    bs = bs.length;\n  }\n\n  return as > bs ? -1 : as < bs ? 1 : a.keypath < b.keypath ? -1 : a.keypath > b.keypath;\n}\n\nvar Typeson = /*#__PURE__*/function () {\n  /**\n   * @param {TypesonOptions} options\n   */\n  function Typeson(options) {\n    _classCallCheck(this, Typeson);\n\n    this.options = options; // Replacers signature: replace (value). Returns falsy if not\n    //   replacing. Otherwise ['Date', value.getTime()]\n\n    this.plainObjectReplacers = [];\n    this.nonplainObjectReplacers = []; // Revivers: [{type => reviver}, {plain: boolean}].\n    //   Sample: [{'Date': value => new Date(value)}, {plain: false}]\n\n    this.revivers = {};\n    /** Types registered via `register()`. */\n\n    this.types = {};\n  }\n  /**\n  * @typedef {null|boolean|number|string|GenericArray|PlainObject} JSON\n  */\n\n  /**\n  * @callback JSONReplacer\n  * @param {\"\"|string} key\n  * @param {JSON} value\n  * @returns {number|string|boolean|null|PlainObject|undefined}\n  * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#The%20replacer%20parameter\n  */\n\n  /**\n   * Serialize given object to Typeson.\n   * Initial arguments work identical to those of `JSON.stringify`.\n   * The `replacer` argument has nothing to do with our replacers.\n   * @param {any} obj\n   * @param {JSONReplacer|string[]} replacer\n   * @param {number|string} space\n   * @param {TypesonOptions} opts\n   * @returns {string|Promise<string>} Promise resolves to a string\n   */\n\n\n  _createClass(Typeson, [{\n    key: \"stringify\",\n    value: function stringify(obj, replacer, space, opts) {\n      opts = _objectSpread2(_objectSpread2(_objectSpread2({}, this.options), opts), {}, {\n        stringification: true\n      });\n      var encapsulated = this.encapsulate(obj, null, opts);\n\n      if (isArray(encapsulated)) {\n        return JSON.stringify(encapsulated[0], replacer, space);\n      }\n\n      return encapsulated.then(function (res) {\n        return JSON.stringify(res, replacer, space);\n      });\n    }\n    /**\n     * Also sync but throws on non-sync result.\n     * @param {any} obj\n     * @param {JSONReplacer|string[]} replacer\n     * @param {number|string} space\n     * @param {TypesonOptions} opts\n     * @returns {string}\n     */\n\n  }, {\n    key: \"stringifySync\",\n    value: function stringifySync(obj, replacer, space, opts) {\n      return this.stringify(obj, replacer, space, _objectSpread2(_objectSpread2({\n        throwOnBadSyncType: true\n      }, opts), {}, {\n        sync: true\n      }));\n    }\n    /**\n     *\n     * @param {any} obj\n     * @param {JSONReplacer|string[]} replacer\n     * @param {number|string} space\n     * @param {TypesonOptions} opts\n     * @returns {Promise<string>}\n     */\n\n  }, {\n    key: \"stringifyAsync\",\n    value: function stringifyAsync(obj, replacer, space, opts) {\n      return this.stringify(obj, replacer, space, _objectSpread2(_objectSpread2({\n        throwOnBadSyncType: true\n      }, opts), {}, {\n        sync: false\n      }));\n    }\n    /**\n    * @callback JSONReviver\n    * @param {string} key\n    * @param {JSON} value\n    * @returns {JSON}\n    */\n\n    /**\n     * Parse Typeson back into an obejct.\n     * Initial arguments works identical to those of `JSON.parse()`.\n     * @param {string} text\n     * @param {JSONReviver} reviver This JSON reviver has nothing to do with\n     *   our revivers.\n     * @param {TypesonOptions} opts\n     * @returns {external:JSON}\n     */\n\n  }, {\n    key: \"parse\",\n    value: function parse(text, reviver, opts) {\n      opts = _objectSpread2(_objectSpread2(_objectSpread2({}, this.options), opts), {}, {\n        parse: true\n      });\n      return this.revive(JSON.parse(text, reviver), opts);\n    }\n    /**\n    * Also sync but throws on non-sync result.\n    * @param {string} text\n    * @param {JSONReviver} reviver This JSON reviver has nothing to do with\n    *   our revivers.\n    * @param {TypesonOptions} opts\n    * @returns {external:JSON}\n    */\n\n  }, {\n    key: \"parseSync\",\n    value: function parseSync(text, reviver, opts) {\n      return this.parse(text, reviver, _objectSpread2(_objectSpread2({\n        throwOnBadSyncType: true\n      }, opts), {}, {\n        sync: true\n      }));\n    }\n    /**\n    * @param {string} text\n    * @param {JSONReviver} reviver This JSON reviver has nothing to do with\n    *   our revivers.\n    * @param {TypesonOptions} opts\n    * @returns {Promise<external:JSON>} Resolves to `external:JSON`\n    */\n\n  }, {\n    key: \"parseAsync\",\n    value: function parseAsync(text, reviver, opts) {\n      return this.parse(text, reviver, _objectSpread2(_objectSpread2({\n        throwOnBadSyncType: true\n      }, opts), {}, {\n        sync: false\n      }));\n    }\n    /**\n    * @typedef {} StateObject\n    */\n\n    /**\n     *\n     * @param {any} obj\n     * @param {StateObject} stateObj\n     * @param {TypesonOptions} [opts={}]\n     * @returns {string[]|false}\n     */\n\n  }, {\n    key: \"specialTypeNames\",\n    value: function specialTypeNames(obj, stateObj) {\n      var opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      opts.returnTypeNames = true;\n      return this.encapsulate(obj, stateObj, opts);\n    }\n    /**\n     *\n     * @param {any} obj\n     * @param {PlainObject} stateObj\n     * @param {PlainObject} [opts={}]\n     * @returns {Promise<any>|GenericArray|PlainObject|string|false}\n     */\n\n  }, {\n    key: \"rootTypeName\",\n    value: function rootTypeName(obj, stateObj) {\n      var opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      opts.iterateNone = true;\n      return this.encapsulate(obj, stateObj, opts);\n    }\n    /**\n     * Encapsulate a complex object into a plain Object by replacing\n     * registered types with plain objects representing the types data.\n     *\n     * This method is used internally by `Typeson.stringify()`.\n     * @param {any} obj - Object to encapsulate.\n     * @param {PlainObject} stateObj\n     * @param {PlainObject} opts\n     * @returns {Promise<any>|GenericArray|PlainObject|string|false}\n     */\n\n  }, {\n    key: \"encapsulate\",\n    value: function encapsulate(obj, stateObj, opts) {\n      /**\n       *\n       * @param {any} ret\n       * @param {GenericArray} promisesData\n       * @returns {Promise<any>}\n       */\n      var checkPromises = _async(function (ret, promisesData) {\n        return _await(Promise.all(promisesData.map(function (pd) {\n          return pd[1].p;\n        })), function (promResults) {\n          return _await(Promise.all(promResults.map(_async(function (promResult) {\n            var _exit = false;\n            var newPromisesData = [];\n\n            var _promisesData$splice = promisesData.splice(0, 1),\n                _promisesData$splice2 = _slicedToArray(_promisesData$splice, 1),\n                prData = _promisesData$splice2[0];\n\n            var _prData = _slicedToArray(prData, 7),\n                keyPath = _prData[0],\n                cyclic = _prData[2],\n                stateObj = _prData[3],\n                parentObj = _prData[4],\n                key = _prData[5],\n                detectedType = _prData[6];\n\n            var encaps = _encapsulate(keyPath, promResult, cyclic, stateObj, newPromisesData, true, detectedType);\n\n            var isTypesonPromise = hasConstructorOf(encaps, TypesonPromise); // Handle case where an embedded custom type itself\n            //   returns a `Typeson.Promise`\n\n            return _invoke(function () {\n              if (keyPath && isTypesonPromise) {\n                return _await(encaps.p, function (encaps2) {\n                  parentObj[key] = encaps2;\n                  _exit = true;\n                  return checkPromises(ret, newPromisesData);\n                });\n              }\n            }, function (_result) {\n              if (_exit) return _result;\n\n              if (keyPath) {\n                parentObj[key] = encaps;\n              } else if (isTypesonPromise) {\n                ret = encaps.p;\n              } else {\n                // If this is itself a `Typeson.Promise` (because the\n                //   original value supplied was a `Promise` or\n                //   because the supplied custom type value resolved\n                //   to one), returning it below will be fine since\n                //   a `Promise` is expected anyways given current\n                //   config (and if not a `Promise`, it will be ready\n                //   as the resolve value)\n                ret = encaps;\n              }\n\n              return checkPromises(ret, newPromisesData);\n            });\n          }))), function () {\n            return ret;\n          });\n        });\n      });\n      /**\n      * @typedef {PlainObject} OwnKeysObject\n      * @property {boolean} ownKeys\n      */\n\n      /**\n      * @callback BuiltinStateObjectPropertiesCallback\n      * @returns {void}\n      */\n\n      /**\n       *\n       * @param {StateObject} stateObj\n       * @param {OwnKeysObject} ownKeysObj\n       * @param {BuiltinStateObjectPropertiesCallback} cb\n       * @returns {undefined}\n       */\n\n\n      opts = _objectSpread2(_objectSpread2({\n        sync: true\n      }, this.options), opts);\n      var _opts = opts,\n          sync = _opts.sync;\n      var that = this,\n          types = {},\n          refObjs = [],\n          // For checking cyclic references\n      refKeys = [],\n          // For checking cyclic references\n      promisesDataRoot = []; // Clone the object deeply while at the same time replacing any\n      //   special types or cyclic reference:\n\n      var cyclic = 'cyclic' in opts ? opts.cyclic : true;\n      var _opts2 = opts,\n          encapsulateObserver = _opts2.encapsulateObserver;\n\n      var ret = _encapsulate('', obj, cyclic, stateObj || {}, promisesDataRoot);\n      /**\n       *\n       * @param {any} ret\n       * @returns {GenericArray|PlainObject|string|false}\n       */\n\n\n      function finish(ret) {\n        // Add `$types` to result only if we ever bumped into a\n        //  special type (or special case where object has own `$types`)\n        var typeNames = Object.values(types);\n\n        if (opts.iterateNone) {\n          if (typeNames.length) {\n            return typeNames[0];\n          }\n\n          return Typeson.getJSONType(ret);\n        }\n\n        if (typeNames.length) {\n          if (opts.returnTypeNames) {\n            return _toConsumableArray(new Set(typeNames));\n          } // Special if array (or a primitive) was serialized\n          //   because JSON would ignore custom `$types` prop on it\n\n\n          if (!ret || !isPlainObject(ret) || // Also need to handle if this is an object with its\n          //   own `$types` property (to avoid ambiguity)\n          hasOwn.call(ret, '$types')) {\n            ret = {\n              $: ret,\n              $types: {\n                $: types\n              }\n            };\n          } else {\n            ret.$types = types;\n          } // No special types\n\n        } else if (isObject(ret) && hasOwn.call(ret, '$types')) {\n          ret = {\n            $: ret,\n            $types: true\n          };\n        }\n\n        if (opts.returnTypeNames) {\n          return false;\n        }\n\n        return ret;\n      }\n\n      function _adaptBuiltinStateObjectProperties(stateObj, ownKeysObj, cb) {\n        Object.assign(stateObj, ownKeysObj);\n        var vals = internalStateObjPropsToIgnore.map(function (prop) {\n          var tmp = stateObj[prop];\n          delete stateObj[prop];\n          return tmp;\n        }); // eslint-disable-next-line node/callback-return\n\n        cb();\n        internalStateObjPropsToIgnore.forEach(function (prop, i) {\n          stateObj[prop] = vals[i];\n        });\n      }\n      /**\n       *\n       * @param {string} keypath\n       * @param {any} value\n       * @param {boolean} cyclic\n       * @param {PlainObject} stateObj\n       * @param {boolean} promisesData\n       * @param {boolean} resolvingTypesonPromise\n       * @param {string} detectedType\n       * @returns {any}\n       */\n\n\n      function _encapsulate(keypath, value, cyclic, stateObj, promisesData, resolvingTypesonPromise, detectedType) {\n        var ret;\n        var observerData = {};\n\n        var $typeof = _typeof(value);\n\n        var runObserver = encapsulateObserver ? function (obj) {\n          var type = detectedType || stateObj.type || Typeson.getJSONType(value);\n          encapsulateObserver(Object.assign(obj || observerData, {\n            keypath: keypath,\n            value: value,\n            cyclic: cyclic,\n            stateObj: stateObj,\n            promisesData: promisesData,\n            resolvingTypesonPromise: resolvingTypesonPromise,\n            awaitingTypesonPromise: hasConstructorOf(value, TypesonPromise)\n          }, {\n            type: type\n          }));\n        } : null;\n\n        if (['string', 'boolean', 'number', 'undefined'].includes($typeof)) {\n          if (value === undefined || Number.isNaN(value) || value === Number.NEGATIVE_INFINITY || value === Number.POSITIVE_INFINITY) {\n            ret = stateObj.replaced ? value : replace(keypath, value, stateObj, promisesData, false, resolvingTypesonPromise, runObserver);\n\n            if (ret !== value) {\n              observerData = {\n                replaced: ret\n              };\n            }\n          } else {\n            ret = value;\n          }\n\n          if (runObserver) {\n            runObserver();\n          }\n\n          return ret;\n        }\n\n        if (value === null) {\n          if (runObserver) {\n            runObserver();\n          }\n\n          return value;\n        }\n\n        if (cyclic && !stateObj.iterateIn && !stateObj.iterateUnsetNumeric && value && _typeof(value) === 'object') {\n          // Options set to detect cyclic references and be able\n          //   to rewrite them.\n          var refIndex = refObjs.indexOf(value);\n\n          if (refIndex < 0) {\n            if (cyclic === true) {\n              refObjs.push(value);\n              refKeys.push(keypath);\n            }\n          } else {\n            types[keypath] = '#';\n\n            if (runObserver) {\n              runObserver({\n                cyclicKeypath: refKeys[refIndex]\n              });\n            }\n\n            return '#' + refKeys[refIndex];\n          }\n        }\n\n        var isPlainObj = isPlainObject(value);\n        var isArr = isArray(value);\n        var replaced = // Running replace will cause infinite loop as will test\n        //   positive again\n        (isPlainObj || isArr) && (!that.plainObjectReplacers.length || stateObj.replaced) || stateObj.iterateIn ? // Optimization: if plain object and no plain-object\n        //   replacers, don't try finding a replacer\n        value : replace(keypath, value, stateObj, promisesData, isPlainObj || isArr, null, runObserver);\n        var clone;\n\n        if (replaced !== value) {\n          ret = replaced;\n          observerData = {\n            replaced: replaced\n          };\n        } else {\n          // eslint-disable-next-line no-lonely-if\n          if (keypath === '' && hasConstructorOf(value, TypesonPromise)) {\n            promisesData.push([keypath, value, cyclic, stateObj, undefined, undefined, stateObj.type]);\n            ret = value;\n          } else if (isArr && stateObj.iterateIn !== 'object' || stateObj.iterateIn === 'array') {\n            // eslint-disable-next-line unicorn/no-new-array -- Sparse\n            clone = new Array(value.length);\n            observerData = {\n              clone: clone\n            };\n          } else if (!['function', 'symbol'].includes(_typeof(value)) && !('toJSON' in value) && !hasConstructorOf(value, TypesonPromise) && !hasConstructorOf(value, Promise) && !hasConstructorOf(value, ArrayBuffer) || isPlainObj || stateObj.iterateIn === 'object') {\n            clone = {};\n\n            if (stateObj.addLength) {\n              clone.length = value.length;\n            }\n\n            observerData = {\n              clone: clone\n            };\n          } else {\n            ret = value; // Only clone vanilla objects and arrays\n          }\n        }\n\n        if (runObserver) {\n          runObserver();\n        }\n\n        if (opts.iterateNone) {\n          return clone || ret;\n        }\n\n        if (!clone) {\n          return ret;\n        } // Iterate object or array\n\n\n        if (stateObj.iterateIn) {\n          var _loop = function _loop(key) {\n            var ownKeysObj = {\n              ownKeys: hasOwn.call(value, key)\n            };\n\n            _adaptBuiltinStateObjectProperties(stateObj, ownKeysObj, function () {\n              var kp = keypath + (keypath ? '.' : '') + escapeKeyPathComponent(key);\n\n              var val = _encapsulate(kp, value[key], Boolean(cyclic), stateObj, promisesData, resolvingTypesonPromise);\n\n              if (hasConstructorOf(val, TypesonPromise)) {\n                promisesData.push([kp, val, Boolean(cyclic), stateObj, clone, key, stateObj.type]);\n              } else if (val !== undefined) {\n                clone[key] = val;\n              }\n            });\n          };\n\n          // eslint-disable-next-line guard-for-in\n          for (var key in value) {\n            _loop(key);\n          }\n\n          if (runObserver) {\n            runObserver({\n              endIterateIn: true,\n              end: true\n            });\n          }\n        } else {\n          // Note: Non-indexes on arrays won't survive stringify so\n          //  somewhat wasteful for arrays, but so too is iterating\n          //  all numeric indexes on sparse arrays when not wanted\n          //  or filtering own keys for positive integers\n          keys(value).forEach(function (key) {\n            var kp = keypath + (keypath ? '.' : '') + escapeKeyPathComponent(key);\n            var ownKeysObj = {\n              ownKeys: true\n            };\n\n            _adaptBuiltinStateObjectProperties(stateObj, ownKeysObj, function () {\n              var val = _encapsulate(kp, value[key], Boolean(cyclic), stateObj, promisesData, resolvingTypesonPromise);\n\n              if (hasConstructorOf(val, TypesonPromise)) {\n                promisesData.push([kp, val, Boolean(cyclic), stateObj, clone, key, stateObj.type]);\n              } else if (val !== undefined) {\n                clone[key] = val;\n              }\n            });\n          });\n\n          if (runObserver) {\n            runObserver({\n              endIterateOwn: true,\n              end: true\n            });\n          }\n        } // Iterate array for non-own numeric properties (we can't\n        //   replace the prior loop though as it iterates non-integer\n        //   keys)\n\n\n        if (stateObj.iterateUnsetNumeric) {\n          var vl = value.length;\n\n          var _loop2 = function _loop2(i) {\n            if (!(i in value)) {\n              // No need to escape numeric\n              var kp = keypath + (keypath ? '.' : '') + i;\n              var ownKeysObj = {\n                ownKeys: false\n              };\n\n              _adaptBuiltinStateObjectProperties(stateObj, ownKeysObj, function () {\n                var val = _encapsulate(kp, undefined, Boolean(cyclic), stateObj, promisesData, resolvingTypesonPromise);\n\n                if (hasConstructorOf(val, TypesonPromise)) {\n                  promisesData.push([kp, val, Boolean(cyclic), stateObj, clone, i, stateObj.type]);\n                } else if (val !== undefined) {\n                  clone[i] = val;\n                }\n              });\n            }\n          };\n\n          for (var i = 0; i < vl; i++) {\n            _loop2(i);\n          }\n\n          if (runObserver) {\n            runObserver({\n              endIterateUnsetNumeric: true,\n              end: true\n            });\n          }\n        }\n\n        return clone;\n      }\n      /**\n      * @typedef {PlainObject} KeyPathEvent\n      * @property {string} cyclicKeypath\n      */\n\n      /**\n      * @typedef {PlainObject} EndIterateInEvent\n      * @property {boolean} endIterateIn\n      * @property {boolean} end\n      */\n\n      /**\n      * @typedef {PlainObject} EndIterateUnsetNumericEvent\n      * @property {boolean} endIterateUnsetNumeric\n      * @property {boolean} end\n      */\n\n      /**\n      * @typedef {PlainObject} TypeDetectedEvent\n      * @property {boolean} typeDetected\n      */\n\n      /**\n      * @typedef {PlainObject} ReplacingEvent\n      * @property {boolean} replacing\n      */\n\n      /**\n      * @callback Observer\n      * @param {KeyPathEvent|EndIterateInEvent|EndIterateUnsetNumericEvent|\n      * TypeDetectedEvent|ReplacingEvent} [event]\n      * @returns {void}\n      */\n\n      /**\n       *\n       * @param {string} keypath\n       * @param {any} value\n       * @param {PlainObject} stateObj\n       * @param {GenericArray} promisesData\n       * @param {boolean} plainObject\n       * @param {boolean} resolvingTypesonPromise\n       * @param {Observer} [runObserver]\n       * @returns {any}\n       */\n\n\n      function replace(keypath, value, stateObj, promisesData, plainObject, resolvingTypesonPromise, runObserver) {\n        // Encapsulate registered types\n        var replacers = plainObject ? that.plainObjectReplacers : that.nonplainObjectReplacers;\n        var i = replacers.length;\n\n        while (i--) {\n          var replacer = replacers[i];\n\n          if (replacer.test(value, stateObj)) {\n            var type = replacer.type;\n\n            if (that.revivers[type]) {\n              // Record the type only if a corresponding reviver\n              //   exists. This is to support specs where only\n              //   replacement is done.\n              // For example, ensuring deep cloning of the object,\n              //   or replacing a type to its equivalent without\n              //   the need to revive it.\n              var existing = types[keypath]; // type can comprise an array of types (see test\n              //   \"should support intermediate types\")\n\n              types[keypath] = existing ? [type].concat(existing) : type;\n            }\n\n            Object.assign(stateObj, {\n              type: type,\n              replaced: true\n            });\n\n            if ((sync || !replacer.replaceAsync) && !replacer.replace) {\n              if (runObserver) {\n                runObserver({\n                  typeDetected: true\n                });\n              }\n\n              return _encapsulate(keypath, value, cyclic && 'readonly', stateObj, promisesData, resolvingTypesonPromise, type);\n            }\n\n            if (runObserver) {\n              runObserver({\n                replacing: true\n              });\n            } // Now, also traverse the result in case it contains its\n            //   own types to replace\n\n\n            var replaceMethod = sync || !replacer.replaceAsync ? 'replace' : 'replaceAsync';\n            return _encapsulate(keypath, replacer[replaceMethod](value, stateObj), cyclic && 'readonly', stateObj, promisesData, resolvingTypesonPromise, type);\n          }\n        }\n\n        return value;\n      }\n\n      return promisesDataRoot.length ? sync && opts.throwOnBadSyncType ? function () {\n        throw new TypeError('Sync method requested but async result obtained');\n      }() : Promise.resolve(checkPromises(ret, promisesDataRoot)).then(finish) : !sync && opts.throwOnBadSyncType ? function () {\n        throw new TypeError('Async method requested but sync result obtained');\n      }() // If this is a synchronous request for stringification, yet\n      //   a promise is the result, we don't want to resolve leading\n      //   to an async result, so we return an array to avoid\n      //   ambiguity\n      : opts.stringification && sync ? [finish(ret)] : sync ? finish(ret) : Promise.resolve(finish(ret));\n    }\n    /**\n     * Also sync but throws on non-sync result.\n     * @param {any} obj\n     * @param {StateObject} stateObj\n     * @param {TypesonOptions} opts\n     * @returns {any}\n     */\n\n  }, {\n    key: \"encapsulateSync\",\n    value: function encapsulateSync(obj, stateObj, opts) {\n      return this.encapsulate(obj, stateObj, _objectSpread2(_objectSpread2({\n        throwOnBadSyncType: true\n      }, opts), {}, {\n        sync: true\n      }));\n    }\n    /**\n     * @param {any} obj\n     * @param {StateObject} stateObj\n     * @param {TypesonOptions} opts\n     * @returns {any}\n     */\n\n  }, {\n    key: \"encapsulateAsync\",\n    value: function encapsulateAsync(obj, stateObj, opts) {\n      return this.encapsulate(obj, stateObj, _objectSpread2(_objectSpread2({\n        throwOnBadSyncType: true\n      }, opts), {}, {\n        sync: false\n      }));\n    }\n    /**\n     * Revive an encapsulated object.\n     * This method is used internally by `Typeson.parse()`.\n     * @param {PlainObject} obj - Object to revive. If it has `$types` member,\n     *   the properties that are listed there will be replaced with its true\n     *   type instead of just plain objects.\n     * @param {TypesonOptions} opts\n     * @throws TypeError If mismatch between sync/async type and result\n     * @returns {Promise<any>|any} If async, returns a Promise that resolves\n     * to `any`.\n     */\n\n  }, {\n    key: \"revive\",\n    value: function revive(obj, opts) {\n      var types = obj && obj.$types; // No type info added. Revival not needed.\n\n      if (!types) {\n        return obj;\n      } // Object happened to have own `$types` property but with\n      //   no actual types, so we unescape and return that object\n\n\n      if (types === true) {\n        return obj.$;\n      }\n\n      opts = _objectSpread2(_objectSpread2({\n        sync: true\n      }, this.options), opts);\n      var _opts3 = opts,\n          sync = _opts3.sync;\n      var keyPathResolutions = [];\n      var stateObj = {};\n      var ignore$Types = true; // Special when root object is not a trivial Object, it will\n      //   be encapsulated in `$`. It will also be encapsulated in\n      //   `$` if it has its own `$` property to avoid ambiguity\n\n      if (types.$ && isPlainObject(types.$)) {\n        obj = obj.$;\n        types = types.$;\n        ignore$Types = false;\n      }\n\n      var that = this;\n      /**\n       * @callback RevivalReducer\n       * @param {any} value\n       * @param {string} type\n       * @returns {any}\n       */\n\n      /**\n       *\n       * @param {string} type\n       * @param {any} val\n       * @throws {Error}\n       * @returns {any}\n       */\n\n      function executeReviver(type, val) {\n        var _ref = that.revivers[type] || [],\n            _ref2 = _slicedToArray(_ref, 1),\n            reviver = _ref2[0];\n\n        if (!reviver) {\n          throw new Error('Unregistered type: ' + type);\n        } // Only `sync` expected here, as problematic async would\n        //  be missing both `reviver` and `reviverAsync`, and\n        //  encapsulation shouldn't have added types, so\n        //  should have made an early exit\n\n\n        if (sync && !('revive' in reviver)) {\n          // Just return value as is\n          return val;\n        }\n\n        return reviver[sync && reviver.revive ? 'revive' : !sync && reviver.reviveAsync ? 'reviveAsync' : 'revive'](val, stateObj);\n      }\n      /**\n       *\n       * @returns {void|TypesonPromise<void>}\n       */\n\n\n      function revivePlainObjects() {\n        // const references = [];\n        // const reviveTypes = [];\n        var plainObjectTypes = [];\n        Object.entries(types).forEach(function (_ref3) {\n          var _ref4 = _slicedToArray(_ref3, 2),\n              keypath = _ref4[0],\n              type = _ref4[1];\n\n          if (type === '#') {\n            /*\n            references.push({\n                keypath,\n                reference: getByKeyPath(obj, keypath)\n            });\n            */\n            return;\n          }\n\n          [].concat(type).forEach(function (type) {\n            var _ref5 = that.revivers[type] || [null, {}],\n                _ref6 = _slicedToArray(_ref5, 2),\n                plain = _ref6[1].plain;\n\n            if (!plain) {\n              // reviveTypes.push({keypath, type});\n              return;\n            }\n\n            plainObjectTypes.push({\n              keypath: keypath,\n              type: type\n            });\n            delete types[keypath]; // Avoid repeating\n          });\n        });\n\n        if (!plainObjectTypes.length) {\n          return undefined;\n        } // console.log(plainObjectTypes.sort(nestedPathsFirst));\n\n        /**\n        * @typedef {PlainObject} PlainObjectType\n        * @property {string} keypath\n        * @property {string} type\n        */\n\n\n        return plainObjectTypes.sort(nestedPathsFirst).reduce(function reducer(possibleTypesonPromise, _ref7) {\n          var keypath = _ref7.keypath,\n              type = _ref7.type;\n\n          if (isThenable(possibleTypesonPromise)) {\n            return possibleTypesonPromise.then(function (val) {\n              return reducer(val, {\n                keypath: keypath,\n                type: type\n              });\n            });\n          } // console.log('obj', JSON.stringify(keypath), obj);\n\n\n          var val = getByKeyPath(obj, keypath);\n          val = executeReviver(type, val);\n\n          if (hasConstructorOf(val, TypesonPromise)) {\n            return val.then(function (v) {\n              var newVal = setAtKeyPath(obj, keypath, v);\n\n              if (newVal === v) {\n                obj = newVal;\n              }\n\n              return undefined;\n            });\n          }\n\n          var newVal = setAtKeyPath(obj, keypath, val);\n\n          if (newVal === val) {\n            obj = newVal;\n          }\n\n          return undefined;\n        }, undefined // This argument must be explicit\n        ); // references.forEach(({keypath, reference}) => {});\n        // reviveTypes.sort(nestedPathsFirst).forEach(() => {});\n      }\n\n      var revivalPromises = [];\n      /**\n       *\n       * @param {string} keypath\n       * @param {any} value\n       * @param {?(GenericArray|PlainObject)} target\n       * @param {GenericArray|PlainObject} [clone]\n       * @param {string} [key]\n       * @returns {any}\n       */\n\n      function _revive(keypath, value, target, clone, key) {\n        if (ignore$Types && keypath === '$types') {\n          return undefined;\n        }\n\n        var type = types[keypath];\n        var isArr = isArray(value);\n\n        if (isArr || isPlainObject(value)) {\n          // eslint-disable-next-line unicorn/no-new-array -- Sparse\n          var _clone = isArr ? new Array(value.length) : {}; // Iterate object or array\n\n\n          keys(value).forEach(function (k) {\n            var val = _revive(keypath + (keypath ? '.' : '') + escapeKeyPathComponent(k), value[k], target || _clone, _clone, k);\n\n            var set = function set(v) {\n              if (hasConstructorOf(v, Undefined)) {\n                _clone[k] = undefined;\n              } else if (v !== undefined) {\n                _clone[k] = v;\n              }\n\n              return v;\n            };\n\n            if (hasConstructorOf(val, TypesonPromise)) {\n              revivalPromises.push(val.then(function (ret) {\n                return set(ret);\n              }));\n            } else {\n              set(val);\n            }\n          });\n          value = _clone; // Try to resolve cyclic reference as soon as available\n\n          while (keyPathResolutions.length) {\n            var _keyPathResolutions$ = _slicedToArray(keyPathResolutions[0], 4),\n                _target = _keyPathResolutions$[0],\n                keyPath = _keyPathResolutions$[1],\n                _clone2 = _keyPathResolutions$[2],\n                k = _keyPathResolutions$[3];\n\n            var val = getByKeyPath(_target, keyPath); // Typeson.Undefined not expected here as not cyclic or\n            //   `undefined`\n\n            if (val !== undefined) {\n              _clone2[k] = val;\n            } else {\n              break;\n            }\n\n            keyPathResolutions.splice(0, 1);\n          }\n        }\n\n        if (!type) {\n          return value;\n        }\n\n        if (type === '#') {\n          var _ret = getByKeyPath(target, value.slice(1));\n\n          if (_ret === undefined) {\n            // Cyclic reference not yet available\n            keyPathResolutions.push([target, value.slice(1), clone, key]);\n          }\n\n          return _ret;\n        } // `type` can be an array here\n\n\n        return [].concat(type).reduce(function reducer(val, typ) {\n          if (hasConstructorOf(val, TypesonPromise)) {\n            return val.then(function (v) {\n              // TypesonPromise here too\n              return reducer(v, typ);\n            });\n          }\n\n          return executeReviver(typ, val);\n        }, value);\n      }\n      /**\n       *\n       * @param {any} retrn\n       * @returns {undefined|any}\n       */\n\n\n      function checkUndefined(retrn) {\n        return hasConstructorOf(retrn, Undefined) ? undefined : retrn;\n      }\n\n      var possibleTypesonPromise = revivePlainObjects();\n      var ret;\n\n      if (hasConstructorOf(possibleTypesonPromise, TypesonPromise)) {\n        ret = possibleTypesonPromise.then(function () {\n          return obj;\n        });\n      } else {\n        ret = _revive('', obj, null);\n\n        if (revivalPromises.length) {\n          // Ensure children resolved\n          ret = TypesonPromise.resolve(ret).then(function (r) {\n            return TypesonPromise.all([// May be a TypesonPromise or not\n            r].concat(revivalPromises));\n          }).then(function (_ref8) {\n            var _ref9 = _slicedToArray(_ref8, 1),\n                r = _ref9[0];\n\n            return r;\n          });\n        }\n      }\n\n      return isThenable(ret) ? sync && opts.throwOnBadSyncType ? function () {\n        throw new TypeError('Sync method requested but async result obtained');\n      }() : hasConstructorOf(ret, TypesonPromise) ? ret.p.then(checkUndefined) : ret : !sync && opts.throwOnBadSyncType ? function () {\n        throw new TypeError('Async method requested but sync result obtained');\n      }() : sync ? checkUndefined(ret) : Promise.resolve(checkUndefined(ret));\n    }\n    /**\n     * Also sync but throws on non-sync result.\n     * @param {any} obj\n     * @param {TypesonOptions} opts\n     * @returns {any}\n     */\n\n  }, {\n    key: \"reviveSync\",\n    value: function reviveSync(obj, opts) {\n      return this.revive(obj, _objectSpread2(_objectSpread2({\n        throwOnBadSyncType: true\n      }, opts), {}, {\n        sync: true\n      }));\n    }\n    /**\n    * @param {any} obj\n    * @param {TypesonOptions} opts\n    * @returns {Promise<any>}\n    */\n\n  }, {\n    key: \"reviveAsync\",\n    value: function reviveAsync(obj, opts) {\n      return this.revive(obj, _objectSpread2(_objectSpread2({\n        throwOnBadSyncType: true\n      }, opts), {}, {\n        sync: false\n      }));\n    }\n    /**\n    * @typedef {Tester|Replacer|Reviver} Spec\n    */\n\n    /**\n     * Register types.\n     * For examples on how to use this method, see\n     *   {@link https://github.com/dfahlander/typeson-registry/tree/master/types}.\n     * @param {object<string,Spec[]>[]} typeSpecSets -\n     * Types and their functions [test, encapsulate, revive];\n     * @param {TypesonOptions} opts\n     * @returns {Typeson}\n     */\n\n  }, {\n    key: \"register\",\n    value: function register(typeSpecSets, opts) {\n      opts = opts || {};\n      [].concat(typeSpecSets).forEach(function R(typeSpec) {\n        var _this = this;\n\n        // Allow arrays of arrays of arrays...\n        if (isArray(typeSpec)) {\n          return typeSpec.map(function (typSpec) {\n            return R.call(_this, typSpec);\n          });\n        }\n\n        typeSpec && keys(typeSpec).forEach(function (typeId) {\n          if (typeId === '#') {\n            throw new TypeError('# cannot be used as a type name as it is reserved ' + 'for cyclic objects');\n          } else if (Typeson.JSON_TYPES.includes(typeId)) {\n            throw new TypeError('Plain JSON object types are reserved as type names');\n          }\n\n          var spec = typeSpec[typeId];\n          var replacers = spec && spec.testPlainObjects ? this.plainObjectReplacers : this.nonplainObjectReplacers;\n          var existingReplacer = replacers.filter(function (r) {\n            return r.type === typeId;\n          });\n\n          if (existingReplacer.length) {\n            // Remove existing spec and replace with this one.\n            replacers.splice(replacers.indexOf(existingReplacer[0]), 1);\n            delete this.revivers[typeId];\n            delete this.types[typeId];\n          }\n\n          if (typeof spec === 'function') {\n            // Support registering just a class without replacer/reviver\n            var Class = spec;\n            spec = {\n              test: function test(x) {\n                return x && x.constructor === Class;\n              },\n              replace: function replace(x) {\n                return _objectSpread2({}, x);\n              },\n              revive: function revive(x) {\n                return Object.assign(Object.create(Class.prototype), x);\n              }\n            };\n          } else if (isArray(spec)) {\n            var _spec = spec,\n                _spec2 = _slicedToArray(_spec, 3),\n                test = _spec2[0],\n                replace = _spec2[1],\n                revive = _spec2[2];\n\n            spec = {\n              test: test,\n              replace: replace,\n              revive: revive\n            };\n          }\n\n          if (!spec || !spec.test) {\n            return;\n          }\n\n          var replacerObj = {\n            type: typeId,\n            test: spec.test.bind(spec)\n          };\n\n          if (spec.replace) {\n            replacerObj.replace = spec.replace.bind(spec);\n          }\n\n          if (spec.replaceAsync) {\n            replacerObj.replaceAsync = spec.replaceAsync.bind(spec);\n          }\n\n          var start = typeof opts.fallback === 'number' ? opts.fallback : opts.fallback ? 0 : Number.POSITIVE_INFINITY;\n\n          if (spec.testPlainObjects) {\n            this.plainObjectReplacers.splice(start, 0, replacerObj);\n          } else {\n            this.nonplainObjectReplacers.splice(start, 0, replacerObj);\n          } // Todo: We might consider a testAsync type\n\n\n          if (spec.revive || spec.reviveAsync) {\n            var reviverObj = {};\n\n            if (spec.revive) {\n              reviverObj.revive = spec.revive.bind(spec);\n            }\n\n            if (spec.reviveAsync) {\n              reviverObj.reviveAsync = spec.reviveAsync.bind(spec);\n            }\n\n            this.revivers[typeId] = [reviverObj, {\n              plain: spec.testPlainObjects\n            }];\n          } // Record to be retrieved via public types property.\n\n\n          this.types[typeId] = spec;\n        }, this);\n      }, this);\n      return this;\n    }\n  }]);\n\n  return Typeson;\n}();\n/**\n * We keep this function minimized so if using two instances of this\n * library, where one is minimized and one is not, it will still work\n * with `hasConstructorOf`.\n * @class\n */\n\n\nvar Undefined = function Undefined() {\n  _classCallCheck(this, Undefined);\n}; // eslint-disable-line space-before-blocks\n\n\nUndefined.__typeson__type__ = 'TypesonUndefined'; // The following provide classes meant to avoid clashes with other values\n// To insist `undefined` should be added\n\nTypeson.Undefined = Undefined; // To support async encapsulation/stringification\n\nTypeson.Promise = TypesonPromise; // Some fundamental type-checking utilities\n\nTypeson.isThenable = isThenable;\nTypeson.toStringTag = toStringTag;\nTypeson.hasConstructorOf = hasConstructorOf;\nTypeson.isObject = isObject;\nTypeson.isPlainObject = isPlainObject;\nTypeson.isUserObject = isUserObject;\nTypeson.escapeKeyPathComponent = escapeKeyPathComponent;\nTypeson.unescapeKeyPathComponent = unescapeKeyPathComponent;\nTypeson.getByKeyPath = getByKeyPath;\nTypeson.getJSONType = getJSONType;\nTypeson.JSON_TYPES = ['null', 'boolean', 'number', 'string', 'array', 'object'];\n\nexport default Typeson;\n", "/*\n * base64-arraybuffer\n * https://github.com/niklasvh/base64-arraybuffer\n *\n * Copyright (c) 2017 <PERSON>, 2012 <PERSON><PERSON>\n * Licensed under the MIT license.\n */\nvar chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'; // Use a lookup table to find the index.\n\nvar lookup = new Uint8Array(256);\n\nfor (var i = 0; i < chars.length; i++) {\n  lookup[chars.charCodeAt(i)] = i;\n}\n/**\n * @param {ArrayBuffer} arraybuffer\n * @param {Integer} byteOffset\n * @param {Integer} lngth\n * @returns {string}\n */\n\n\nvar encode = function encode(arraybuffer, byteOffset, lngth) {\n  if (lngth === null || lngth === undefined) {\n    lngth = arraybuffer.byteLength; // Needed for Safari\n  }\n\n  var bytes = new Uint8Array(arraybuffer, byteOffset || 0, // Default needed for Safari\n  lngth);\n  var len = bytes.length;\n  var base64 = '';\n\n  for (var _i = 0; _i < len; _i += 3) {\n    base64 += chars[bytes[_i] >> 2];\n    base64 += chars[(bytes[_i] & 3) << 4 | bytes[_i + 1] >> 4];\n    base64 += chars[(bytes[_i + 1] & 15) << 2 | bytes[_i + 2] >> 6];\n    base64 += chars[bytes[_i + 2] & 63];\n  }\n\n  if (len % 3 === 2) {\n    base64 = base64.slice(0, -1) + '=';\n  } else if (len % 3 === 1) {\n    base64 = base64.slice(0, -2) + '==';\n  }\n\n  return base64;\n};\n/**\n * @param {string} base64\n * @returns {ArrayBuffer}\n */\n\nvar decode = function decode(base64) {\n  var len = base64.length;\n  var bufferLength = base64.length * 0.75;\n  var p = 0;\n  var encoded1, encoded2, encoded3, encoded4;\n\n  if (base64[base64.length - 1] === '=') {\n    bufferLength--;\n\n    if (base64[base64.length - 2] === '=') {\n      bufferLength--;\n    }\n  }\n\n  var arraybuffer = new ArrayBuffer(bufferLength),\n      bytes = new Uint8Array(arraybuffer);\n\n  for (var _i2 = 0; _i2 < len; _i2 += 4) {\n    encoded1 = lookup[base64.charCodeAt(_i2)];\n    encoded2 = lookup[base64.charCodeAt(_i2 + 1)];\n    encoded3 = lookup[base64.charCodeAt(_i2 + 2)];\n    encoded4 = lookup[base64.charCodeAt(_i2 + 3)];\n    bytes[p++] = encoded1 << 2 | encoded2 >> 4;\n    bytes[p++] = (encoded2 & 15) << 4 | encoded3 >> 2;\n    bytes[p++] = (encoded3 & 3) << 6 | encoded4 & 63;\n  }\n\n  return arraybuffer;\n};\n\nexport { decode, encode };\n", "import Typeson from 'typeson';\nimport {encode, decode} from 'base64-arraybuffer-es6';\n\nconst arraybuffer = {\n    arraybuffer: {\n        test (x) { return Typeson.toStringTag(x) === 'ArrayBuffer'; },\n        replace (b, stateObj) {\n            if (!stateObj.buffers) {\n                stateObj.buffers = [];\n            }\n            const index = stateObj.buffers.indexOf(b);\n            if (index > -1) {\n                return {index};\n            }\n            stateObj.buffers.push(b);\n            return encode(b);\n        },\n        revive (b64, stateObj) {\n            if (!stateObj.buffers) {\n                stateObj.buffers = [];\n            }\n            if (typeof b64 === 'object') {\n                return stateObj.buffers[b64.index];\n            }\n            const buffer = decode(b64);\n            stateObj.buffers.push(buffer);\n            return buffer;\n        }\n    }\n};\n\nexport default arraybuffer;\n\n// See also typed-arrays!\n", "/* globals BigInt */\nimport Typeson from 'typeson';\n\nconst bigintObject = {\n    bigintObject: {\n        test (x) {\n            return typeof x === 'object' && Typeson.hasConstructorOf(x, BigInt);\n        },\n        replace (n) { return String(n); },\n        revive (s) {\n            // Filed this to avoid error: https://github.com/eslint/eslint/issues/11810\n            // eslint-disable-next-line no-new-object\n            return new Object(BigInt(s));\n        }\n    }\n};\n\nexport default bigintObject;\n", "/* globals BigInt */\n\nconst bigint = {\n    bigint: {\n        test (x) {\n            return typeof x === 'bigint';\n        },\n        replace (n) { return String(n); },\n        revive (s) { return BigInt(s); }\n    }\n};\n\nexport default bigint;\n", "/**\n * Not currently in use internally, but provided for parity.\n * @param {<PERSON><PERSON>y<PERSON>uffer} buf\n * @returns {Uint8Array}\n */\nfunction arraybuffer2string (buf) {\n    return new Uint8Array(buf).reduce(\n        (s, byte) => s + String.fromCharCode(byte), ''\n    );\n}\n\n/**\n *\n * @param {string} str\n * @returns {ArrayBuffer}\n */\nfunction string2arraybuffer (str) {\n    /*\n    // UTF-8 approaches\n    const utf8 = unescape(encodeURIComponent(str));\n    const arr = new Uint8Array(utf8.length);\n    for (let i = 0; i < utf8.length; i++) {\n        arr[i] = utf8.charCodeAt(i);\n    }\n    return arr.buffer;\n\n    const utf8 = [];\n    for (let i = 0; i < str.length; i++) {\n        let charcode = str.charCodeAt(i);\n        if (charcode < 0x80) utf8.push(charcode);\n        else if (charcode < 0x800) {\n            utf8.push(0xc0 | (charcode >> 6),\n                0x80 | (charcode & 0x3f));\n        } else if (charcode < 0xd800 || charcode >= 0xe000) {\n            utf8.push(0xe0 | (charcode >> 12),\n                0x80 | ((charcode >> 6) & 0x3f),\n                0x80 | (charcode & 0x3f));\n        // surrogate pair\n        } else {\n            i++;\n            // UTF-16 encodes 0x10000-0x10FFFF by\n            // subtracting 0x10000 and splitting the\n            // 20 bits of 0x0-0xFFFFF into two halves\n            charcode = 0x10000 + (((charcode & 0x3ff) << 10) |\n                (str.charCodeAt(i) & 0x3ff));\n            utf8.push(0xf0 | (charcode >> 18),\n                0x80 | ((charcode >> 12) & 0x3f),\n                0x80 | ((charcode >> 6) & 0x3f),\n                0x80 | (charcode & 0x3f));\n        }\n    }\n    return utf8;\n    */\n    /*\n    // Working UTF-16 options (equivalents)\n    const buf = new ArrayBuffer(str.length * 2); // 2 bytes for each char\n    const bufView = new Uint16Array(buf);\n    for (let i = 0, strLen = str.length; i < strLen; i++) {\n        bufView[i] = str.charCodeAt(i);\n    }\n    return buf;\n    */\n\n    const array = new Uint8Array(str.length);\n    for (let i = 0; i < str.length; i++) {\n        array[i] = str.charCodeAt(i); // & 0xff;\n    }\n    return array.buffer;\n}\nexport {arraybuffer2string, string2arraybuffer};\n", "/* globals XMLHttpRequest, Blob, FileReader */\nimport Typeson from 'typeson';\nimport {string2arraybuffer} from '../utils/stringArrayBuffer.js';\n\nconst blob = {\n    blob: {\n        test (x) { return Typeson.toStringTag(x) === 'Blob'; },\n        replace (b) { // Sync\n            const req = new XMLHttpRequest();\n            req.overrideMimeType('text/plain; charset=x-user-defined');\n            req.open('GET', URL.createObjectURL(b), false); // Sync\n            req.send();\n\n            // Seems not feasible to accurately simulate\n            /* istanbul ignore next */\n            if (req.status !== 200 && req.status !== 0) {\n                throw new Error('Bad Blob access: ' + req.status);\n            }\n            return {\n                type: b.type,\n                stringContents: req.responseText\n            };\n        },\n        revive ({type, stringContents}) {\n            return new Blob([string2arraybuffer(stringContents)], {type});\n        },\n        replaceAsync (b) {\n            return new Typeson.Promise((resolve, reject) => {\n                /*\n                if (b.isClosed) { // On MDN, but not in https://w3c.github.io/FileAPI/#dfn-Blob\n                    reject(new Error('The Blob is closed'));\n                    return;\n                }\n                */\n                const reader = new FileReader();\n                reader.addEventListener('load', () => {\n                    resolve({\n                        type: b.type,\n                        stringContents: reader.result\n                    });\n                });\n                // Seems not feasible to accurately simulate\n                /* istanbul ignore next */\n                reader.addEventListener('error', () => {\n                    reject(reader.error);\n                });\n                reader.readAsBinaryString(b);\n            });\n        }\n    }\n};\n\nexport default blob;\n", "/* globals performance */\n\n// The `performance` global is optional\n\n/**\n * @todo We could use `import generateUUID from 'uuid/v4';` (but it needs\n *   crypto library, etc.; `rollup-plugin-node-builtins` doesn't recommend\n *   using its own version and though there is <https://www.npmjs.com/package/crypto-browserify>,\n *   it may be troublesome to bundle and not strongly needed)\n * @returns {string}\n */\nexport default function generateUUID () { //  Adapted from original: public domain/MIT: http://stackoverflow.com/a/8809472/271577\n    /* istanbul ignore next */\n    let d = Date.now() +\n        // use high-precision timer if available\n        // istanbul ignore next\n        (typeof performance !== 'undefined' &&\n            typeof performance.now === 'function'\n            ? performance.now()\n            : 0);\n\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/gu, function (c) {\n        /* eslint-disable no-bitwise */\n        const r = Math.trunc((d + Math.random() * 16) % 16);\n        d = Math.floor(d / 16);\n        return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);\n        /* eslint-enable no-bitwise */\n    });\n}\n", "import generateUUID from '../utils/generateUUID.js';\n\nconst cloneableObjectsByUUID = {};\n\nconst cloneable = {\n    cloneable: {\n        test (x) {\n            return x && typeof x === 'object' &&\n                typeof x[Symbol.for('cloneEncapsulate')] === 'function';\n        },\n        replace (clonable) {\n            const encapsulated = clonable[Symbol.for('cloneEncapsulate')]();\n            const uuid = generateUUID();\n            cloneableObjectsByUUID[uuid] = clonable;\n            return {uuid, encapsulated};\n        },\n        revive ({uuid, encapsulated}) {\n            return cloneableObjectsByUUID[uuid][Symbol.for('cloneRevive')](\n                encapsulated\n            );\n        }\n    }\n};\n\nexport default cloneable;\n", "/* globals crypto */\nimport Typeson from 'typeson';\n\nconst cryptokey = {\n    cryptokey: {\n        test (x) {\n            return Typeson.toStringTag(x) === 'CryptoKey' && x.extractable;\n        },\n        replaceAsync (key) {\n            return new Typeson.Promise((resolve, reject) => {\n                // eslint-disable-next-line promise/catch-or-return\n                crypto.subtle.exportKey('jwk', key).catch(\n                    /* eslint-disable promise/prefer-await-to-callbacks */\n                    // istanbul ignore next\n                    (err) => {\n                        /* eslint-enable promise/prefer-await-to-callbacks */\n                        // eslint-disable-next-line max-len\n                        // istanbul ignore next -- Our format should be valid and our key extractable\n                        reject(err);\n                    }\n                // eslint-disable-next-line max-len\n                // eslint-disable-next-line promise/always-return, promise/prefer-await-to-then\n                ).then((jwk) => {\n                    resolve({\n                        jwk,\n                        algorithm: key.algorithm,\n                        usages: key.usages\n                    });\n                });\n            });\n        },\n        revive ({jwk, algorithm, usages}) {\n            return crypto.subtle.importKey('jwk', jwk, algorithm, true, usages);\n        }\n    }\n};\n\nexport default cryptokey;\n", "import Typeson from 'typeson';\nimport {encode, decode} from 'base64-arraybuffer-es6';\n\nconst dataview = {\n    dataview: {\n        test (x) { return Typeson.toStringTag(x) === 'DataView'; },\n        replace ({buffer, byteOffset, byteLength}, stateObj) {\n            if (!stateObj.buffers) {\n                stateObj.buffers = [];\n            }\n            const index = stateObj.buffers.indexOf(buffer);\n            if (index > -1) {\n                return {index, byteOffset, byteLength};\n            }\n            stateObj.buffers.push(buffer);\n            return {\n                encoded: encode(buffer),\n                byteOffset,\n                byteLength\n            };\n        },\n        revive (b64Obj, stateObj) {\n            if (!stateObj.buffers) {\n                stateObj.buffers = [];\n            }\n            const {byteOffset, byteLength, encoded, index} = b64Obj;\n            let buffer;\n            if ('index' in b64Obj) {\n                buffer = stateObj.buffers[index];\n            } else {\n                buffer = decode(encoded);\n                stateObj.buffers.push(buffer);\n            }\n            return new DataView(buffer, byteOffset, byteLength);\n        }\n    }\n};\n\nexport default dataview;\n", "import Typeson from 'typeson';\n\nconst date = {\n    date: {\n        test (x) { return Typeson.toStringTag(x) === 'Date'; },\n        replace (dt) {\n            const time = dt.getTime();\n            if (Number.isNaN(time)) {\n                return 'NaN';\n            }\n            return time;\n        },\n        revive (time) {\n            if (time === 'NaN') {\n                return new Date(Number.NaN);\n            }\n            return new Date(time);\n        }\n    }\n};\n\nexport default date;\n", "import Typeson from 'typeson';\n\nconst error = {\n    error: {\n        test (x) { return Typeson.toStringTag(x) === 'Error'; },\n        replace ({name, message}) {\n            return {name, message};\n        },\n        revive ({name, message}) {\n            const e = new Error(message);\n            e.name = name;\n            return e;\n        }\n    }\n};\n// See also errors.js that may be registered after having registered this type.\n\nexport default error;\n", "/* eslint-env browser, node */\nimport Typeson from 'typeson';\n\n/* istanbul ignore next */\nconst _global = typeof self === 'undefined' ? global : self;\n\nconst errors = {};\n// Comprises all built-in errors.\n[\n    'TypeError',\n    'RangeError',\n    'SyntaxError',\n    'ReferenceError',\n    'EvalError',\n    'URIError',\n    'InternalError' // non-standard\n].forEach((errName) => {\n    const Cnstrctr = _global[errName];\n    if (Cnstrctr) {\n        errors[errName.toLowerCase()] = {\n            test (x) { return Typeson.hasConstructorOf(x, Cnstrctr); },\n            replace (e) { return e.message; },\n            revive (message) { return new Cnstrctr(message); }\n        };\n    }\n});\n\nexport default errors;\n", "/* globals XMLHttpRequest, File, FileReader */\nimport Typeson from 'typeson';\nimport {string2arraybuffer} from '../utils/stringArrayBuffer.js';\n\nconst file = {\n    file: {\n        test (x) { return Typeson.toStringTag(x) === 'File'; },\n        replace (f) { // Sync\n            const req = new XMLHttpRequest();\n            req.overrideMimeType('text/plain; charset=x-user-defined');\n            req.open('GET', URL.createObjectURL(f), false); // Sync\n            req.send();\n\n            // Seems not feasible to accurately simulate\n            /* istanbul ignore next */\n            if (req.status !== 200 && req.status !== 0) {\n                throw new Error('Bad File access: ' + req.status);\n            }\n            return {\n                type: f.type,\n                stringContents: req.responseText,\n                name: f.name,\n                lastModified: f.lastModified\n            };\n        },\n        revive ({name, type, stringContents, lastModified}) {\n            return new File([string2arraybuffer(stringContents)], name, {\n                type,\n                lastModified\n            });\n        },\n        replaceAsync (f) {\n            return new Typeson.Promise(function (resolve, reject) {\n                /*\n                if (f.isClosed) { // On MDN, but not in https://w3c.github.io/FileAPI/#dfn-Blob\n                    reject(new Error('The File is closed'));\n                    return;\n                }\n                */\n                const reader = new FileReader();\n                reader.addEventListener('load', function () {\n                    resolve({\n                        type: f.type,\n                        stringContents: reader.result,\n                        name: f.name,\n                        lastModified: f.lastModified\n                    });\n                });\n                // Seems not feasible to accurately simulate\n                /* istanbul ignore next */\n                reader.addEventListener('error', function () {\n                    reject(reader.error);\n                });\n                reader.readAsBinaryString(f);\n            });\n        }\n    }\n};\n\nexport default file;\n", "import Typeson from 'typeson';\nimport file from './file.js';\n\nconst filelist = {\n    file: file.file,\n    filelist: {\n        test (x) { return Typeson.toStringTag(x) === 'FileList'; },\n        replace (fl) {\n            const arr = [];\n            for (let i = 0; i < fl.length; i++) {\n                arr[i] = fl.item(i);\n            }\n            return arr;\n        },\n        revive (o) {\n            /**\n             * `FileList` polyfill.\n             */\n            class FileList {\n                /**\n                 * Set private properties and length.\n                 */\n                constructor () {\n                    // eslint-disable-next-line prefer-rest-params\n                    this._files = arguments[0];\n                    this.length = this._files.length;\n                }\n                /**\n                 * @param {Integer} index\n                 * @returns {File}\n                 */\n                item (index) {\n                    return this._files[index];\n                }\n                /* eslint-disable class-methods-use-this */\n                /**\n                 * @returns {\"FileList\"}\n                 */\n                get [Symbol.toStringTag] () {\n                    /* eslint-enable class-methods-use-this */\n                    return 'FileList';\n                }\n            }\n            return new FileList(o);\n        }\n    }\n};\n\nexport default filelist;\n", "/* globals createImageBitmap */\n// `ImageBitmap` is browser / DOM specific. It also can only work\n//  same-domain (or CORS)\n\nimport Typeson from 'typeson';\n\nconst imagebitmap = {\n    imagebitmap: {\n        test (x) {\n            return Typeson.toStringTag(x) === 'ImageBitmap' ||\n                // In Node, our polyfill sets the dataset on a canvas\n                //  element as JSDom no longer allows overriding toStringTag\n                (x && x.dataset && x.dataset.toStringTag === 'ImageBitmap');\n        },\n        replace (bm) {\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            ctx.drawImage(bm, 0, 0);\n            // Although `width` and `height` are part of `ImageBitMap`,\n            //   these will be auto-created for us when reviving with the\n            //   data URL (and they are not settable even if they weren't)\n            // return {\n            //   width: bm.width, height: bm.height, dataURL: canvas.toDataURL()\n            // };\n            return canvas.toDataURL();\n        },\n        revive (o) {\n            /*\n            var req = new XMLHttpRequest();\n            req.open('GET', o, false); // Sync\n            if (req.status !== 200 && req.status !== 0) {\n              throw new Error('Bad ImageBitmap access: ' + req.status);\n            }\n            req.send();\n            return req.responseText;\n            */\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            const img = document.createElement('img');\n            // The onload is needed by some browsers per http://stackoverflow.com/a/4776378/271577\n            img.addEventListener('load', function () {\n                ctx.drawImage(img, 0, 0);\n            });\n            img.src = o;\n            // Works in contexts allowing an `ImageBitmap` (We might use\n            //   `OffscreenCanvas.transferToBitmap` when supported)\n            return canvas;\n        },\n        reviveAsync (o) {\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            const img = document.createElement('img');\n            // The onload is needed by some browsers per http://stackoverflow.com/a/4776378/271577\n            img.addEventListener('load', function () {\n                ctx.drawImage(img, 0, 0);\n            });\n            img.src = o; // o.dataURL;\n            return createImageBitmap(canvas); // Returns a promise\n        }\n    }\n};\n\nexport default imagebitmap;\n", "/* globals ImageData */\n// `ImageData` is browser / DOM specific (though `node-canvas` has it\n//   available on `Canvas`).\n\nimport Typeson from 'typeson';\n\nconst imagedata = {\n    imagedata: {\n        test (x) { return Typeson.toStringTag(x) === 'ImageData'; },\n        replace (d) {\n            return {\n                // Ensure `length` gets preserved for revival\n                array: [...d.data],\n                width: d.width,\n                height: d.height\n            };\n        },\n        revive (o) {\n            return new ImageData(\n                new Uint8ClampedArray(o.array), o.width, o.height\n            );\n        }\n    }\n};\n\nexport default imagedata;\n", "const infinity = {\n    infinity: {\n        test (x) { return x === Number.POSITIVE_INFINITY; },\n        replace (n) { return 'Infinity'; },\n        revive (s) { return Number.POSITIVE_INFINITY; }\n    }\n};\n\nexport default infinity;\n", "import Typeson from 'typeson';\n\nconst IntlCollator = {\n    test (x) { return Typeson.hasConstructorOf(x, Intl.Collator); },\n    replace (c) { return c.resolvedOptions(); },\n    revive (options) { return new Intl.Collator(options.locale, options); }\n};\n\nconst IntlDateTimeFormat = {\n    test (x) { return Typeson.hasConstructorOf(x, Intl.DateTimeFormat); },\n    replace (dtf) { return dtf.resolvedOptions(); },\n    revive (options) {\n        return new Intl.DateTimeFormat(options.locale, options);\n    }\n};\n\nconst IntlNumberFormat = {\n    test (x) { return Typeson.hasConstructorOf(x, Intl.NumberFormat); },\n    replace (nf) { return nf.resolvedOptions(); },\n    revive (options) { return new Intl.NumberFormat(options.locale, options); }\n};\n\nconst intlTypes = {\n    IntlCollator,\n    IntlDateTimeFormat,\n    IntlNumberFormat\n};\n\nexport default intlTypes;\n", "import Typeson from 'typeson';\n\nconst map = {\n    map: {\n        test (x) { return Typeson.toStringTag(x) === 'Map'; },\n        replace (mp) { return [...mp.entries()]; },\n        revive (entries) { return new Map(entries); }\n    }\n};\n\nexport default map;\n", "const nan = {\n    nan: {\n        test (x) { return Number.isNaN(x); },\n        replace (n) { return 'NaN'; },\n        revive (s) { return Number.NaN; }\n    }\n};\n\nexport default nan;\n", "const negativeInfinity = {\n    negativeInfinity: {\n        test (x) { return x === Number.NEGATIVE_INFINITY; },\n        replace (n) { return '-Infinity'; },\n        revive (s) { return Number.NEGATIVE_INFINITY; }\n    }\n};\n\nexport default negativeInfinity;\n", "import Typeson from 'typeson';\n\nconst nonbuiltinIgnore = {\n    nonbuiltinIgnore: {\n        test (x) {\n            return x && typeof x === 'object' && !Array.isArray(x) && ![\n                'Object',\n                // `Proxy` and `Reflect`, two other built-in objects, will also\n                //   have a `toStringTag` of `Object`; we don't want built-in\n                //   function objects, however\n                'Boolean', 'Number', 'String',\n                'Error', 'RegExp', 'Math', 'Date',\n                'Map', 'Set',\n                'JSON',\n                'ArrayBuffer', 'SharedArrayBuffer', 'DataView',\n                'Int8Array', 'Uint8Array', 'Uint8ClampedArray', 'Int16Array',\n                'Uint16Array', 'Int32Array', 'Uint32Array',\n                'Float32Array', 'Float64Array',\n                'Promise',\n                'String Iterator', 'Array Iterator',\n                'Map Iterator', 'Set Iterator',\n                'WeakMap', 'WeakSet',\n                'Atomics', 'Module'\n            ].includes(Typeson.toStringTag(x));\n        },\n        replace (rexp) {\n            // Not in use\n        }\n    }\n};\n\nexport default nonbuiltinIgnore;\n", "// This module is for objectified primitives (such as `new Number(3)` or\n//      `new String(\"foo\")`)\n/* eslint-disable no-new-wrappers, unicorn/new-for-builtins */\nimport Typeson from 'typeson';\n\nconst primitiveObjects = {\n    // String Object (not primitive string which need no type spec)\n    StringObject: {\n        test (x) {\n            return Typeson.toStringTag(x) === 'String' && typeof x === 'object';\n        },\n        replace (s) { return String(s); }, // convert to primitive string\n        revive (s) { return new String(s); } // Revive to an objectified string\n    },\n    // Boolean Object (not primitive boolean which need no type spec)\n    BooleanObject: {\n        test (x) {\n            return Typeson.toStringTag(x) === 'Boolean' &&\n                typeof x === 'object';\n        },\n        replace (b) { return Boolean(b); }, // convert to primitive boolean\n        revive (b) {\n            // Revive to an objectified Boolean\n            return new Boolean(b);\n        }\n    },\n    // Number Object (not primitive number which need no type spec)\n    NumberObject: {\n        test (x) {\n            return Typeson.toStringTag(x) === 'Number' && typeof x === 'object';\n        },\n        replace (n) { return Number(n); }, // convert to primitive number\n        revive (n) { return new Number(n); } // Revive to an objectified number\n    }\n};\n/* eslint-enable no-new-wrappers, unicorn/new-for-builtins */\n\nexport default primitiveObjects;\n", "import Typeson from 'typeson';\n\nconst regexp = {\n    regexp: {\n        test (x) { return Typeson.toStringTag(x) === 'RegExp'; },\n        replace (rexp) {\n            return {\n                source: rexp.source,\n                flags: (rexp.global ? 'g' : '') +\n                    (rexp.ignoreCase ? 'i' : '') +\n                    (rexp.multiline ? 'm' : '') +\n                    (rexp.sticky ? 'y' : '') +\n                    (rexp.unicode ? 'u' : '')\n            };\n        },\n        revive ({source, flags}) { return new RegExp(source, flags); }\n    }\n};\n\nexport default regexp;\n", "// Here we allow the exact same non-plain object, function, and symbol\n//  instances to be resurrected (assuming the same session/environment);\n//  plain objects are ignored by <PERSON>son so not presently available and\n//  we consciously exclude arrays\n\nimport generateUUID from '../utils/generateUUID.js';\n\nconst resurrectableObjectsByUUID = {};\n\nconst resurrectable = {\n    resurrectable: {\n        test (x) {\n            return x &&\n                !Array.isArray(x) &&\n                ['object', 'function', 'symbol'].includes(typeof x);\n        },\n        replace (rsrrctble) {\n            const uuid = generateUUID();\n            resurrectableObjectsByUUID[uuid] = rsrrctble;\n            return uuid;\n        },\n        revive (serializedResurrectable) {\n            return resurrectableObjectsByUUID[serializedResurrectable];\n        }\n    }\n};\n\nexport default resurrectable;\n", "import Typeson from 'typeson';\n\nconst set = {\n    set: {\n        test (x) { return Typeson.toStringTag(x) === 'Set'; },\n        replace (st) {\n            return [...st.values()];\n        },\n        revive (values) { return new Set(values); }\n    }\n};\n\nexport default set;\n", "/* eslint-env browser, node */\nimport Typeson from 'typeson';\n\n/* istanbul ignore next */\nconst _global = typeof self === 'undefined' ? global : self;\n\n// Support all kinds of typed arrays (views of ArrayBuffers)\nconst typedArraysSocketIO = {};\n[\n    'Int8Array',\n    'Uint8Array',\n    'Uint8ClampedArray',\n    'Int16Array',\n    'Uint16Array',\n    'Int32Array',\n    'Uint32Array',\n    'Float32Array',\n    'Float64Array'\n].forEach(function (typeName) {\n    const arrType = typeName;\n    const TypedArray = _global[typeName];\n    /* istanbul ignore if */\n    if (!TypedArray) {\n        return;\n    }\n    typedArraysSocketIO[typeName.toLowerCase()] = {\n        test (x) { return Typeson.toStringTag(x) === arrType; },\n        replace (a) {\n            return (a.byteOffset === 0 &&\n                a.byteLength === a.buffer.byteLength\n                ? a\n                // socket.io supports streaming ArrayBuffers.\n                // If we have a typed array representing a portion\n                //   of the buffer, we need to clone\n                //   the buffer before leaving it to socket.io.\n                : a.slice(0)).buffer;\n        },\n        revive (buf) {\n            // One may configure socket.io to revive binary data as\n            //    Buffer or Blob.\n            // We should therefore not rely on that the instance we\n            //   get here is an ArrayBuffer\n            // If not, let's assume user wants to receive it as\n            //   configured with socket.io.\n            return Typeson.toStringTag(buf) === 'ArrayBuffer'\n                ? new TypedArray(buf)\n                : buf;\n        }\n    };\n});\n\nexport default typedArraysSocketIO;\n", "/* eslint-env browser, node */\nimport Typeson from 'typeson';\nimport {encode, decode} from 'base64-arraybuffer-es6';\n\n/* istanbul ignore next */\nconst _global = typeof self === 'undefined' ? global : self;\n\nconst typedArrays = {};\n[\n    'Int8Array',\n    'Uint8Array',\n    'Uint8ClampedArray',\n    'Int16Array',\n    'Uint16Array',\n    'Int32Array',\n    'Uint32Array',\n    'Float32Array',\n    'Float64Array'\n].forEach(function (typeName) {\n    const arrType = typeName;\n    const TypedArray = _global[arrType];\n    /* istanbul ignore if */\n    if (!TypedArray) {\n        return;\n    }\n    typedArrays[typeName.toLowerCase()] = {\n        test (x) { return Typeson.toStringTag(x) === arrType; },\n        replace ({buffer, byteOffset, length: l}, stateObj) {\n            if (!stateObj.buffers) {\n                stateObj.buffers = [];\n            }\n            const index = stateObj.buffers.indexOf(buffer);\n            if (index > -1) {\n                return {index, byteOffset, length: l};\n            }\n            stateObj.buffers.push(buffer);\n            return {\n                encoded: encode(buffer),\n                byteOffset,\n                length: l\n            };\n        },\n        revive (b64Obj, stateObj) {\n            if (!stateObj.buffers) {\n                stateObj.buffers = [];\n            }\n            const {byteOffset, length: len, encoded, index} = b64Obj;\n            let buffer;\n            if ('index' in b64Obj) {\n                buffer = stateObj.buffers[index];\n            } else {\n                buffer = decode(encoded);\n                stateObj.buffers.push(buffer);\n            }\n            return new TypedArray(buffer, byteOffset, len);\n        }\n    };\n});\n\nexport default typedArrays;\n", "// This does not preserve `undefined` in sparse arrays; see the `undefined`\n//  or `sparse-undefined` preset\nimport Typeson from 'typeson';\n\nconst undef = {\n    undef: {\n        test (x, stateObj) {\n            return typeof x === 'undefined' &&\n                (stateObj.ownKeys || !('ownKeys' in stateObj));\n        },\n        replace (n) { return 0; },\n        revive (s) {\n            // Will add `undefined` (returning `undefined` would instead\n            //   avoid explicitly setting)\n            return new Typeson.Undefined();\n        }\n    }\n};\n\nexport default undef;\n", "import Typeson from 'typeson';\n\nconst userObject = {\n    userObject: {\n        test (x, stateObj) { return Typeson.isUserObject(x); },\n        replace (n) { return {...n}; },\n        revive (s) { return s; }\n    }\n};\n\nexport default userObject;\n", "const arrayNonindexKeys = [\n    {\n        arrayNonindexKeys: {\n            testPlainObjects: true,\n            test (x, stateObj) {\n                if (Array.isArray(x)) {\n                    if (\n                        // By avoiding serializing arrays into objects which\n                        //  have only positive-integer keys, we reduce\n                        //  size and improve revival performance; arrays with\n                        //  non-index keys will be larger however\n                        Object.keys(x).some((k) => {\n                            //  No need to check for `isNaN` or\n                            //   `isNaN(Number.parseInt())` as `NaN` will be\n                            //   treated as a string.\n                            //  No need to do check as\n                            //   `Number.parseInt(Number())` since scientific\n                            //   notation will be pre-resolved if a number\n                            //   was given, and it will otherwise be a string\n                            return String(Number.parseInt(k)) !== k;\n                        })\n                    ) {\n                        stateObj.iterateIn = 'object';\n                        stateObj.addLength = true;\n                    }\n                    return true;\n                }\n                return false;\n            },\n            replace (a, stateObj) {\n                // Catch sparse undefined\n                stateObj.iterateUnsetNumeric = true;\n                return a;\n            },\n            revive (o) {\n                if (Array.isArray(o)) {\n                    return o;\n                }\n                const arr = [];\n                // No map here as may be a sparse array (including\n                //   with `length` set)\n                // Todo: Reenable when Node `engines` >= 7\n                // Object.entries(o).forEach(([key, val]) => {\n                Object.keys(o).forEach((key) => {\n                    const val = o[key];\n                    arr[key] = val;\n                });\n                return arr;\n            }\n        }\n    },\n    {\n        sparseUndefined: {\n            test (x, stateObj) {\n                return typeof x === 'undefined' && stateObj.ownKeys === false;\n            },\n            replace (n) { return 0; },\n            revive (s) { return undefined; } // Will avoid adding anything\n        }\n    }\n];\n\nexport default arrayNonindexKeys;\n", "import nan from '../types/nan.js';\nimport infinity from '../types/infinity.js';\nimport NegativeInfinity from '../types/negative-infinity.js';\n\nconst specialNumbers = [\n    nan,\n    infinity,\n    NegativeInfinity\n];\n\nexport default specialNumbers;\n", "/* This preset includes types that are built-in into the JavaScript\n    language itself, this should work universally.\n\n  Types that were added in ES6 or beyond will be checked before inclusion\n   so that this module can be consumed by both ES5 and ES6 environments.\n\n  Some types cannot be encapsulated because their inner state is private:\n    `WeakMap`, `WeakSet`.\n\n  The Function type is not included because their closures would not be\n    serialized, so a revived Function that uses closures would not behave\n    as expected.\n\n  Symbols are similarly not included.\n*/\n\nimport arrayNonindexKeys from './array-nonindex-keys.js';\nimport undef from '../types/undef.js';\nimport primitiveObjects from '../types/primitive-objects.js';\nimport specialNumbers from './special-numbers.js';\nimport date from '../types/date.js';\nimport error from '../types/error.js';\nimport errors from '../types/errors.js';\nimport regexp from '../types/regexp.js';\nimport map from '../types/map.js';\nimport set from '../types/set.js';\nimport arraybuffer from '../types/arraybuffer.js';\nimport typedArrays from '../types/typed-arrays.js';\nimport dataview from '../types/dataview.js';\nimport intlTypes from '../types/intl-types.js';\nimport bigint from '../types/bigint.js';\nimport bigintObject from '../types/bigint-object.js';\n\nconst expObj = [\n    undef,\n    // ES5\n    arrayNonindexKeys, primitiveObjects, specialNumbers,\n    date, error, errors, regexp\n].concat(\n    // ES2015 (ES6)\n    /* istanbul ignore next */\n    typeof Map === 'function' ? map : [],\n    /* istanbul ignore next */\n    typeof Set === 'function' ? set : [],\n    /* istanbul ignore next */\n    typeof ArrayBuffer === 'function' ? arraybuffer : [],\n    /* istanbul ignore next */\n    typeof Uint8Array === 'function' ? typedArrays : [],\n    /* istanbul ignore next */\n    typeof DataView === 'function' ? dataview : [],\n    /* istanbul ignore next */\n    typeof Intl !== 'undefined' ? intlTypes : [],\n\n    /* istanbul ignore next */\n    typeof BigInt !== 'undefined' ? [bigint, bigintObject] : []\n);\nexport default expObj;\n", "/*\nWhen communicating via `postMessage()` (`Worker.postMessage()` or\n`window.postMessage()`), the browser will use a similar algorithm as <PERSON><PERSON>\ndoes to encapsulate and revive all items in the structure (aka the structured\nclone algorithm). This algorithm supports all built-in types as well as many\nDOM types. Therefore, only types that are not included in the structured clone\nalgorithm need to be registered, which is:\n\n* Error\n* Specific Errors like SyntaxError, TypeError, etc.\n* Any custom type you want to send across window- or worker boundraries\n\nThis preset will only include the Error types and you can register your\ncustom types after having registered these.\n*/\n\nimport error from '../types/error.js';\nimport errors from '../types/errors.js';\n\nconst postmessage = [\n    error,\n    errors\n];\n\nexport default postmessage;\n", "import builtin from './builtin.js';\nimport typedArraysSocketIO from '../types/typed-arrays-socketio.js';\n\nconst socketio = [\n    builtin,\n    // Leave ArrayBuffer as is, and let socket.io stream it instead.\n    {arraybuffer: null},\n    // Encapsulate TypedArrays in ArrayBuffers instead of base64 strings.\n    typedArraysSocketIO\n];\n\nexport default socketio;\n", "const sparseUndefined = [\n    {\n        sparseArrays: {\n            testPlainObjects: true,\n            test (x) { return Array.isArray(x); },\n            replace (a, stateObj) {\n                stateObj.iterateUnsetNumeric = true;\n                return a;\n            }\n        }\n    },\n    {\n        sparseUndefined: {\n            test (x, stateObj) {\n                return typeof x === 'undefined' && stateObj.ownKeys === false;\n            },\n            replace (n) { return 0; },\n            revive (s) { return undefined; } // Will avoid adding anything\n        }\n    }\n];\n\nexport default sparseUndefined;\n", "/* This preset includes types for the Structured Cloning Algorithm. */\n\nimport userObject from '../types/user-object.js';\nimport arrayNonindexKeys from './array-nonindex-keys.js';\nimport undef from '../types/undef.js';\nimport primitiveObjects from '../types/primitive-objects.js';\nimport specialNumbers from './special-numbers.js';\nimport date from '../types/date.js';\nimport regexp from '../types/regexp.js';\nimport map from '../types/map.js';\nimport set from '../types/set.js';\nimport arraybuffer from '../types/arraybuffer.js';\nimport typedArrays from '../types/typed-arrays.js';\nimport dataview from '../types/dataview.js';\nimport intlTypes from '../types/intl-types.js';\n\nimport imagedata from '../types/imagedata.js';\nimport imagebitmap from '../types/imagebitmap.js'; // Async return\nimport file from '../types/file.js';\nimport filelist from '../types/filelist.js';\nimport blob from '../types/blob.js';\nimport bigint from '../types/bigint.js';\nimport bigintObject from '../types/bigint-object.js';\n\nimport cryptokey from '../types/cryptokey.js';\n\nconst expObj = [\n    // Todo: Might also register synchronous `ImageBitmap` and\n    //    `Blob`/`File`/`FileList`?\n    // ES5\n    userObject, // Processed last (non-builtin)\n\n    undef,\n    arrayNonindexKeys, primitiveObjects, specialNumbers,\n    date, regexp,\n\n    // Non-built-ins\n    imagedata,\n    imagebitmap, // Async return\n    file,\n    filelist,\n    blob\n].concat(\n    // ES2015 (ES6)\n    /* istanbul ignore next */\n    typeof Map === 'function' ? map : [],\n    /* istanbul ignore next */\n    typeof Set === 'function' ? set : [],\n    /* istanbul ignore next */\n    typeof ArrayBuffer === 'function' ? arraybuffer : [],\n    /* istanbul ignore next */\n    typeof Uint8Array === 'function' ? typedArrays : [],\n    /* istanbul ignore next */\n    typeof DataView === 'function' ? dataview : [],\n    /* istanbul ignore next */\n    typeof Intl !== 'undefined' ? intlTypes : [],\n    /* istanbul ignore next */\n    typeof crypto !== 'undefined' ? cryptokey : [],\n    /* istanbul ignore next */\n    typeof BigInt !== 'undefined' ? [bigint, bigintObject] : []\n);\nexport default expObj;\n", "/* globals DOMException */\nimport structuredCloning from './structured-cloning.js';\n\nexport default structuredCloning.concat({\n    checkDataCloneException: {\n        test (val) {\n            // Should also throw with:\n            // 1. `IsDetachedBuffer` (a process not called within the\n            //      ECMAScript spec)\n            // 2. `IsCallable` (covered by `typeof === 'function'` or a\n            //       function's `toStringTag`)\n            // 3. internal slots besides [[Prototype]] or [[Extensible]] (e.g.,\n            //        [[PromiseState]] or [[WeakMapData]])\n            // 4. exotic object (e.g., `Proxy`) (unless an `%ObjectPrototype%`\n            //      intrinsic object) (which does not have default\n            //      behavior for one or more of the essential internal methods\n            //      that are limited to the following for non-function objects\n            //      (we auto-exclude functions):\n            //      [[GetPrototypeOf]],[[SetPrototypeOf]],[[IsExtensible]],\n            //      [[PreventExtensions]],[[GetOwnProperty]],\n            //      [[DefineOwnProperty]],[[HasProperty]],\n            //      [[Get]],[[Set]],[[Delete]],[[OwnPropertyKeys]]);\n            //      except for the standard, built-in exotic objects, we'd need\n            //      to know whether these methods had distinct behaviors\n            // Note: There is no apparent way for us to detect a `Proxy` and\n            //      reject (Chrome at least is not rejecting anyways)\n            const stringTag = ({}.toString.call(val).slice(8, -1));\n            if (\n                [\n                    // Symbol's `toStringTag` is only \"Symbol\" for its initial\n                    //   value, so we check `typeof`\n                    'symbol',\n                    // All functions including bound function exotic objects\n                    'function'\n                ].includes(typeof val) ||\n                [\n                    // A non-array exotic object\n                    'Arguments',\n                    // A non-array exotic object\n                    'Module',\n                    // `Error` and other errors have the [[ErrorData]] internal\n                    //    slot and give \"Error\"\n                    'Error',\n                    // Promise instances have an extra slot ([[PromiseState]])\n                    //    but not throwing in Chrome `postMessage`\n                    'Promise',\n                    // WeakMap instances have an extra slot ([[WeakMapData]])\n                    //    but not throwing in Chrome `postMessage`\n                    'WeakMap',\n                    // WeakSet instances have an extra slot ([[WeakSetData]])\n                    //    but not throwing in Chrome `postMessage`\n                    'WeakSet',\n\n                    // HTML-SPECIFIC\n                    'Event',\n                    // Also in Node `worker_threads` (currently experimental)\n                    'MessageChannel'\n                ].includes(stringTag) ||\n                /*\n                // isClosed is no longer documented\n                ((stringTag === 'Blob' || stringTag === 'File') &&\n                    val.isClosed) ||\n                */\n                (val && typeof val === 'object' &&\n                    // Duck-type DOM node objects (non-array exotic?\n                    //    objects which cannot be cloned by the SCA)\n                    typeof val.nodeType === 'number' &&\n                    typeof val.insertBefore === 'function')\n            ) {\n                throw new DOMException(\n                    'The object cannot be cloned.', 'DataCloneError'\n                );\n            }\n            return false;\n        }\n    }\n});\n", "import sparseUndefined from './sparse-undefined.js';\nimport undf from '../types/undef.js';\n\nconst undef = [\n    sparseUndefined,\n    undf\n];\n\nexport default undef;\n", "import builtin from './builtin.js';\n\nconst universal = [\n    builtin\n    // TODO: Add types that are de-facto universal even though not\n    //   built-in into ecmasript standard.\n];\n\nexport default universal;\n", "// This file is auto-generated from `build.js`\nimport Typeson from 'typeson';\n\n// TYPES\nimport arraybuffer from './types/arraybuffer.js';\nimport bigintObject from './types/bigint-object.js';\nimport bigint from './types/bigint.js';\nimport blob from './types/blob.js';\nimport cloneable from './types/cloneable.js';\nimport cryptokey from './types/cryptokey.js';\nimport dataview from './types/dataview.js';\nimport date from './types/date.js';\nimport error from './types/error.js';\nimport errors from './types/errors.js';\nimport file from './types/file.js';\nimport filelist from './types/filelist.js';\nimport imagebitmap from './types/imagebitmap.js';\nimport imagedata from './types/imagedata.js';\nimport infinity from './types/infinity.js';\nimport intlTypes from './types/intl-types.js';\nimport map from './types/map.js';\nimport nan from './types/nan.js';\nimport negativeInfinity from './types/negative-infinity.js';\nimport nonbuiltinIgnore from './types/nonbuiltin-ignore.js';\nimport primitiveObjects from './types/primitive-objects.js';\nimport regexp from './types/regexp.js';\nimport resurrectable from './types/resurrectable.js';\nimport set from './types/set.js';\nimport typedArraysSocketio from './types/typed-arrays-socketio.js';\nimport typedArrays from './types/typed-arrays.js';\nimport undef from './types/undef.js';\nimport userObject from './types/user-object.js';\n\n// PRESETS\nimport arrayNonindexKeys from './presets/array-nonindex-keys.js';\nimport builtin from './presets/builtin.js';\nimport postmessage from './presets/postmessage.js';\nimport socketio from './presets/socketio.js';\nimport sparseUndefined from './presets/sparse-undefined.js';\nimport specialNumbers from './presets/special-numbers.js';\nimport structuredCloningThrowing from\n    './presets/structured-cloning-throwing.js';\nimport structuredCloning from './presets/structured-cloning.js';\nimport undefPreset from './presets/undef.js';\nimport universal from './presets/universal.js';\n\nTypeson.types = {\n    arraybuffer, bigintObject, bigint, blob, cloneable, cryptokey, dataview,\n    date, error, errors, file, filelist, imagebitmap, imagedata, infinity,\n    intlTypes, map, nan, negativeInfinity, nonbuiltinIgnore, primitiveObjects,\n    regexp, resurrectable, set, typedArraysSocketio, typedArrays, undef,\n    userObject\n};\nTypeson.presets = {\n    arrayNonindexKeys, builtin, postmessage, socketio, sparseUndefined,\n    specialNumbers, structuredCloningThrowing, structuredCloning,\n    undef: undefPreset, universal\n};\n\nexport default Typeson;\n"], "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_defineProperty", "value", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "push", "apply", "_objectSpread2", "arguments", "source", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "_slicedToArray", "arr", "_arrayWithHoles", "Array", "isArray", "_iterableToArrayLimit", "_arr", "_n", "_d", "_e", "undefined", "_s", "_i", "next", "done", "err", "_unsupportedIterableToArray", "_nonIterableRest", "_toConsumableArray", "_arrayWithoutHoles", "_arrayLikeToArray", "_iterableToArray", "iter", "from", "_nonIterableSpread", "o", "minLen", "n", "toString", "call", "slice", "name", "test", "len", "arr2", "TypesonPromise", "f", "this", "p", "Promise", "__typeson__type__", "toStringTag", "then", "onFulfilled", "onRejected", "_this", "typesonResolve", "typesonReject", "res", "reject", "resolve", "v", "meth", "promArr", "map", "prom", "toStr", "hasOwn$1", "hasOwnProperty", "getProto", "getPrototypeOf", "fnToString", "isThenable", "catch<PERSON><PERSON><PERSON>", "isObject", "val", "hasConstructorOf", "a", "b", "proto", "Ctor", "isPlainObject", "escapeKeyPathComponent", "keyPathComponent", "replace", "unescapeKeyPathComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keyP<PERSON>", "period", "indexOf", "innerObj", "set<PERSON>t<PERSON>ey<PERSON>ath", "_await", "direct", "hasOwn", "internalStateObjPropsToIgnore", "_async", "args", "e", "nested<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keypath", "as", "match", "bs", "<PERSON>son", "options", "plainObjectReplacers", "nonplainObjectReplacers", "revivers", "types", "_createClass", "protoProps", "staticProps", "stringify", "replacer", "space", "opts", "stringification", "encapsulated", "encapsulate", "JSON", "stringifySync", "throwOnBadSyncType", "sync", "stringifyAsync", "parse", "text", "reviver", "revive", "parseSync", "parseAsync", "specialTypeNames", "stateObj", "returnTypeNames", "rootTypeName", "iterateNone", "checkPromises", "ret", "promisesData", "all", "pd", "promResults", "promResult", "_exit", "newPromisesData", "_promisesData$splice2", "splice", "_prData", "cyclic", "parentObj", "detectedType", "encaps", "_encapsulate", "isTypesonPromise", "_invoke", "body", "result", "encaps2", "_result", "that", "refObjs", "refKeys", "promisesDataRoot", "encapsulateObserver", "finish", "typeNames", "values", "getJSONType", "Set", "$types", "$", "_adaptBuiltinStateObjectProperties", "ownKeysObj", "cb", "assign", "vals", "prop", "tmp", "resolvingTypesonPromise", "observerData", "$typeof", "runObserver", "type", "awaitingTypesonPromise", "includes", "Number", "isNaN", "NEGATIVE_INFINITY", "POSITIVE_INFINITY", "replaced", "iterateIn", "iterateUnsetNumeric", "refIndex", "cyclicKeypath", "clone", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isArr", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add<PERSON><PERSON><PERSON>", "_loop", "kp", "Boolean", "endIterateIn", "end", "endIterateOwn", "vl", "_loop2", "endIterateUnsetNumeric", "plainObject", "replacers", "existing", "concat", "replaceAsync", "replacing", "typeDetected", "encapsulateSync", "encapsulateAsync", "keyPathResolutions", "ignore$Types", "executeReviver", "Error", "reviveAsync", "revivalPromises", "checkUndefined", "retrn", "Undefined", "possibleTypesonPromise", "revivePlainObjects", "plainObjectTypes", "entries", "_ref3", "_ref4", "plain", "sort", "reduce", "reducer", "_ref7", "newVal", "_revive", "_clone", "k", "set", "_keyPathResolutions$", "_target", "_clone2", "_ret", "typ", "r", "_ref8", "reviveSync", "register", "typeSpecSets", "R", "typeSpec", "typSpec", "typeId", "JSON_TYPES", "spec", "testPlainObjects", "existingReplacer", "Class", "x", "create", "_spec2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bind", "start", "fallback", "reviver<PERSON><PERSON><PERSON>", "isUserObject", "chars", "lookup", "Uint8Array", "charCodeAt", "encode", "arraybuffer", "byteOffset", "lngth", "byteLength", "bytes", "base64", "decode", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "_i2", "buffers", "index", "b64", "buffer", "bigintObject", "BigInt", "String", "s", "bigint", "string2arraybuffer", "str", "array", "blob", "req", "XMLHttpRequest", "overrideMimeType", "open", "URL", "createObjectURL", "send", "status", "stringContents", "responseText", "Blob", "reader", "FileReader", "addEventListener", "error", "readAsBinaryString", "generateUUID", "d", "Date", "now", "performance", "c", "Math", "trunc", "random", "floor", "cloneableObjectsByUUID", "cloneable", "clonable", "uuid", "cryptokey", "extractable", "crypto", "subtle", "exportKey", "jwk", "algorithm", "usages", "importKey", "dataview", "encoded", "b64Obj", "DataView", "date", "dt", "time", "getTime", "NaN", "message", "_global", "self", "global", "errors", "err<PERSON><PERSON>", "Cnstrctr", "toLowerCase", "file", "lastModified", "File", "filelist", "fl", "item", "_files", "imagebitmap", "dataset", "bm", "canvas", "document", "createElement", "getContext", "drawImage", "toDataURL", "ctx", "img", "src", "createImageBitmap", "imagedata", "data", "width", "height", "ImageData", "Uint8ClampedArray", "infinity", "intlTypes", "IntlCollator", "Intl", "Collator", "resolvedOptions", "locale", "IntlDateTimeFormat", "DateTimeFormat", "dtf", "IntlNumberFormat", "NumberFormat", "nf", "mp", "Map", "nan", "negativeInfinity", "nonbuiltinIgnore", "rexp", "primitiveObjects", "StringObject", "BooleanObject", "NumberObject", "regexp", "flags", "ignoreCase", "multiline", "sticky", "unicode", "RegExp", "resurrectableObjectsByUUID", "resurrectable", "rsrrctble", "serializedResurrectable", "st", "typedArraysSocketIO", "typeName", "arrType", "TypedArray", "buf", "typedArrays", "l", "undef", "userObject", "arrayNonindexKeys", "some", "parseInt", "sparseUndefined", "specialNumbers", "NegativeInfinity", "expObj", "postmessage", "socketio", "builtin", "sparseA<PERSON>ys", "structuredCloning", "checkDataCloneException", "stringTag", "nodeType", "insertBefore", "DOMException", "undf", "universal", "typedArraysSocketio", "presets", "structuredCloningThrowing", "undefPreset"], "mappings": "wOAAA,SAASA,UAAQC,UAIbD,UADoB,mBAAXE,QAAoD,iBAApBA,OAAOC,SACtC,iBAAUF,iBACJA,GAGN,iBAAUA,UACXA,GAAyB,mBAAXC,QAAyBD,EAAIG,cAAgBF,QAAUD,IAAQC,OAAOG,UAAY,gBAAkBJ,IAI9GA,GAGjB,SAASK,kBAAgBC,EAAUC,QAC3BD,aAAoBC,SAClB,IAAIC,UAAU,qCAIxB,SAASC,oBAAkBC,EAAQC,OAC5B,IAAIC,EAAI,EAAGA,EAAID,EAAME,OAAQD,IAAK,KACjCE,EAAaH,EAAMC,GACvBE,EAAWC,WAAaD,EAAWC,aAAc,EACjDD,EAAWE,cAAe,EACtB,UAAWF,IAAYA,EAAWG,UAAW,GACjDC,OAAOC,eAAeT,EAAQI,EAAWM,IAAKN,IAUlD,SAASO,kBAAgBrB,EAAKoB,EAAKE,UAC7BF,KAAOpB,EACTkB,OAAOC,eAAenB,EAAKoB,EAAK,CAC9BE,MAAOA,EACPP,YAAY,EACZC,cAAc,EACdC,UAAU,IAGZjB,EAAIoB,GAAOE,EAGNtB,EAGT,SAASuB,UAAQC,EAAQC,OACnBC,EAAOR,OAAOQ,KAAKF,MAEnBN,OAAOS,sBAAuB,KAC5BC,EAAUV,OAAOS,sBAAsBH,GACvCC,IAAgBG,EAAUA,EAAQC,QAAO,SAAUC,UAC9CZ,OAAOa,yBAAyBP,EAAQM,GAAKf,eAEtDW,EAAKM,KAAKC,MAAMP,EAAME,UAGjBF,EAGT,SAASQ,iBAAexB,OACjB,IAAIE,EAAI,EAAGA,EAAIuB,UAAUtB,OAAQD,IAAK,KACrCwB,EAAyB,MAAhBD,UAAUvB,GAAauB,UAAUvB,GAAK,GAE/CA,EAAI,EACNW,UAAQL,OAAOkB,IAAS,GAAMC,SAAQ,SAAUjB,GAC9CC,kBAAgBX,EAAQU,EAAKgB,EAAOhB,OAE7BF,OAAOoB,0BAChBpB,OAAOqB,iBAAiB7B,EAAQQ,OAAOoB,0BAA0BF,IAEjEb,UAAQL,OAAOkB,IAASC,SAAQ,SAAUjB,GACxCF,OAAOC,eAAeT,EAAQU,EAAKF,OAAOa,yBAAyBK,EAAQhB,cAK1EV,EAGT,SAAS8B,eAAeC,EAAK7B,UAY7B,SAAS8B,gBAAgBD,MACnBE,MAAMC,QAAQH,GAAM,OAAOA,EAZxBC,CAAgBD,IAmBzB,SAASI,sBAAsBJ,EAAK7B,MACZ,oBAAXX,UAA4BA,OAAOC,YAAYgB,OAAOuB,IAAO,WACpEK,EAAO,GACPC,GAAK,EACLC,GAAK,EACLC,OAAKC,UAGF,IAAiCC,EAA7BC,EAAKX,EAAIxC,OAAOC,cAAmB6C,GAAMI,EAAKC,EAAGC,QAAQC,QAChER,EAAKd,KAAKmB,EAAG7B,QAETV,GAAKkC,EAAKjC,SAAWD,GAH8CmC,GAAK,IAK9E,MAAOQ,GACPP,GAAK,EACLC,EAAKM,cAGER,GAAsB,MAAhBK,EAAE,QAAoBA,EAAE,oBAE/BJ,EAAI,MAAMC,UAIXH,EA3CwBD,CAAsBJ,EAAK7B,IAAM4C,8BAA4Bf,EAAK7B,IAmEnG,SAAS6C,yBACD,IAAIjD,UAAU,6IApEmFiD,GAGzG,SAASC,qBAAmBjB,UAI5B,SAASkB,qBAAmBlB,MACtBE,MAAMC,QAAQH,GAAM,OAAOmB,oBAAkBnB,GAJ1CkB,CAAmBlB,IAW5B,SAASoB,mBAAiBC,MACF,oBAAX7D,QAA0BA,OAAOC,YAAYgB,OAAO4C,GAAO,OAAOnB,MAAMoB,KAAKD,GAZtDD,CAAiBpB,IAAQe,8BAA4Bf,IA2DzF,SAASuB,6BACD,IAAIxD,UAAU,wIA5D2EwD,GA0CjG,SAASR,8BAA4BS,EAAGC,MACjCD,MACY,iBAANA,EAAgB,OAAOL,oBAAkBK,EAAGC,OACnDC,EAAIjD,OAAOd,UAAUgE,SAASC,KAAKJ,GAAGK,MAAM,GAAI,SAC1C,WAANH,GAAkBF,EAAE9D,cAAagE,EAAIF,EAAE9D,YAAYoE,MAC7C,QAANJ,GAAqB,QAANA,EAAoBxB,MAAMoB,KAAKE,GACxC,cAANE,GAAqB,2CAA2CK,KAAKL,GAAWP,oBAAkBK,EAAGC,WAG3G,SAASN,oBAAkBnB,EAAKgC,IACnB,MAAPA,GAAeA,EAAMhC,EAAI5B,UAAQ4D,EAAMhC,EAAI5B,YAE1C,IAAID,EAAI,EAAG8D,EAAO,IAAI/B,MAAM8B,GAAM7D,EAAI6D,EAAK7D,IAAK8D,EAAK9D,GAAK6B,EAAI7B,UAE5D8D,EAoDT,IAAIC,EAAiB,SAASA,eAAeC,GAC3CvE,kBAAgBwE,KAAMF,qBAEjBG,EAAI,IAAIC,QAAQH,IASvBD,EAAeK,kBAAoB,iBAIb,oBAAX/E,SAET0E,EAAevE,UAAUH,OAAOgF,aAAe,kBAUjDN,EAAevE,UAAU8E,KAAO,SAAUC,EAAaC,OACjDC,EAAQR,YAEL,IAAIF,GAAe,SAAUW,EAAgBC,GAElDF,EAAMP,EAAEI,MAAK,SAAUM,GAErBF,EAAeH,EAAcA,EAAYK,GAAOA,MAFlD,OAGY,SAAUA,UACbJ,EAAaA,EAAWI,GAAOT,QAAQU,OAAOD,MACpDN,KAAKI,EAAgBC,OAU5BZ,EAAevE,UAAf,MAAoC,SAAUgF,UACrCP,KAAKK,KAAK,KAAME,IASzBT,EAAee,QAAU,SAAUC,UAC1B,IAAIhB,GAAe,SAAUW,GAClCA,EAAeK,OAUnBhB,EAAec,OAAS,SAAUE,UACzB,IAAIhB,GAAe,SAAUW,EAAgBC,GAClDA,EAAcI,OAIlB,CAAC,MAAO,QAAQtD,SAAQ,SAAUuD,GAMhCjB,EAAeiB,GAAQ,SAAUC,UACxB,IAAIlB,GAAe,SAAUW,EAAgBC,GAElDR,QAAQa,GAAMC,EAAQC,KAAI,SAAUC,UAC3BA,GAAQA,EAAK5F,aAAsD,mBAAvC4F,EAAK5F,YAAY6E,kBAAyCe,EAAKjB,EAAIiB,MACpGb,KAAKI,EAAgBC,UAK/B,IACIS,EADO,GACM5B,SACb6B,EAAW,GAAGC,eACdC,EAAWjF,OAAOkF,eAClBC,EAAaJ,EAAS7B,SAQ1B,SAASkC,WAAWX,EAAGY,UACdC,SAASb,IAAwB,mBAAXA,EAAET,QAAyBqB,GAAoC,mBAAfZ,EAAC,OAShF,SAASV,YAAYwB,UACZT,EAAM3B,KAAKoC,GAAKnC,MAAM,GAAI,GAWnC,SAASoC,iBAAiBC,EAAGC,OACtBD,GAAoB,WAAf5G,UAAQ4G,UACT,MAGLE,EAAQV,EAASQ,OAEhBE,SACU,OAAND,MAGLE,EAAOb,EAAS5B,KAAKwC,EAAO,gBAAkBA,EAAM1G,kBAEpC,mBAAT2G,EACI,OAANF,EAGLA,IAAME,IAIA,OAANF,GAAcP,EAAWhC,KAAKyC,KAAUT,EAAWhC,KAAKuC,IAI3C,mBAANA,GAAsD,iBAA3BE,EAAK9B,mBAAkC8B,EAAK9B,oBAAsB4B,EAAE5B,mBAa5G,SAAS+B,cAAcN,YAEhBA,GAA4B,WAArBxB,YAAYwB,OAIZN,EAASM,IAOdC,iBAAiBD,EAAKvF,SA8B/B,SAASsF,SAASb,UACTA,GAAoB,WAAf5F,UAAQ4F,GAStB,SAASqB,uBAAuBC,UACvBA,EAAiBC,QAAQ,KAAM,MAAMA,QAAQ,MAAO,MAS7D,SAASC,yBAAyBF,UACzBA,EAAiBC,QAAQ,MAAO,KAAKA,QAAQ,MAAO,KAS7D,SAASE,aAAapH,EAAKqH,MACT,KAAZA,SACKrH,MAGLsH,EAASD,EAAQE,QAAQ,QAEzBD,GAAU,EAAG,KACXE,EAAWxH,EAAImH,yBAAyBE,EAAQ/C,MAAM,EAAGgD,iBACzCpE,IAAbsE,OAAyBtE,EAAYkE,aAAaI,EAAUH,EAAQ/C,MAAMgD,EAAS,WAGrFtH,EAAImH,yBAAyBE,IAWtC,SAASI,aAAazH,EAAKqH,EAAS/F,MAClB,KAAZ+F,SACK/F,MAGLgG,EAASD,EAAQE,QAAQ,YAEzBD,GAAU,EAELG,aADQzH,EAAImH,yBAAyBE,EAAQ/C,MAAM,EAAGgD,KAC/BD,EAAQ/C,MAAMgD,EAAS,GAAIhG,IAG3DtB,EAAImH,yBAAyBE,IAAY/F,EAClCtB,GAcT,SAAS0H,OAAOpG,EAAO4D,EAAMyC,UACvBA,EACKzC,EAAOA,EAAK5D,GAASA,GAGzBA,GAAUA,EAAM4D,OACnB5D,EAAQyD,QAAQW,QAAQpE,IAGnB4D,EAAO5D,EAAM4D,KAAKA,GAAQ5D,GAGnC,IAAII,EAAOR,OAAOQ,KACdkB,EAAUD,MAAMC,QAChBgF,EAAS,GAAG1B,eACZ2B,EAAgC,CAAC,OAAQ,WAAY,YAAa,uBAUtE,SAASC,OAAOlD,UACP,eACA,IAAImD,EAAO,GAAInH,EAAI,EAAGA,EAAIuB,UAAUtB,OAAQD,IAC/CmH,EAAKnH,GAAKuB,UAAUvB,cAIbmE,QAAQW,QAAQd,EAAE3C,MAAM4C,KAAMkD,IACrC,MAAOC,UACAjD,QAAQU,OAAOuC,KAmD5B,SAASC,iBAAiBtB,EAAGC,MACT,KAAdD,EAAEuB,eACI,MAGNC,EAAKxB,EAAEuB,QAAQE,MAAM,QAAU,EAC/BC,EAAKzB,EAAEsB,QAAQE,MAAM,QAAU,SAE/BD,IACFA,EAAKA,EAAGtH,QAGNwH,IACFA,EAAKA,EAAGxH,QAGHsH,EAAKE,GAAM,EAAIF,EAAKE,EAAK,EAAI1B,EAAEuB,QAAUtB,EAAEsB,SAAW,EAAIvB,EAAEuB,QAAUtB,EAAEsB,YAG7EI,EAAuB,oBAIhBA,QAAQC,GACflI,kBAAgBwE,KAAMyD,cAEjBC,QAAUA,OAGVC,qBAAuB,QACvBC,wBAA0B,QAG1BC,SAAW,QAGXC,MAAQ,UAhkBjB,SAASC,eAAarI,EAAasI,EAAYC,UACzCD,GAAYpI,oBAAkBF,EAAYH,UAAWyI,GACrDC,GAAarI,oBAAkBF,EAAauI,GACzCvI,EAulBPqI,CAAaN,QAAS,CAAC,CACrBlH,IAAK,YACLE,MAAO,SAASyH,UAAU/I,EAAKgJ,EAAUC,EAAOC,GAC9CA,EAAOhH,iBAAeA,iBAAeA,iBAAe,GAAI2C,KAAK0D,SAAUW,GAAO,GAAI,CAChFC,iBAAiB,QAEfC,EAAevE,KAAKwE,YAAYrJ,EAAK,KAAMkJ,UAE3CtG,EAAQwG,GACHE,KAAKP,UAAUK,EAAa,GAAIJ,EAAUC,GAG5CG,EAAalE,MAAK,SAAUM,UAC1B8D,KAAKP,UAAUvD,EAAKwD,EAAUC,QAYxC,CACD7H,IAAK,gBACLE,MAAO,SAASiI,cAAcvJ,EAAKgJ,EAAUC,EAAOC,UAC3CrE,KAAKkE,UAAU/I,EAAKgJ,EAAUC,EAAO/G,iBAAeA,iBAAe,CACxEsH,oBAAoB,GACnBN,GAAO,GAAI,CACZO,MAAM,OAYT,CACDrI,IAAK,iBACLE,MAAO,SAASoI,eAAe1J,EAAKgJ,EAAUC,EAAOC,UAC5CrE,KAAKkE,UAAU/I,EAAKgJ,EAAUC,EAAO/G,iBAAeA,iBAAe,CACxEsH,oBAAoB,GACnBN,GAAO,GAAI,CACZO,MAAM,OAoBT,CACDrI,IAAK,QACLE,MAAO,SAASqI,MAAMC,EAAMC,EAASX,UACnCA,EAAOhH,iBAAeA,iBAAeA,iBAAe,GAAI2C,KAAK0D,SAAUW,GAAO,GAAI,CAChFS,OAAO,IAEF9E,KAAKiF,OAAOR,KAAKK,MAAMC,EAAMC,GAAUX,KAW/C,CACD9H,IAAK,YACLE,MAAO,SAASyI,UAAUH,EAAMC,EAASX,UAChCrE,KAAK8E,MAAMC,EAAMC,EAAS3H,iBAAeA,iBAAe,CAC7DsH,oBAAoB,GACnBN,GAAO,GAAI,CACZO,MAAM,OAWT,CACDrI,IAAK,aACLE,MAAO,SAAS0I,WAAWJ,EAAMC,EAASX,UACjCrE,KAAK8E,MAAMC,EAAMC,EAAS3H,iBAAeA,iBAAe,CAC7DsH,oBAAoB,GACnBN,GAAO,GAAI,CACZO,MAAM,OAeT,CACDrI,IAAK,mBACLE,MAAO,SAAS2I,iBAAiBjK,EAAKkK,OAChChB,EAAO/G,UAAUtB,OAAS,QAAsBqC,IAAjBf,UAAU,GAAmBA,UAAU,GAAK,UAC/E+G,EAAKiB,iBAAkB,EAChBtF,KAAKwE,YAAYrJ,EAAKkK,EAAUhB,KAUxC,CACD9H,IAAK,eACLE,MAAO,SAAS8I,aAAapK,EAAKkK,OAC5BhB,EAAO/G,UAAUtB,OAAS,QAAsBqC,IAAjBf,UAAU,GAAmBA,UAAU,GAAK,UAC/E+G,EAAKmB,aAAc,EACZxF,KAAKwE,YAAYrJ,EAAKkK,EAAUhB,KAaxC,CACD9H,IAAK,cACLE,MAAO,SAAS+H,YAAYrJ,EAAKkK,EAAUhB,OAOrCoB,EAAgBxC,QAAO,SAAUyC,EAAKC,UACjC9C,OAAO3C,QAAQ0F,IAAID,EAAa1E,KAAI,SAAU4E,UAC5CA,EAAG,GAAG5F,OACV,SAAU6F,UACNjD,OAAO3C,QAAQ0F,IAAIE,EAAY7E,IAAIgC,QAAO,SAAU8C,OACrDC,GAAQ,EACRC,EAAkB,GAGlBC,EAAwBvI,eADDgI,EAAaQ,OAAO,EAAG,GACe,GAG7DC,EAAUzI,eAFDuI,EAAsB,GAEE,GACjC1D,EAAU4D,EAAQ,GAClBC,EAASD,EAAQ,GACjBf,EAAWe,EAAQ,GACnBE,EAAYF,EAAQ,GACpB7J,EAAM6J,EAAQ,GACdG,EAAeH,EAAQ,GAEvBI,EAASC,aAAajE,EAASuD,EAAYM,EAAQhB,EAAUY,GAAiB,EAAMM,GAEpFG,EAAmB7E,iBAAiB2E,EAAQ1G,UApQ5D,SAAS6G,QAAQC,EAAMvG,OACjBwG,EAASD,WAETC,GAAUA,EAAOxG,KACZwG,EAAOxG,KAAKA,GAGdA,EAAKwG,GAgQKF,EAAQ,cACTnE,GAAWkE,SACN7D,OAAO2D,EAAOvG,GAAG,SAAU6G,UAChCR,EAAU/J,GAAOuK,EACjBd,GAAQ,EACDP,EAAcC,EAAKO,SAG7B,SAAUc,UACPf,EAAce,GAEdvE,EACF8D,EAAU/J,GAAOiK,EAEjBd,EADSgB,EACHF,EAAOvG,EASPuG,EAGDf,EAAcC,EAAKO,aAExB,kBACGP,WA2BTd,GAJJP,EAAOhH,iBAAeA,iBAAe,CACnCuH,MAAM,GACL5E,KAAK0D,SAAUW,IAEDO,KACboC,EAAOhH,KACP8D,EAAQ,GACRmD,EAAU,GAEdC,EAAU,GAEVC,EAAmB,GAGfd,IAAS,WAAYhC,IAAOA,EAAKgC,OAEjCe,EADS/C,EACoB+C,oBAE7B1B,EAAMe,aAAa,GAAItL,EAAKkL,EAAQhB,GAAY,GAAI8B,YAQ/CE,OAAO3B,OAGV4B,EAAYjL,OAAOkL,OAAOzD,MAE1BO,EAAKmB,mBACH8B,EAAUtL,OACLsL,EAAU,GAGZ7D,QAAQ+D,YAAY9B,MAGzB4B,EAAUtL,OAAQ,IAChBqI,EAAKiB,uBACAzG,qBAAmB,IAAI4I,IAAIH,IAK/B5B,GAAQxD,cAAcwD,KAE3B3C,EAAOvD,KAAKkG,EAAK,UAQfA,EAAIgC,OAAS5D,EAPb4B,EAAM,CACJiC,EAAGjC,EACHgC,OAAQ,CACNC,EAAG7D,SAOAnC,SAAS+D,IAAQ3C,EAAOvD,KAAKkG,EAAK,YAC3CA,EAAM,CACJiC,EAAGjC,EACHgC,QAAQ,WAIRrD,EAAKiB,iBAIFI,WAGAkC,mCAAmCvC,EAAUwC,EAAYC,GAChEzL,OAAO0L,OAAO1C,EAAUwC,OACpBG,EAAOhF,EAA8B/B,KAAI,SAAUgH,OACjDC,EAAM7C,EAAS4C,iBACZ5C,EAAS4C,GACTC,KAGTJ,IACA9E,EAA8BxF,SAAQ,SAAUyK,EAAMlM,GACpDsJ,EAAS4C,GAAQD,EAAKjM,eAgBjB0K,aAAapD,EAAS5G,EAAO4J,EAAQhB,EAAUM,EAAcwC,EAAyB5B,OACzFb,EACA0C,EAAe,GAEfC,EAAUnN,UAAQuB,GAElB6L,EAAclB,EAAsB,SAAUjM,OAC5CoN,EAAOhC,GAAgBlB,EAASkD,MAAQ9E,QAAQ+D,YAAY/K,GAChE2K,EAAoB/K,OAAO0L,OAAO5M,GAAOiN,EAAc,CACrD/E,QAASA,EACT5G,MAAOA,EACP4J,OAAQA,EACRhB,SAAUA,EACVM,aAAcA,EACdwC,wBAAyBA,EACzBK,uBAAwB3G,iBAAiBpF,EAAOqD,IAC/C,CACDyI,KAAMA,MAEN,QAEA,CAAC,SAAU,UAAW,SAAU,aAAaE,SAASJ,eAC1ChK,IAAV5B,GAAuBiM,OAAOC,MAAMlM,IAAUA,IAAUiM,OAAOE,mBAAqBnM,IAAUiM,OAAOG,mBACvGnD,EAAML,EAASyD,SAAWrM,EAAQ4F,QAAQgB,EAAS5G,EAAO4I,EAAUM,GAAc,EAAOwC,EAAyBG,MAEtG7L,IACV2L,EAAe,CACbU,SAAUpD,IAIdA,EAAMjJ,EAGJ6L,GACFA,IAGK5C,KAGK,OAAVjJ,SACE6L,GACFA,IAGK7L,KAGL4J,IAAWhB,EAAS0D,YAAc1D,EAAS2D,qBAAuBvM,GAA4B,WAAnBvB,UAAQuB,GAAqB,KAGtGwM,EAAWhC,EAAQvE,QAAQjG,QAE3BwM,EAAW,UAMbnF,EAAMT,GAAW,IAEbiF,GACFA,EAAY,CACVY,cAAehC,EAAQ+B,KAIpB,IAAM/B,EAAQ+B,IAbN,IAAX5C,IACFY,EAAQ9J,KAAKV,GACbyK,EAAQ/J,KAAKkG,QAsBf8F,EAPAC,EAAalH,cAAczF,GAC3B4M,EAAQtL,EAAQtB,GAChBqM,GAEHM,GAAcC,MAAYrC,EAAKrD,qBAAqB3H,QAAUqJ,EAASyD,WAAazD,EAAS0D,UAE9FtM,EAAQ4F,QAAQgB,EAAS5G,EAAO4I,EAAUM,EAAcyD,GAAcC,EAAO,KAAMf,MAG/EQ,IAAarM,GACfiJ,EAAMoD,EACNV,EAAe,CACbU,SAAUA,IAII,KAAZzF,GAAkBxB,iBAAiBpF,EAAOqD,IAC5C6F,EAAaxI,KAAK,CAACkG,EAAS5G,EAAO4J,EAAQhB,OAAUhH,OAAWA,EAAWgH,EAASkD,OACpF7C,EAAMjJ,GACG4M,GAAgC,WAAvBhE,EAAS0D,WAAiD,UAAvB1D,EAAS0D,WAE9DI,EAAQ,IAAIrL,MAAMrB,EAAMT,QACxBoM,EAAe,CACbe,MAAOA,KAEC,CAAC,WAAY,UAAUV,SAASvN,UAAQuB,KAAa,WAAYA,GAAWoF,iBAAiBpF,EAAOqD,IAAoB+B,iBAAiBpF,EAAOyD,UAAa2B,iBAAiBpF,EAAO6M,gBAAgBF,GAAqC,WAAvB/D,EAAS0D,UAWtOrD,EAAMjJ,GAVN0M,EAAQ,GAEJ9D,EAASkE,YACXJ,EAAMnN,OAASS,EAAMT,QAGvBoM,EAAe,CACbe,MAAOA,IAOTb,GACFA,IAGEjE,EAAKmB,mBACA2D,GAASzD,MAGbyD,SACIzD,KAILL,EAAS0D,UAAW,KAClBS,EAAQ,SAASA,MAAMjN,OACrBsL,EAAa,CACfnL,QAASqG,EAAOvD,KAAK/C,EAAOF,IAG9BqL,mCAAmCvC,EAAUwC,GAAY,eACnD4B,EAAKpG,GAAWA,EAAU,IAAM,IAAMlB,uBAAuB5F,GAE7DqF,EAAM6E,aAAagD,EAAIhN,EAAMF,GAAMmN,QAAQrD,GAAShB,EAAUM,EAAcwC,GAE5EtG,iBAAiBD,EAAK9B,GACxB6F,EAAaxI,KAAK,CAACsM,EAAI7H,EAAK8H,QAAQrD,GAAShB,EAAU8D,EAAO5M,EAAK8I,EAASkD,YAC3DlK,IAARuD,IACTuH,EAAM5M,GAAOqF,WAMd,IAAIrF,KAAOE,EACd+M,EAAMjN,GAGJ+L,GACFA,EAAY,CACVqB,cAAc,EACdC,KAAK,SAQT/M,EAAKJ,GAAOe,SAAQ,SAAUjB,OACxBkN,EAAKpG,GAAWA,EAAU,IAAM,IAAMlB,uBAAuB5F,GAKjEqL,mCAAmCvC,EAJlB,CACf3I,SAAS,IAG8C,eACnDkF,EAAM6E,aAAagD,EAAIhN,EAAMF,GAAMmN,QAAQrD,GAAShB,EAAUM,EAAcwC,GAE5EtG,iBAAiBD,EAAK9B,GACxB6F,EAAaxI,KAAK,CAACsM,EAAI7H,EAAK8H,QAAQrD,GAAShB,EAAU8D,EAAO5M,EAAK8I,EAASkD,YAC3DlK,IAARuD,IACTuH,EAAM5M,GAAOqF,SAKf0G,GACFA,EAAY,CACVuB,eAAe,EACfD,KAAK,OAQPvE,EAAS2D,oBAAqB,SAC5Bc,EAAKrN,EAAMT,OAEX+N,EAAS,SAASA,OAAOhO,QACrBA,KAAKU,GAAQ,KAEbgN,EAAKpG,GAAWA,EAAU,IAAM,IAAMtH,EAK1C6L,mCAAmCvC,EAJlB,CACf3I,SAAS,IAG8C,eACnDkF,EAAM6E,aAAagD,OAAIpL,EAAWqL,QAAQrD,GAAShB,EAAUM,EAAcwC,GAE3EtG,iBAAiBD,EAAK9B,GACxB6F,EAAaxI,KAAK,CAACsM,EAAI7H,EAAK8H,QAAQrD,GAAShB,EAAU8D,EAAOpN,EAAGsJ,EAASkD,YACzDlK,IAARuD,IACTuH,EAAMpN,GAAK6F,QAMV7F,EAAI,EAAGA,EAAI+N,EAAI/N,IACtBgO,EAAOhO,GAGLuM,GACFA,EAAY,CACV0B,wBAAwB,EACxBJ,KAAK,WAKJT,WAiDA9G,QAAQgB,EAAS5G,EAAO4I,EAAUM,EAAcsE,EAAa9B,EAAyBG,WAEzF4B,EAAYD,EAAcjD,EAAKrD,qBAAuBqD,EAAKpD,wBAC3D7H,EAAImO,EAAUlO,OAEXD,KAAK,KACNoI,EAAW+F,EAAUnO,MAErBoI,EAASxE,KAAKlD,EAAO4I,GAAW,KAC9BkD,EAAOpE,EAASoE,QAEhBvB,EAAKnD,SAAS0E,GAAO,KAOnB4B,EAAWrG,EAAMT,GAGrBS,EAAMT,GAAW8G,EAAW,CAAC5B,GAAM6B,OAAOD,GAAY5B,SAGxDlM,OAAO0L,OAAO1C,EAAU,CACtBkD,KAAMA,EACNO,UAAU,KAGPlE,GAAST,EAASkG,cAAkBlG,EAAS9B,SAU9CiG,GACFA,EAAY,CACVgC,WAAW,IAOR7D,aAAapD,EAASc,EADTS,IAAST,EAASkG,aAAe,UAAY,gBACZ5N,EAAO4I,GAAWgB,GAAU,WAAYhB,EAAUM,EAAcwC,EAAyBI,KAlBxID,GACFA,EAAY,CACViC,cAAc,IAIX9D,aAAapD,EAAS5G,EAAO4J,GAAU,WAAYhB,EAAUM,EAAcwC,EAAyBI,YAgB1G9L,SAGF0K,EAAiBnL,OAAS4I,GAAQP,EAAKM,mBAAqB,iBAC3D,IAAIhJ,UAAU,mDAD6C,GAE7DuE,QAAQW,QAAQ4E,EAAcC,EAAKyB,IAAmB9G,KAAKgH,SAAWzC,GAAQP,EAAKM,mBAAqB,iBACtG,IAAIhJ,UAAU,mDADwF,GAM5G0I,EAAKC,iBAAmBM,EAAO,CAACyC,OAAO3B,IAAQd,EAAOyC,OAAO3B,GAAOxF,QAAQW,QAAQwG,OAAO3B,MAU9F,CACDnJ,IAAK,kBACLE,MAAO,SAAS+N,gBAAgBrP,EAAKkK,EAAUhB,UACtCrE,KAAKwE,YAAYrJ,EAAKkK,EAAUhI,iBAAeA,iBAAe,CACnEsH,oBAAoB,GACnBN,GAAO,GAAI,CACZO,MAAM,OAUT,CACDrI,IAAK,mBACLE,MAAO,SAASgO,iBAAiBtP,EAAKkK,EAAUhB,UACvCrE,KAAKwE,YAAYrJ,EAAKkK,EAAUhI,iBAAeA,iBAAe,CACnEsH,oBAAoB,GACnBN,GAAO,GAAI,CACZO,MAAM,OAeT,CACDrI,IAAK,SACLE,MAAO,SAASwI,OAAO9J,EAAKkJ,OACtBP,EAAQ3I,GAAOA,EAAIuM,WAElB5D,SACI3I,MAKK,IAAV2I,SACK3I,EAAIwM,MAOT/C,GAJJP,EAAOhH,iBAAeA,iBAAe,CACnCuH,MAAM,GACL5E,KAAK0D,SAAUW,IAEAO,KACd8F,EAAqB,GACrBrF,EAAW,GACXsF,GAAe,EAIf7G,EAAM6D,GAAKzF,cAAc4B,EAAM6D,KACjCxM,EAAMA,EAAIwM,EACV7D,EAAQA,EAAM6D,EACdgD,GAAe,OAGb3D,EAAOhH,cAgBF4K,eAAerC,EAAM3G,OAGxBoD,EADQrH,eADDqJ,EAAKnD,SAAS0E,IAAS,GACD,GACb,OAEfvD,QACG,IAAI6F,MAAM,sBAAwBtC,UAOtC3D,KAAU,WAAYI,GAEjBpD,EAGFoD,EAAQJ,GAAQI,EAAQC,OAAS,UAAYL,GAAQI,EAAQ8F,YAAc,cAAgB,UAAUlJ,EAAKyD,OAiG/G0F,EAAkB,YAqGbC,eAAeC,UACfpJ,iBAAiBoJ,EAAOC,QAAa7M,EAAY4M,MAItDvF,EADAyF,WAlMKC,yBAGHC,EAAmB,MACvBhP,OAAOiP,QAAQxH,GAAOtG,SAAQ,SAAU+N,OAClCC,EAAQ7N,eAAe4N,EAAO,GAC9BlI,EAAUmI,EAAM,GAChBjD,EAAOiD,EAAM,GAEJ,MAATjD,MAUD6B,OAAO7B,GAAM/K,SAAQ,SAAU+K,GAEpB5K,eADAqJ,EAAKnD,SAAS0E,IAAS,CAAC,KAAM,IACR,GAChB,GAAGkD,QAOrBJ,EAAiBlO,KAAK,CACpBkG,QAASA,EACTkF,KAAMA,WAEDzE,EAAMT,UAIZgI,EAAiBrP,cAWfqP,EAAiBK,KAAKtI,kBAAkBuI,QAAO,SAASC,QAAQT,EAAwBU,OACzFxI,EAAUwI,EAAMxI,QAChBkF,EAAOsD,EAAMtD,QAEb9G,WAAW0J,UACNA,EAAuB9K,MAAK,SAAUuB,UACpCgK,QAAQhK,EAAK,CAClByB,QAASA,EACTkF,KAAMA,WAMR3G,EAAMW,aAAapH,EAAKkI,MAGxBxB,iBAFJD,EAAMgJ,eAAerC,EAAM3G,GAED9B,UACjB8B,EAAIvB,MAAK,SAAUS,OACpBgL,EAASlJ,aAAazH,EAAKkI,EAASvC,GAEpCgL,IAAWhL,IACb3F,EAAM2Q,UAORA,EAASlJ,aAAazH,EAAKkI,EAASzB,GAEpCkK,IAAWlK,IACbzG,EAAM2Q,UAIPzN,GA8GwB+M,UAGzBvJ,iBAAiBsJ,EAAwBrL,GAC3C4F,EAAMyF,EAAuB9K,MAAK,kBACzBlF,MAGTuK,WAtGOqG,QAAQ1I,EAAS5G,EAAOZ,EAAQsN,EAAO5M,OAC1CoO,GAA4B,WAAZtH,OAIhBkF,EAAOzE,EAAMT,GACbgG,EAAQtL,EAAQtB,MAEhB4M,GAASnH,cAAczF,GAAQ,KAE7BuP,EAAS3C,EAAQ,IAAIvL,MAAMrB,EAAMT,QAAU,OAG/Ca,EAAKJ,GAAOe,SAAQ,SAAUyO,OACxBrK,EAAMmK,QAAQ1I,GAAWA,EAAU,IAAM,IAAMlB,uBAAuB8J,GAAIxP,EAAMwP,GAAIpQ,GAAUmQ,EAAQA,EAAQC,GAE9GC,EAAM,SAASA,IAAIpL,UACjBe,iBAAiBf,EAAGoK,GACtBc,EAAOC,QAAK5N,OACGA,IAANyC,IACTkL,EAAOC,GAAKnL,GAGPA,GAGLe,iBAAiBD,EAAK9B,GACxBiL,EAAgB5N,KAAKyE,EAAIvB,MAAK,SAAUqF,UAC/BwG,EAAIxG,OAGbwG,EAAItK,MAGRnF,EAAQuP,EAEDtB,EAAmB1O,QAAQ,KAC5BmQ,EAAuBxO,eAAe+M,EAAmB,GAAI,GAC7D0B,EAAUD,EAAqB,GAC/B3J,EAAU2J,EAAqB,GAC/BE,EAAUF,EAAqB,GAC/BF,EAAIE,EAAqB,GAEzBvK,EAAMW,aAAa6J,EAAS5J,WAGpBnE,IAARuD,QACFyK,EAAQJ,GAAKrK,EAKf8I,EAAmBvE,OAAO,EAAG,QAI5BoC,SACI9L,KAGI,MAAT8L,EAAc,KACZ+D,EAAO/J,aAAa1G,EAAQY,EAAMgD,MAAM,gBAE/BpB,IAATiO,GAEF5B,EAAmBvN,KAAK,CAACtB,EAAQY,EAAMgD,MAAM,GAAI0J,EAAO5M,IAGnD+P,QAIF,GAAGlC,OAAO7B,GAAMoD,QAAO,SAASC,QAAQhK,EAAK2K,UAC9C1K,iBAAiBD,EAAK9B,GACjB8B,EAAIvB,MAAK,SAAUS,UAEjB8K,QAAQ9K,EAAGyL,MAIf3B,eAAe2B,EAAK3K,KAC1BnF,IAqBGsP,CAAQ,GAAI5Q,EAAK,MAEnB4P,EAAgB/O,SAElB0J,EAAM5F,EAAee,QAAQ6E,GAAKrF,MAAK,SAAUmM,UACxC1M,EAAe8F,IAAI,CAC1B4G,GAAGpC,OAAOW,OACT1K,MAAK,SAAUoM,UACJ9O,eAAe8O,EAAO,GACpB,QAObhL,WAAWiE,GAAOd,GAAQP,EAAKM,mBAAqB,iBACnD,IAAIhJ,UAAU,mDADqC,GAErDkG,iBAAiB6D,EAAK5F,GAAkB4F,EAAIzF,EAAEI,KAAK2K,gBAAkBtF,GAAOd,GAAQP,EAAKM,mBAAqB,iBAC5G,IAAIhJ,UAAU,mDAD8F,GAE9GiJ,EAAOoG,eAAetF,GAAOxF,QAAQW,QAAQmK,eAAetF,MASnE,CACDnJ,IAAK,aACLE,MAAO,SAASiQ,WAAWvR,EAAKkJ,UACvBrE,KAAKiF,OAAO9J,EAAKkC,iBAAeA,iBAAe,CACpDsH,oBAAoB,GACnBN,GAAO,GAAI,CACZO,MAAM,OAST,CACDrI,IAAK,cACLE,MAAO,SAASqO,YAAY3P,EAAKkJ,UACxBrE,KAAKiF,OAAO9J,EAAKkC,iBAAeA,iBAAe,CACpDsH,oBAAoB,GACnBN,GAAO,GAAI,CACZO,MAAM,OAiBT,CACDrI,IAAK,WACLE,MAAO,SAASkQ,SAASC,EAAcvI,UACrCA,EAAOA,GAAQ,MACZ+F,OAAOwC,GAAcpP,SAAQ,SAASqP,EAAEC,OACrCtM,EAAQR,QAGRjC,EAAQ+O,UACHA,EAAS7L,KAAI,SAAU8L,UACrBF,EAAErN,KAAKgB,EAAOuM,MAIzBD,GAAYjQ,EAAKiQ,GAAUtP,SAAQ,SAAUwP,MAC5B,MAAXA,QACI,IAAIrR,UAAU,wEACf,GAAI8H,QAAQwJ,WAAWxE,SAASuE,SAC/B,IAAIrR,UAAU,0DAGlBuR,EAAOJ,EAASE,GAChB9C,EAAYgD,GAAQA,EAAKC,iBAAmBnN,KAAK2D,qBAAuB3D,KAAK4D,wBAC7EwJ,EAAmBlD,EAAUlN,QAAO,SAAUwP,UACzCA,EAAEjE,OAASyE,QAGhBI,EAAiBpR,SAEnBkO,EAAU/D,OAAO+D,EAAUxH,QAAQ0K,EAAiB,IAAK,UAClDpN,KAAK6D,SAASmJ,UACdhN,KAAK8D,MAAMkJ,IAGA,mBAATE,EAAqB,KAE1BG,EAAQH,EACZA,EAAO,CACLvN,KAAM,SAASA,KAAK2N,UACXA,GAAKA,EAAEhS,cAAgB+R,GAEhChL,QAAS,SAASA,QAAQiL,UACjBjQ,iBAAe,GAAIiQ,IAE5BrI,OAAQ,SAASA,OAAOqI,UACfjR,OAAO0L,OAAO1L,OAAOkR,OAAOF,EAAM9R,WAAY+R,UAGpD,GAAIvP,EAAQmP,GAAO,KAEpBM,EAAS7P,eADDuP,EACuB,GAKnCA,EAAO,CACLvN,KALS6N,EAAO,GAMhBnL,QALYmL,EAAO,GAMnBvI,OALWuI,EAAO,OASjBN,GAASA,EAAKvN,UAIf8N,EAAc,CAChBlF,KAAMyE,EACNrN,KAAMuN,EAAKvN,KAAK+N,KAAKR,IAGnBA,EAAK7K,UACPoL,EAAYpL,QAAU6K,EAAK7K,QAAQqL,KAAKR,IAGtCA,EAAK7C,eACPoD,EAAYpD,aAAe6C,EAAK7C,aAAaqD,KAAKR,QAGhDS,EAAiC,iBAAlBtJ,EAAKuJ,SAAwBvJ,EAAKuJ,SAAWvJ,EAAKuJ,SAAW,EAAIlF,OAAOG,qBAEvFqE,EAAKC,sBACFxJ,qBAAqBwC,OAAOwH,EAAO,EAAGF,QAEtC7J,wBAAwBuC,OAAOwH,EAAO,EAAGF,GAI5CP,EAAKjI,QAAUiI,EAAKpC,YAAa,KAC/B+C,EAAa,GAEbX,EAAKjI,SACP4I,EAAW5I,OAASiI,EAAKjI,OAAOyI,KAAKR,IAGnCA,EAAKpC,cACP+C,EAAW/C,YAAcoC,EAAKpC,YAAY4C,KAAKR,SAG5CrJ,SAASmJ,GAAU,CAACa,EAAY,CACnCpC,MAAOyB,EAAKC,wBAKXrJ,MAAMkJ,GAAUE,KACpBlN,QACFA,MACIA,SAIJyD,QAtsCkB,GAgtCvByH,EAAY,SAASA,YACvB1P,kBAAgBwE,KAAMkL,uxDAIxBA,EAAU/K,kBAAoB,mBAG9BsD,EAAQyH,UAAYA,EAEpBzH,EAAQvD,QAAUJ,EAElB2D,EAAQhC,WAAaA,WACrBgC,EAAQrD,YAAcA,YACtBqD,EAAQ5B,iBAAmBA,iBAC3B4B,EAAQ9B,SAAWA,SACnB8B,EAAQvB,cAAgBA,cACxBuB,EAAQqK,aA96CR,SAASA,aAAalM,OACfA,GAA4B,WAArBxB,YAAYwB,UACf,MAGLI,EAAQV,EAASM,UAEhBI,IAKEH,iBAAiBD,EAAKvF,SAAWyR,aAAa9L,KAm6CvDyB,EAAQtB,uBAAyBA,uBACjCsB,EAAQnB,yBAA2BA,yBACnCmB,EAAQlB,aAAeA,aACvBkB,EAAQ+D,YAj1CR,SAASA,YAAY/K,UACF,OAAVA,EAAiB,OAASqB,MAAMC,QAAQtB,GAAS,QAAUvB,UAAQuB,IAi1C5EgH,EAAQwJ,WAAa,CAAC,OAAQ,UAAW,SAAU,SAAU,QAAS,UC1yDtE,IAJA,IAAIc,EAAQ,mEAERC,EAAS,IAAIC,WAAW,KAEnBlS,EAAI,EAAGA,EAAIgS,EAAM/R,OAAQD,IAChCiS,EAAOD,EAAMG,WAAWnS,IAAMA,EAUhC,IAAIoS,EAAS,SAASA,OAAOC,EAAaC,EAAYC,GAChDA,MAAAA,IACFA,EAAQF,EAAYG,oBAGlBC,EAAQ,IAAIP,WAAWG,EAAaC,GAAc,EACtDC,GACI1O,EAAM4O,EAAMxS,OACZyS,EAAS,GAEJlQ,EAAK,EAAGA,EAAKqB,EAAKrB,GAAM,EAC/BkQ,GAAUV,EAAMS,EAAMjQ,IAAO,GAC7BkQ,GAAUV,GAAmB,EAAZS,EAAMjQ,KAAY,EAAIiQ,EAAMjQ,EAAK,IAAM,GACxDkQ,GAAUV,GAAuB,GAAhBS,EAAMjQ,EAAK,KAAY,EAAIiQ,EAAMjQ,EAAK,IAAM,GAC7DkQ,GAAUV,EAAsB,GAAhBS,EAAMjQ,EAAK,WAGzBqB,EAAM,GAAM,EACd6O,EAASA,EAAOhP,MAAM,GAAI,GAAK,IACtBG,EAAM,GAAM,IACrB6O,EAASA,EAAOhP,MAAM,GAAI,GAAK,MAG1BgP,GAOLC,EAAS,SAASA,OAAOD,OAIvBE,EAAUC,EAAUC,EAAUC,EAH9BlP,EAAM6O,EAAOzS,OACb+S,EAA+B,IAAhBN,EAAOzS,OACtBiE,EAAI,EAG0B,MAA9BwO,EAAOA,EAAOzS,OAAS,KACzB+S,IAEkC,MAA9BN,EAAOA,EAAOzS,OAAS,IACzB+S,aAIAX,EAAc,IAAI9E,YAAYyF,GAC9BP,EAAQ,IAAIP,WAAWG,GAElBY,EAAM,EAAGA,EAAMpP,EAAKoP,GAAO,EAClCL,EAAWX,EAAOS,EAAOP,WAAWc,IACpCJ,EAAWZ,EAAOS,EAAOP,WAAWc,EAAM,IAC1CH,EAAWb,EAAOS,EAAOP,WAAWc,EAAM,IAC1CF,EAAWd,EAAOS,EAAOP,WAAWc,EAAM,IAC1CR,EAAMvO,KAAO0O,GAAY,EAAIC,GAAY,EACzCJ,EAAMvO,MAAmB,GAAX2O,IAAkB,EAAIC,GAAY,EAChDL,EAAMvO,MAAmB,EAAX4O,IAAiB,EAAe,GAAXC,SAG9BV,GC5EHA,EAAc,CAChBA,YAAa,CACTzO,mBAAM2N,SAAuC,gBAA3B7J,EAAQrD,YAAYkN,IACtCjL,yBAASN,EAAGsD,GACHA,EAAS4J,UACV5J,EAAS4J,QAAU,QAEjBC,EAAQ7J,EAAS4J,QAAQvM,QAAQX,UACnCmN,GAAS,EACF,CAACA,MAAAA,IAEZ7J,EAAS4J,QAAQ9R,KAAK4E,GACfoM,EAAOpM,KAElBkD,uBAAQkK,EAAK9J,MACJA,EAAS4J,UACV5J,EAAS4J,QAAU,IAEJ,WAAf/T,QAAOiU,UACA9J,EAAS4J,QAAQE,EAAID,WAE1BE,EAASV,EAAOS,UACtB9J,EAAS4J,QAAQ9R,KAAKiS,GACfA,KCvBbC,EAAe,CACjBA,aAAc,CACV1P,mBAAM2N,SACkB,WAAbpS,QAAOoS,IAAkB7J,EAAQ5B,iBAAiByL,EAAGgC,SAEhEjN,yBAAS/C,UAAYiQ,OAAOjQ,IAC5B2F,uBAAQuK,UAGG,IAAInT,OAAOiT,OAAOE,OCV/BC,EAAS,CACXA,OAAQ,CACJ9P,mBAAM2N,SACkB,iBAANA,GAElBjL,yBAAS/C,UAAYiQ,OAAOjQ,IAC5B2F,uBAAQuK,UAAYF,OAAOE,MCQnC,SAASE,mBAAoBC,WA+CnBC,EAAQ,IAAI3B,WAAW0B,EAAI3T,QACxBD,EAAI,EAAGA,EAAI4T,EAAI3T,OAAQD,IAC5B6T,EAAM7T,GAAK4T,EAAIzB,WAAWnS,UAEvB6T,EAAMR,OC/DjB,IAAMS,EAAO,CACTA,KAAM,CACFlQ,mBAAM2N,SAAuC,SAA3B7J,EAAQrD,YAAYkN,IACtCjL,yBAASN,OACC+N,EAAM,IAAIC,kBAChBD,EAAIE,iBAAiB,sCACrBF,EAAIG,KAAK,MAAOC,IAAIC,gBAAgBpO,IAAI,GACxC+N,EAAIM,OAIe,MAAfN,EAAIO,QAAiC,IAAfP,EAAIO,aACpB,IAAIxF,MAAM,oBAAsBiF,EAAIO,cAEvC,CACH9H,KAAMxG,EAAEwG,KACR+H,eAAgBR,EAAIS,eAG5BtL,8BAASsD,IAAAA,KAAM+H,IAAAA,sBACJ,IAAIE,KAAK,CAACd,mBAAmBY,IAAkB,CAAC/H,KAAAA,KAE3D8B,mCAActI,UACH,IAAI0B,EAAQvD,SAAQ,SAACW,EAASD,OAO3B6P,EAAS,IAAIC,WACnBD,EAAOE,iBAAiB,QAAQ,WAC5B9P,EAAQ,CACJ0H,KAAMxG,EAAEwG,KACR+H,eAAgBG,EAAO5J,YAK/B4J,EAAOE,iBAAiB,SAAS,WAC7B/P,EAAO6P,EAAOG,UAElBH,EAAOI,mBAAmB9O,SCnC3B,SAAS+O,mBAEhBC,EAAIC,KAAKC,OAGe,oBAAhBC,aACuB,mBAApBA,YAAYD,IACjBC,YAAYD,MACZ,SAEH,uCAAuC5O,QAAQ,SAAU,SAAU8O,OAEhE3E,EAAI4E,KAAKC,OAAON,EAAoB,GAAhBK,KAAKE,UAAiB,WAChDP,EAAIK,KAAKG,MAAMR,EAAI,KACL,MAANI,EAAY3E,EAAS,EAAJA,EAAU,GAAMjN,SAAS,OCvB1D,IAAMiS,EAAyB,GAEzBC,EAAY,CACdA,UAAW,CACP9R,mBAAM2N,UACKA,GAAkB,WAAbpS,QAAOoS,IAC8B,mBAAtCA,EAAElS,WAAW,sBAE5BiH,yBAASqP,OACCnN,EAAemN,EAAStW,WAAW,uBACnCuW,EAAOb,sBACbU,EAAuBG,GAAQD,EACxB,CAACC,KAAAA,EAAMpN,aAAAA,IAElBU,8BAAS0M,IAAAA,KAAMpN,IAAAA,oBACJiN,EAAuBG,GAAMvW,WAAW,gBAC3CmJ,MCfVqN,EAAY,CACdA,UAAW,CACPjS,mBAAM2N,SACgC,cAA3B7J,EAAQrD,YAAYkN,IAAsBA,EAAEuE,aAEvDxH,mCAAc9N,UACH,IAAIkH,EAAQvD,SAAQ,SAACW,EAASD,GAEjCkR,OAAOC,OAAOC,UAAU,MAAOzV,mBAG1BmC,GAIGkC,EAAOlC,MAIb2B,MAAK,SAAC4R,GACJpR,EAAQ,CACJoR,IAAAA,EACAC,UAAW3V,EAAI2V,UACfC,OAAQ5V,EAAI4V,gBAK5BlN,8BAASgN,IAAAA,IAAKC,IAAAA,UAAWC,IAAAA,cACdL,OAAOC,OAAOK,UAAU,MAAOH,EAAKC,GAAW,EAAMC,MC7BlEE,EAAW,CACbA,SAAU,CACN1S,mBAAM2N,SAAuC,aAA3B7J,EAAQrD,YAAYkN,IACtCjL,2BAA2CgD,OAAjC+J,IAAAA,OAAQf,IAAAA,WAAYE,IAAAA,WACrBlJ,EAAS4J,UACV5J,EAAS4J,QAAU,QAEjBC,EAAQ7J,EAAS4J,QAAQvM,QAAQ0M,UACnCF,GAAS,EACF,CAACA,MAAAA,EAAOb,WAAAA,EAAYE,WAAAA,IAE/BlJ,EAAS4J,QAAQ9R,KAAKiS,GACf,CACHkD,QAASnE,EAAOiB,GAChBf,WAAAA,EACAE,WAAAA,KAGRtJ,uBAAQsN,EAAQlN,GACPA,EAAS4J,UACV5J,EAAS4J,QAAU,QAGnBG,EADGf,EAA0CkE,EAA1ClE,WAAYE,EAA8BgE,EAA9BhE,WAAY+D,EAAkBC,EAAlBD,QAASpD,EAASqD,EAATrD,YAEpC,UAAWqD,EACXnD,EAAS/J,EAAS4J,QAAQC,IAE1BE,EAASV,EAAO4D,GAChBjN,EAAS4J,QAAQ9R,KAAKiS,IAEnB,IAAIoD,SAASpD,EAAQf,EAAYE,MC/B9CkE,EAAO,CACTA,KAAM,CACF9S,mBAAM2N,SAAuC,SAA3B7J,EAAQrD,YAAYkN,IACtCjL,yBAASqQ,OACCC,EAAOD,EAAGE,iBACZlK,OAAOC,MAAMgK,GACN,MAEJA,GAEX1N,uBAAQ0N,SACS,QAATA,EACO,IAAI3B,KAAKtI,OAAOmK,KAEpB,IAAI7B,KAAK2B,MCdtB/B,EAAQ,CACVA,MAAO,CACHjR,mBAAM2N,SAAuC,UAA3B7J,EAAQrD,YAAYkN,IACtCjL,kCACW,CAAC3C,OADFA,KACQoT,UADFA,UAGhB7N,8BAASvF,IAAAA,KAAMoT,IAAAA,QACL3P,EAAI,IAAI0H,MAAMiI,UACpB3P,EAAEzD,KAAOA,EACFyD,KCPb4P,EAA0B,oBAATC,KAAuBC,OAASD,KAEjDE,EAAS,GAEf,CACI,YACA,aACA,cACA,iBACA,YACA,WACA,iBACF1V,SAAQ,SAAC2V,OACDC,EAAWL,EAAQI,GACrBC,IACAF,EAAOC,EAAQE,eAAiB,CAC5B1T,mBAAM2N,UAAY7J,EAAQ5B,iBAAiByL,EAAG8F,IAC9C/Q,yBAASc,UAAYA,EAAE2P,SACvB7N,uBAAQ6N,UAAkB,IAAIM,EAASN,SClBnD,IAAMQ,EAAO,CACTA,KAAM,CACF3T,mBAAM2N,SAAuC,SAA3B7J,EAAQrD,YAAYkN,IACtCjL,yBAAStC,OACC+P,EAAM,IAAIC,kBAChBD,EAAIE,iBAAiB,sCACrBF,EAAIG,KAAK,MAAOC,IAAIC,gBAAgBpQ,IAAI,GACxC+P,EAAIM,OAIe,MAAfN,EAAIO,QAAiC,IAAfP,EAAIO,aACpB,IAAIxF,MAAM,oBAAsBiF,EAAIO,cAEvC,CACH9H,KAAMxI,EAAEwI,KACR+H,eAAgBR,EAAIS,aACpB7Q,KAAMK,EAAEL,KACR6T,aAAcxT,EAAEwT,eAGxBtO,8BAASvF,IAAAA,KAAM6I,IAAAA,KAAM+H,IAAAA,eAAgBiD,IAAAA,oBAC1B,IAAIC,KAAK,CAAC9D,mBAAmBY,IAAkB5Q,EAAM,CACxD6I,KAAAA,EACAgL,aAAAA,KAGRlJ,mCAActK,UACH,IAAI0D,EAAQvD,SAAQ,SAAUW,EAASD,OAOpC6P,EAAS,IAAIC,WACnBD,EAAOE,iBAAiB,QAAQ,WAC5B9P,EAAQ,CACJ0H,KAAMxI,EAAEwI,KACR+H,eAAgBG,EAAO5J,OACvBnH,KAAMK,EAAEL,KACR6T,aAAcxT,EAAEwT,kBAKxB9C,EAAOE,iBAAiB,SAAS,WAC7B/P,EAAO6P,EAAOG,UAElBH,EAAOI,mBAAmB9Q,SClDpC0T,EAAW,CACbH,KAAMA,EAAKA,KACXG,SAAU,CACN9T,mBAAM2N,SAAuC,aAA3B7J,EAAQrD,YAAYkN,IACtCjL,yBAASqR,WACC9V,EAAM,GACH7B,EAAI,EAAGA,EAAI2X,EAAG1X,OAAQD,IAC3B6B,EAAI7B,GAAK2X,EAAGC,KAAK5X,UAEd6B,GAEXqH,uBAAQ7F,UA6BG,uEAnBMwU,OAAStW,UAAU,QACnBtB,OAASgE,KAAK4T,OAAO5X,+IAM9B,cAAMkT,UACKlP,KAAK4T,OAAO1E,UAMlB9T,OAAOgF,gBAAZ,qBAEW,2BAGR,CAAahB,MCrC1ByU,EAAc,CAChBA,YAAa,CACTlU,mBAAM2N,SACgC,gBAA3B7J,EAAQrD,YAAYkN,IAGtBA,GAAKA,EAAEwG,SAAqC,gBAA1BxG,EAAEwG,QAAQ1T,aAErCiC,yBAAS0R,OACCC,EAASC,SAASC,cAAc,iBAC1BF,EAAOG,WAAW,MAC1BC,UAAUL,EAAI,EAAG,GAOdC,EAAOK,aAElBpP,uBAAQ7F,OAUE4U,EAASC,SAASC,cAAc,UAChCI,EAAMN,EAAOG,WAAW,MACxBI,EAAMN,SAASC,cAAc,cAEnCK,EAAI5D,iBAAiB,QAAQ,WACzB2D,EAAIF,UAAUG,EAAK,EAAG,MAE1BA,EAAIC,IAAMpV,EAGH4U,GAEXlJ,iCAAa1L,OACH4U,EAASC,SAASC,cAAc,UAChCI,EAAMN,EAAOG,WAAW,MACxBI,EAAMN,SAASC,cAAc,cAEnCK,EAAI5D,iBAAiB,QAAQ,WACzB2D,EAAIF,UAAUG,EAAK,EAAG,MAE1BA,EAAIC,IAAMpV,EACHqV,kBAAkBT,MCnD/BU,EAAY,CACdA,UAAW,CACP/U,mBAAM2N,SAAuC,cAA3B7J,EAAQrD,YAAYkN,IACtCjL,yBAAS0O,SACE,CAEHnB,yBAAWmB,EAAE4D,MACbC,MAAO7D,EAAE6D,MACTC,OAAQ9D,EAAE8D,SAGlB5P,uBAAQ7F,UACG,IAAI0V,UACP,IAAIC,kBAAkB3V,EAAEwQ,OAAQxQ,EAAEwV,MAAOxV,EAAEyV,WCnBrDG,EAAW,CACbA,SAAU,CACNrV,mBAAM2N,UAAYA,IAAM5E,OAAOG,mBAC/BxG,yBAAS/C,SAAY,YACrB2F,uBAAQuK,UAAY9G,OAAOG,qBCkB7BoM,EAAY,CACdC,aArBiB,CACjBvV,mBAAM2N,UAAY7J,EAAQ5B,iBAAiByL,EAAG6H,KAAKC,WACnD/S,yBAAS8O,UAAYA,EAAEkE,mBACvBpQ,uBAAQvB,UAAkB,IAAIyR,KAAKC,SAAS1R,EAAQ4R,OAAQ5R,KAmB5D6R,mBAhBuB,CACvB5V,mBAAM2N,UAAY7J,EAAQ5B,iBAAiByL,EAAG6H,KAAKK,iBACnDnT,yBAASoT,UAAcA,EAAIJ,mBAC3BpQ,uBAAQvB,UACG,IAAIyR,KAAKK,eAAe9R,EAAQ4R,OAAQ5R,KAanDgS,iBATqB,CACrB/V,mBAAM2N,UAAY7J,EAAQ5B,iBAAiByL,EAAG6H,KAAKQ,eACnDtT,yBAASuT,UAAaA,EAAGP,mBACzBpQ,uBAAQvB,UAAkB,IAAIyR,KAAKQ,aAAajS,EAAQ4R,OAAQ5R,MCjB9DzC,EAAM,CACRA,IAAK,CACDtB,mBAAM2N,SAAuC,QAA3B7J,EAAQrD,YAAYkN,IACtCjL,yBAASwT,6BAAiBA,EAAGvK,YAC7BrG,uBAAQqG,UAAkB,IAAIwK,IAAIxK,MCNpCyK,EAAM,CACRA,IAAK,CACDpW,mBAAM2N,UAAY5E,OAAOC,MAAM2E,IAC/BjL,yBAAS/C,SAAY,OACrB2F,uBAAQuK,UAAY9G,OAAOmK,OCJ7BmD,EAAmB,CACrBA,iBAAkB,CACdrW,mBAAM2N,UAAYA,IAAM5E,OAAOE,mBAC/BvG,yBAAS/C,SAAY,aACrB2F,uBAAQuK,UAAY9G,OAAOE,qBCF7BqN,EAAmB,CACrBA,iBAAkB,CACdtW,mBAAM2N,UACKA,GAAkB,WAAbpS,QAAOoS,KAAmBxP,MAAMC,QAAQuP,KAAO,CACvD,mBAIW,SAAU,SACrB,QAAS,SAAU,OAAQ,OAC3B,MAAO,MACP,OACA,cAAe,oBAAqB,WACpC,YAAa,aAAc,oBAAqB,aAChD,cAAe,aAAc,cAC7B,eAAgB,eAChB,UACA,kBAAmB,iBACnB,eAAgB,eAChB,UAAW,UACX,UAAW,UACb7E,SAAShF,EAAQrD,YAAYkN,KAEnCjL,yBAAS6T,OCpBXC,EAAmB,CAErBC,aAAc,CACVzW,mBAAM2N,SACgC,WAA3B7J,EAAQrD,YAAYkN,IAAgC,WAAbpS,QAAOoS,IAEzDjL,yBAASmN,UAAYD,OAAOC,IAC5BvK,uBAAQuK,UAAY,IAAID,OAAOC,KAGnC6G,cAAe,CACX1W,mBAAM2N,SACgC,YAA3B7J,EAAQrD,YAAYkN,IACV,WAAbpS,QAAOoS,IAEfjL,yBAASN,UAAY2H,QAAQ3H,IAC7BkD,uBAAQlD,UAEG,IAAI2H,QAAQ3H,KAI3BuU,aAAc,CACV3W,mBAAM2N,SACgC,WAA3B7J,EAAQrD,YAAYkN,IAAgC,WAAbpS,QAAOoS,IAEzDjL,yBAAS/C,UAAYoJ,OAAOpJ,IAC5B2F,uBAAQ3F,UAAY,IAAIoJ,OAAOpJ,MC9BjCiX,EAAS,CACXA,OAAQ,CACJ5W,mBAAM2N,SAAuC,WAA3B7J,EAAQrD,YAAYkN,IACtCjL,yBAAS6T,SACE,CACH3Y,OAAQ2Y,EAAK3Y,OACbiZ,OAAQN,EAAKjD,OAAS,IAAM,KACvBiD,EAAKO,WAAa,IAAM,KACxBP,EAAKQ,UAAY,IAAM,KACvBR,EAAKS,OAAS,IAAM,KACpBT,EAAKU,QAAU,IAAM,MAGlC3R,8BAAS1H,IAAAA,OAAQiZ,IAAAA,aAAiB,IAAIK,OAAOtZ,EAAQiZ,MCRvDM,EAA6B,GAE7BC,EAAgB,CAClBA,cAAe,CACXpX,mBAAM2N,UACKA,IACFxP,MAAMC,QAAQuP,IACf,CAAC,SAAU,WAAY,UAAU7E,iBAAgB6E,KAEzDjL,yBAAS2U,OACCrF,EAAOb,sBACbgG,EAA2BnF,GAAQqF,EAC5BrF,GAEX1M,uBAAQgS,UACGH,EAA2BG,MCpBxC/K,EAAM,CACRA,IAAK,CACDvM,mBAAM2N,SAAuC,QAA3B7J,EAAQrD,YAAYkN,IACtCjL,yBAAS6U,6BACMA,EAAG3P,WAElBtC,uBAAQsC,UAAiB,IAAIE,IAAIF,MCJnCwL,EAA0B,oBAATC,KAAuBC,OAASD,KAGjDmE,EAAsB,GAC5B,CACI,YACA,aACA,oBACA,aACA,cACA,aACA,cACA,eACA,gBACF3Z,SAAQ,SAAU4Z,OACVC,EAAUD,EACVE,EAAavE,EAAQqE,GAEtBE,IAGLH,EAAoBC,EAAS/D,eAAiB,CAC1C1T,mBAAM2N,UAAY7J,EAAQrD,YAAYkN,KAAO+J,GAC7ChV,yBAASP,UACoB,IAAjBA,EAAEuM,YACNvM,EAAEyM,aAAezM,EAAEsN,OAAOb,WACxBzM,EAKAA,EAAErC,MAAM,IAAI2P,QAEtBnK,uBAAQsS,SAOgC,gBAA7B9T,EAAQrD,YAAYmX,GACrB,IAAID,EAAWC,GACfA,QCzClB,IAAMxE,EAA0B,oBAATC,KAAuBC,OAASD,KAEjDwE,EAAc,GACpB,CACI,YACA,aACA,oBACA,aACA,cACA,aACA,cACA,eACA,gBACFha,SAAQ,SAAU4Z,OACVC,EAAUD,EACVE,EAAavE,EAAQsE,GAEtBC,IAGLE,EAAYJ,EAAS/D,eAAiB,CAClC1T,mBAAM2N,UAAY7J,EAAQrD,YAAYkN,KAAO+J,GAC7ChV,2BAA0CgD,OAAhC+J,IAAAA,OAAQf,IAAAA,WAAoBoJ,IAARzb,OACrBqJ,EAAS4J,UACV5J,EAAS4J,QAAU,QAEjBC,EAAQ7J,EAAS4J,QAAQvM,QAAQ0M,UACnCF,GAAS,EACF,CAACA,MAAAA,EAAOb,WAAAA,EAAYrS,OAAQyb,IAEvCpS,EAAS4J,QAAQ9R,KAAKiS,GACf,CACHkD,QAASnE,EAAOiB,GAChBf,WAAAA,EACArS,OAAQyb,KAGhBxS,uBAAQsN,EAAQlN,GACPA,EAAS4J,UACV5J,EAAS4J,QAAU,QAGnBG,EADGf,EAA2CkE,EAA3ClE,WAAoBzO,EAAuB2S,EAA/BvW,OAAasW,EAAkBC,EAAlBD,QAASpD,EAASqD,EAATrD,YAErC,UAAWqD,EACXnD,EAAS/J,EAAS4J,QAAQC,IAE1BE,EAASV,EAAO4D,GAChBjN,EAAS4J,QAAQ9R,KAAKiS,IAEnB,IAAIkI,EAAWlI,EAAQf,EAAYzO,SClDtD,IAAM8X,EAAQ,CACVA,MAAO,CACH/X,mBAAM2N,EAAGjI,eACe,IAANiI,IACTjI,EAAS3I,WAAa,YAAa2I,KAE5ChD,yBAAS/C,UAAY,GACrB2F,uBAAQuK,UAGG,IAAI/L,EAAQyH,aCZzByM,EAAa,CACfA,WAAY,CACRhY,mBAAM2N,EAAGjI,UAAmB5B,EAAQqK,aAAaR,IACjDjL,yBAAS/C,sZAAgBA,IACzB2F,uBAAQuK,UAAYA,KCNtBoI,EAAoB,CACtB,CACIA,kBAAmB,CACfzK,kBAAkB,EAClBxN,mBAAM2N,EAAGjI,WACDvH,MAAMC,QAAQuP,KAMVjR,OAAOQ,KAAKyQ,GAAGuK,MAAK,SAAC5L,UAQVsD,OAAO7G,OAAOoP,SAAS7L,MAAQA,OAG1C5G,EAAS0D,UAAY,SACrB1D,EAASkE,WAAY,IAElB,IAIflH,yBAASP,EAAGuD,UAERA,EAAS2D,qBAAsB,EACxBlH,GAEXmD,uBAAQ7F,MACAtB,MAAMC,QAAQqB,UACPA,MAELxB,EAAM,UAKZvB,OAAOQ,KAAKuC,GAAG5B,SAAQ,SAACjB,OACdqF,EAAMxC,EAAE7C,GACdqB,EAAIrB,GAAOqF,KAERhE,KAInB,CACIma,gBAAiB,CACbpY,mBAAM2N,EAAGjI,eACe,IAANiI,IAA0C,IAArBjI,EAAS3I,SAEhD2F,yBAAS/C,UAAY,GACrB2F,uBAAQuK,QCrDdwI,EAAiB,CACnBjC,EACAf,EACAiD,GC0BEC,EAAS,CACXR,EAEAE,EAAmBzB,EAAkB6B,EACrCvF,EAAM7B,EAAOsC,EAAQqD,GACvBnM,OAGiB,mBAAR0L,IAAqB7U,EAAM,GAEnB,mBAARwG,IAAqByE,EAAM,GAEX,mBAAhB5C,YAA6B8E,EAAc,GAE5B,mBAAfH,WAA4BuJ,EAAc,GAE7B,mBAAbhF,SAA0BH,EAAW,GAE5B,oBAAT8C,KAAuBF,EAAY,GAGxB,oBAAX3F,OAAyB,CAACG,EAAQJ,GAAgB,ICnCvD8I,EAAc,CAChBvH,EACAsC,GClBEkF,GAAW,CACbC,EAEA,CAACjK,YAAa,MAEd+I,GCREY,GAAkB,CACpB,CACIO,aAAc,CACVnL,kBAAkB,EAClBxN,mBAAM2N,UAAYxP,MAAMC,QAAQuP,IAChCjL,yBAASP,EAAGuD,UACRA,EAAS2D,qBAAsB,EACxBlH,KAInB,CACIiW,gBAAiB,CACbpY,mBAAM2N,EAAGjI,eACe,IAANiI,IAA0C,IAArBjI,EAAS3I,SAEhD2F,yBAAS/C,UAAY,GACrB2F,uBAAQuK,QCSd0I,GAAS,CAIXP,IAGAC,EAAmBzB,EAAkB6B,EACrCvF,EAAM8D,EAGN7B,EACAb,EACAP,EACAG,EACA5D,GACFzF,OAGiB,mBAAR0L,IAAqB7U,EAAM,GAEnB,mBAARwG,IAAqByE,EAAM,GAEX,mBAAhB5C,YAA6B8E,EAAc,GAE5B,mBAAfH,WAA4BuJ,EAAc,GAE7B,mBAAbhF,SAA0BH,EAAW,GAE5B,oBAAT8C,KAAuBF,EAAY,GAExB,oBAAXnD,OAAyBF,EAAY,GAE1B,oBAAXtC,OAAyB,CAACG,EAAQJ,GAAgB,OCxD9CkJ,GAAkBnO,OAAO,CACpCoO,wBAAyB,CACrB7Y,mBAAMiC,OAqBI6W,EAAa,GAAGlZ,SAASC,KAAKoC,GAAKnC,MAAM,GAAI,MAE/C,sBAMEgJ,iBAAgB7G,KAClB,sFAsBE6G,SAASgQ,IAMV7W,GAAsB,WAAf1G,QAAO0G,IAGa,iBAAjBA,EAAI8W,UACiB,mBAArB9W,EAAI+W,mBAET,IAAIC,aACN,+BAAgC,yBAGjC,MCtEblB,GAAQ,CACVK,GACAc,GCHEC,GAAY,CACdT,UC2CJ5U,EAAQK,MAAQ,CACZsK,YAAAA,EAAaiB,aAAAA,EAAcI,OAAAA,EAAQI,KAAAA,EAAM4B,UAAAA,EAAWG,UAAAA,EAAWS,SAAAA,EAC/DI,KAAAA,EAAM7B,MAAAA,EAAOsC,OAAAA,EAAQI,KAAAA,EAAMG,SAAAA,EAAUI,YAAAA,EAAaa,UAAAA,EAAWM,SAAAA,EAC7DC,UAAAA,EAAWhU,IAAAA,EAAK8U,IAAAA,EAAKC,iBAAAA,EAAkBC,iBAAAA,EAAkBE,iBAAAA,EACzDI,OAAAA,EAAQQ,cAAAA,EAAe7K,IAAAA,EAAK6M,oBAAAA,EAAqBvB,YAAAA,EAAaE,MAAAA,EAC9DC,WAAAA,GAEJlU,EAAQuV,QAAU,CACdpB,kBAAAA,EAAmBS,QAAAA,EAASF,YAAAA,EAAaC,SAAAA,GAAUL,gBAAAA,GACnDC,eAAAA,EAAgBiB,0BAAAA,GAA2BV,kBAAAA,GAC3Cb,MAAOwB,GAAaJ,UAAAA"}