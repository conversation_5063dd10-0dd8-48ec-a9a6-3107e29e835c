!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).Typeson=t()}(this,(function(){"use strict";function _typeof$1(e){return(_typeof$1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function _typeof(e){return typeof e}:function _typeof(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck$1(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties$1(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function _defineProperty$1(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ownKeys$1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _objectSpread2$1(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys$1(Object(r),!0).forEach((function(t){_defineProperty$1(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys$1(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function _slicedToArray(e,t){return function _arrayWithHoles(e){if(Array.isArray(e))return e}(e)||function _iterableToArrayLimit(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=e[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!t||r.length!==t);n=!0);}catch(e){o=!0,i=e}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}(e,t)||_unsupportedIterableToArray$1(e,t)||function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _toConsumableArray$1(e){return function _arrayWithoutHoles$1(e){if(Array.isArray(e))return _arrayLikeToArray$1(e)}(e)||function _iterableToArray$1(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||_unsupportedIterableToArray$1(e)||function _nonIterableSpread$1(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _unsupportedIterableToArray$1(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray$1(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray$1(e,t):void 0}}function _arrayLikeToArray$1(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var e=function TypesonPromise(e){_classCallCheck$1(this,TypesonPromise),this.p=new Promise(e)};e.__typeson__type__="TypesonPromise","undefined"!=typeof Symbol&&(e.prototype[Symbol.toStringTag]="TypesonPromise"),e.prototype.then=function(t,r){var n=this;return new e((function(e,o){n.p.then((function(r){e(t?t(r):r)})).catch((function(e){return r?r(e):Promise.reject(e)})).then(e,o)}))},e.prototype.catch=function(e){return this.then(null,e)},e.resolve=function(t){return new e((function(e){e(t)}))},e.reject=function(t){return new e((function(e,r){r(t)}))},["all","race"].forEach((function(t){e[t]=function(r){return new e((function(e,n){Promise[t](r.map((function(e){return e&&e.constructor&&"TypesonPromise"===e.constructor.__typeson__type__?e.p:e}))).then(e,n)}))}}));var t={}.toString,r={}.hasOwnProperty,n=Object.getPrototypeOf,o=r.toString;function isThenable(e,t){return isObject(e)&&"function"==typeof e.then&&(!t||"function"==typeof e.catch)}function toStringTag(e){return t.call(e).slice(8,-1)}function hasConstructorOf(e,t){if(!e||"object"!==_typeof$1(e))return!1;var i=n(e);if(!i)return null===t;var a=r.call(i,"constructor")&&i.constructor;return"function"!=typeof a?null===t:t===a||(null!==t&&o.call(a)===o.call(t)||"function"==typeof t&&"string"==typeof a.__typeson__type__&&a.__typeson__type__===t.__typeson__type__)}function isPlainObject(e){return!(!e||"Object"!==toStringTag(e))&&(!n(e)||hasConstructorOf(e,Object))}function isObject(e){return e&&"object"===_typeof$1(e)}function escapeKeyPathComponent(e){return e.replace(/~/g,"~0").replace(/\./g,"~1")}function unescapeKeyPathComponent(e){return e.replace(/~1/g,".").replace(/~0/g,"~")}function getByKeyPath(e,t){if(""===t)return e;var r=t.indexOf(".");if(r>-1){var n=e[unescapeKeyPathComponent(t.slice(0,r))];return void 0===n?void 0:getByKeyPath(n,t.slice(r+1))}return e[unescapeKeyPathComponent(t)]}function setAtKeyPath(e,t,r){if(""===t)return r;var n=t.indexOf(".");return n>-1?setAtKeyPath(e[unescapeKeyPathComponent(t.slice(0,n))],t.slice(n+1),r):(e[unescapeKeyPathComponent(t)]=r,e)}function _await(e,t,r){return r?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var i=Object.keys,a=Array.isArray,c={}.hasOwnProperty,u=["type","replaced","iterateIn","iterateUnsetNumeric"];function _async(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}function nestedPathsFirst(e,t){if(""===e.keypath)return-1;var r=e.keypath.match(/\./g)||0,n=t.keypath.match(/\./g)||0;return r&&(r=r.length),n&&(n=n.length),r>n?-1:r<n?1:e.keypath<t.keypath?-1:e.keypath>t.keypath}var s=function(){function Typeson(e){_classCallCheck$1(this,Typeson),this.options=e,this.plainObjectReplacers=[],this.nonplainObjectReplacers=[],this.revivers={},this.types={}}return function _createClass$1(e,t,r){return t&&_defineProperties$1(e.prototype,t),r&&_defineProperties$1(e,r),e}(Typeson,[{key:"stringify",value:function stringify(e,t,r,n){n=_objectSpread2$1(_objectSpread2$1(_objectSpread2$1({},this.options),n),{},{stringification:!0});var o=this.encapsulate(e,null,n);return a(o)?JSON.stringify(o[0],t,r):o.then((function(e){return JSON.stringify(e,t,r)}))}},{key:"stringifySync",value:function stringifySync(e,t,r,n){return this.stringify(e,t,r,_objectSpread2$1(_objectSpread2$1({throwOnBadSyncType:!0},n),{},{sync:!0}))}},{key:"stringifyAsync",value:function stringifyAsync(e,t,r,n){return this.stringify(e,t,r,_objectSpread2$1(_objectSpread2$1({throwOnBadSyncType:!0},n),{},{sync:!1}))}},{key:"parse",value:function parse(e,t,r){return r=_objectSpread2$1(_objectSpread2$1(_objectSpread2$1({},this.options),r),{},{parse:!0}),this.revive(JSON.parse(e,t),r)}},{key:"parseSync",value:function parseSync(e,t,r){return this.parse(e,t,_objectSpread2$1(_objectSpread2$1({throwOnBadSyncType:!0},r),{},{sync:!0}))}},{key:"parseAsync",value:function parseAsync(e,t,r){return this.parse(e,t,_objectSpread2$1(_objectSpread2$1({throwOnBadSyncType:!0},r),{},{sync:!1}))}},{key:"specialTypeNames",value:function specialTypeNames(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r.returnTypeNames=!0,this.encapsulate(e,t,r)}},{key:"rootTypeName",value:function rootTypeName(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r.iterateNone=!0,this.encapsulate(e,t,r)}},{key:"encapsulate",value:function encapsulate(t,r,n){var o=_async((function(t,r){return _await(Promise.all(r.map((function(e){return e[1].p}))),(function(n){return _await(Promise.all(n.map(_async((function(n){var i=!1,a=[],c=_slicedToArray(r.splice(0,1),1),u=_slicedToArray(c[0],7),s=u[0],f=u[2],l=u[3],p=u[4],y=u[5],v=u[6],b=_encapsulate(s,n,f,l,a,!0,v),d=hasConstructorOf(b,e);return function _invoke(e,t){var r=e();return r&&r.then?r.then(t):t(r)}((function(){if(s&&d)return _await(b.p,(function(e){return p[y]=e,i=!0,o(t,a)}))}),(function(e){return i?e:(s?p[y]=b:t=d?b.p:b,o(t,a))}))})))),(function(){return t}))}))})),s=(n=_objectSpread2$1(_objectSpread2$1({sync:!0},this.options),n)).sync,f=this,l={},p=[],y=[],v=[],b=!("cyclic"in n)||n.cyclic,d=n.encapsulateObserver,h=_encapsulate("",t,b,r||{},v);function finish(e){var t=Object.values(l);if(n.iterateNone)return t.length?t[0]:Typeson.getJSONType(e);if(t.length){if(n.returnTypeNames)return _toConsumableArray$1(new Set(t));e&&isPlainObject(e)&&!c.call(e,"$types")?e.$types=l:e={$:e,$types:{$:l}}}else isObject(e)&&c.call(e,"$types")&&(e={$:e,$types:!0});return!n.returnTypeNames&&e}function _adaptBuiltinStateObjectProperties(e,t,r){Object.assign(e,t);var n=u.map((function(t){var r=e[t];return delete e[t],r}));r(),u.forEach((function(t,r){e[t]=n[r]}))}function _encapsulate(t,r,o,u,s,v,b){var h,g={},m=_typeof$1(r),O=d?function(n){var i=b||u.type||Typeson.getJSONType(r);d(Object.assign(n||g,{keypath:t,value:r,cyclic:o,stateObj:u,promisesData:s,resolvingTypesonPromise:v,awaitingTypesonPromise:hasConstructorOf(r,e)},{type:i}))}:null;if(["string","boolean","number","undefined"].includes(m))return void 0===r||Number.isNaN(r)||r===Number.NEGATIVE_INFINITY||r===Number.POSITIVE_INFINITY?(h=u.replaced?r:replace(t,r,u,s,!1,v,O))!==r&&(g={replaced:h}):h=r,O&&O(),h;if(null===r)return O&&O(),r;if(o&&!u.iterateIn&&!u.iterateUnsetNumeric&&r&&"object"===_typeof$1(r)){var _=p.indexOf(r);if(!(_<0))return l[t]="#",O&&O({cyclicKeypath:y[_]}),"#"+y[_];!0===o&&(p.push(r),y.push(t))}var S,j=isPlainObject(r),A=a(r),w=(j||A)&&(!f.plainObjectReplacers.length||u.replaced)||u.iterateIn?r:replace(t,r,u,s,j||A,null,O);if(w!==r?(h=w,g={replaced:w}):""===t&&hasConstructorOf(r,e)?(s.push([t,r,o,u,void 0,void 0,u.type]),h=r):A&&"object"!==u.iterateIn||"array"===u.iterateIn?(S=new Array(r.length),g={clone:S}):(["function","symbol"].includes(_typeof$1(r))||"toJSON"in r||hasConstructorOf(r,e)||hasConstructorOf(r,Promise)||hasConstructorOf(r,ArrayBuffer))&&!j&&"object"!==u.iterateIn?h=r:(S={},u.addLength&&(S.length=r.length),g={clone:S}),O&&O(),n.iterateNone)return S||h;if(!S)return h;if(u.iterateIn){var T=function _loop(n){var i={ownKeys:c.call(r,n)};_adaptBuiltinStateObjectProperties(u,i,(function(){var i=t+(t?".":"")+escapeKeyPathComponent(n),a=_encapsulate(i,r[n],Boolean(o),u,s,v);hasConstructorOf(a,e)?s.push([i,a,Boolean(o),u,S,n,u.type]):void 0!==a&&(S[n]=a)}))};for(var I in r)T(I);O&&O({endIterateIn:!0,end:!0})}else i(r).forEach((function(n){var i=t+(t?".":"")+escapeKeyPathComponent(n);_adaptBuiltinStateObjectProperties(u,{ownKeys:!0},(function(){var t=_encapsulate(i,r[n],Boolean(o),u,s,v);hasConstructorOf(t,e)?s.push([i,t,Boolean(o),u,S,n,u.type]):void 0!==t&&(S[n]=t)}))})),O&&O({endIterateOwn:!0,end:!0});if(u.iterateUnsetNumeric){for(var P=r.length,C=function _loop2(n){if(!(n in r)){var i=t+(t?".":"")+n;_adaptBuiltinStateObjectProperties(u,{ownKeys:!1},(function(){var t=_encapsulate(i,void 0,Boolean(o),u,s,v);hasConstructorOf(t,e)?s.push([i,t,Boolean(o),u,S,n,u.type]):void 0!==t&&(S[n]=t)}))}},N=0;N<P;N++)C(N);O&&O({endIterateUnsetNumeric:!0,end:!0})}return S}function replace(e,t,r,n,o,i,a){for(var c=o?f.plainObjectReplacers:f.nonplainObjectReplacers,u=c.length;u--;){var p=c[u];if(p.test(t,r)){var y=p.type;if(f.revivers[y]){var v=l[e];l[e]=v?[y].concat(v):y}return Object.assign(r,{type:y,replaced:!0}),!s&&p.replaceAsync||p.replace?(a&&a({replacing:!0}),_encapsulate(e,p[s||!p.replaceAsync?"replace":"replaceAsync"](t,r),b&&"readonly",r,n,i,y)):(a&&a({typeDetected:!0}),_encapsulate(e,t,b&&"readonly",r,n,i,y))}}return t}return v.length?s&&n.throwOnBadSyncType?function(){throw new TypeError("Sync method requested but async result obtained")}():Promise.resolve(o(h,v)).then(finish):!s&&n.throwOnBadSyncType?function(){throw new TypeError("Async method requested but sync result obtained")}():n.stringification&&s?[finish(h)]:s?finish(h):Promise.resolve(finish(h))}},{key:"encapsulateSync",value:function encapsulateSync(e,t,r){return this.encapsulate(e,t,_objectSpread2$1(_objectSpread2$1({throwOnBadSyncType:!0},r),{},{sync:!0}))}},{key:"encapsulateAsync",value:function encapsulateAsync(e,t,r){return this.encapsulate(e,t,_objectSpread2$1(_objectSpread2$1({throwOnBadSyncType:!0},r),{},{sync:!1}))}},{key:"revive",value:function revive(t,r){var n=t&&t.$types;if(!n)return t;if(!0===n)return t.$;var o=(r=_objectSpread2$1(_objectSpread2$1({sync:!0},this.options),r)).sync,c=[],u={},s=!0;n.$&&isPlainObject(n.$)&&(t=t.$,n=n.$,s=!1);var l=this;function executeReviver(e,t){var r=_slicedToArray(l.revivers[e]||[],1)[0];if(!r)throw new Error("Unregistered type: "+e);return o&&!("revive"in r)?t:r[o&&r.revive?"revive":!o&&r.reviveAsync?"reviveAsync":"revive"](t,u)}var p=[];function checkUndefined(e){return hasConstructorOf(e,f)?void 0:e}var y,v=function revivePlainObjects(){var r=[];if(Object.entries(n).forEach((function(e){var t=_slicedToArray(e,2),o=t[0],i=t[1];"#"!==i&&[].concat(i).forEach((function(e){_slicedToArray(l.revivers[e]||[null,{}],2)[1].plain&&(r.push({keypath:o,type:e}),delete n[o])}))})),r.length)return r.sort(nestedPathsFirst).reduce((function reducer(r,n){var o=n.keypath,i=n.type;if(isThenable(r))return r.then((function(e){return reducer(e,{keypath:o,type:i})}));var a=getByKeyPath(t,o);if(hasConstructorOf(a=executeReviver(i,a),e))return a.then((function(e){var r=setAtKeyPath(t,o,e);r===e&&(t=r)}));var c=setAtKeyPath(t,o,a);c===a&&(t=c)}),void 0)}();return hasConstructorOf(v,e)?y=v.then((function(){return t})):(y=function _revive(t,r,o,u,l){if(!s||"$types"!==t){var y=n[t],v=a(r);if(v||isPlainObject(r)){var b=v?new Array(r.length):{};for(i(r).forEach((function(n){var i=_revive(t+(t?".":"")+escapeKeyPathComponent(n),r[n],o||b,b,n),a=function set(e){return hasConstructorOf(e,f)?b[n]=void 0:void 0!==e&&(b[n]=e),e};hasConstructorOf(i,e)?p.push(i.then((function(e){return a(e)}))):a(i)})),r=b;c.length;){var d=_slicedToArray(c[0],4),h=d[0],g=d[1],m=d[2],O=d[3],_=getByKeyPath(h,g);if(void 0===_)break;m[O]=_,c.splice(0,1)}}if(!y)return r;if("#"===y){var S=getByKeyPath(o,r.slice(1));return void 0===S&&c.push([o,r.slice(1),u,l]),S}return[].concat(y).reduce((function reducer(t,r){return hasConstructorOf(t,e)?t.then((function(e){return reducer(e,r)})):executeReviver(r,t)}),r)}}("",t,null),p.length&&(y=e.resolve(y).then((function(t){return e.all([t].concat(p))})).then((function(e){return _slicedToArray(e,1)[0]})))),isThenable(y)?o&&r.throwOnBadSyncType?function(){throw new TypeError("Sync method requested but async result obtained")}():hasConstructorOf(y,e)?y.p.then(checkUndefined):y:!o&&r.throwOnBadSyncType?function(){throw new TypeError("Async method requested but sync result obtained")}():o?checkUndefined(y):Promise.resolve(checkUndefined(y))}},{key:"reviveSync",value:function reviveSync(e,t){return this.revive(e,_objectSpread2$1(_objectSpread2$1({throwOnBadSyncType:!0},t),{},{sync:!0}))}},{key:"reviveAsync",value:function reviveAsync(e,t){return this.revive(e,_objectSpread2$1(_objectSpread2$1({throwOnBadSyncType:!0},t),{},{sync:!1}))}},{key:"register",value:function register(e,t){return t=t||{},[].concat(e).forEach((function R(e){var r=this;if(a(e))return e.map((function(e){return R.call(r,e)}));e&&i(e).forEach((function(r){if("#"===r)throw new TypeError("# cannot be used as a type name as it is reserved for cyclic objects");if(Typeson.JSON_TYPES.includes(r))throw new TypeError("Plain JSON object types are reserved as type names");var n=e[r],o=n&&n.testPlainObjects?this.plainObjectReplacers:this.nonplainObjectReplacers,i=o.filter((function(e){return e.type===r}));if(i.length&&(o.splice(o.indexOf(i[0]),1),delete this.revivers[r],delete this.types[r]),"function"==typeof n){var c=n;n={test:function test(e){return e&&e.constructor===c},replace:function replace(e){return _objectSpread2$1({},e)},revive:function revive(e){return Object.assign(Object.create(c.prototype),e)}}}else if(a(n)){var u=_slicedToArray(n,3);n={test:u[0],replace:u[1],revive:u[2]}}if(n&&n.test){var s={type:r,test:n.test.bind(n)};n.replace&&(s.replace=n.replace.bind(n)),n.replaceAsync&&(s.replaceAsync=n.replaceAsync.bind(n));var f="number"==typeof t.fallback?t.fallback:t.fallback?0:Number.POSITIVE_INFINITY;if(n.testPlainObjects?this.plainObjectReplacers.splice(f,0,s):this.nonplainObjectReplacers.splice(f,0,s),n.revive||n.reviveAsync){var l={};n.revive&&(l.revive=n.revive.bind(n)),n.reviveAsync&&(l.reviveAsync=n.reviveAsync.bind(n)),this.revivers[r]=[l,{plain:n.testPlainObjects}]}this.types[r]=n}}),this)}),this),this}}]),Typeson}(),f=function Undefined(){_classCallCheck$1(this,Undefined)};function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function _defineProperty(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _toConsumableArray(e){return function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}(e)||function _iterableToArray(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function _unsupportedIterableToArray(e,t){if(!e)return;if("string"==typeof e)return _arrayLikeToArray(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return _arrayLikeToArray(e,t)}(e)||function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}f.__typeson__type__="TypesonUndefined",s.Undefined=f,s.Promise=e,s.isThenable=isThenable,s.toStringTag=toStringTag,s.hasConstructorOf=hasConstructorOf,s.isObject=isObject,s.isPlainObject=isPlainObject,s.isUserObject=function isUserObject(e){if(!e||"Object"!==toStringTag(e))return!1;var t=n(e);return!t||(hasConstructorOf(e,Object)||isUserObject(t))},s.escapeKeyPathComponent=escapeKeyPathComponent,s.unescapeKeyPathComponent=unescapeKeyPathComponent,s.getByKeyPath=getByKeyPath,s.getJSONType=function getJSONType(e){return null===e?"null":Array.isArray(e)?"array":_typeof$1(e)},s.JSON_TYPES=["null","boolean","number","string","array","object"];for(var l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",p=new Uint8Array(256),y=0;y<l.length;y++)p[l.charCodeAt(y)]=y;var v=function encode(e,t,r){null==r&&(r=e.byteLength);for(var n=new Uint8Array(e,t||0,r),o=n.length,i="",a=0;a<o;a+=3)i+=l[n[a]>>2],i+=l[(3&n[a])<<4|n[a+1]>>4],i+=l[(15&n[a+1])<<2|n[a+2]>>6],i+=l[63&n[a+2]];return o%3==2?i=i.slice(0,-1)+"=":o%3==1&&(i=i.slice(0,-2)+"=="),i},b=function decode(e){var t,r,n,o,i=e.length,a=.75*e.length,c=0;"="===e[e.length-1]&&(a--,"="===e[e.length-2]&&a--);for(var u=new ArrayBuffer(a),s=new Uint8Array(u),f=0;f<i;f+=4)t=p[e.charCodeAt(f)],r=p[e.charCodeAt(f+1)],n=p[e.charCodeAt(f+2)],o=p[e.charCodeAt(f+3)],s[c++]=t<<2|r>>4,s[c++]=(15&r)<<4|n>>2,s[c++]=(3&n)<<6|63&o;return u},d={arraybuffer:{test:function test(e){return"ArrayBuffer"===s.toStringTag(e)},replace:function replace(e,t){t.buffers||(t.buffers=[]);var r=t.buffers.indexOf(e);return r>-1?{index:r}:(t.buffers.push(e),v(e))},revive:function revive(e,t){if(t.buffers||(t.buffers=[]),"object"===_typeof(e))return t.buffers[e.index];var r=b(e);return t.buffers.push(r),r}}},h={bigintObject:{test:function test(e){return"object"===_typeof(e)&&s.hasConstructorOf(e,BigInt)},replace:function replace(e){return String(e)},revive:function revive(e){return new Object(BigInt(e))}}},g={bigint:{test:function test(e){return"bigint"==typeof e},replace:function replace(e){return String(e)},revive:function revive(e){return BigInt(e)}}};function string2arraybuffer(e){for(var t=new Uint8Array(e.length),r=0;r<e.length;r++)t[r]=e.charCodeAt(r);return t.buffer}var m={blob:{test:function test(e){return"Blob"===s.toStringTag(e)},replace:function replace(e){var t=new XMLHttpRequest;if(t.overrideMimeType("text/plain; charset=x-user-defined"),t.open("GET",URL.createObjectURL(e),!1),t.send(),200!==t.status&&0!==t.status)throw new Error("Bad Blob access: "+t.status);return{type:e.type,stringContents:t.responseText}},revive:function revive(e){var t=e.type,r=e.stringContents;return new Blob([string2arraybuffer(r)],{type:t})},replaceAsync:function replaceAsync(e){return new s.Promise((function(t,r){var n=new FileReader;n.addEventListener("load",(function(){t({type:e.type,stringContents:n.result})})),n.addEventListener("error",(function(){r(n.error)})),n.readAsBinaryString(e)}))}}};function generateUUID(){var e=Date.now()+("undefined"!=typeof performance&&"function"==typeof performance.now?performance.now():0);return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var r=Math.trunc((e+16*Math.random())%16);return e=Math.floor(e/16),("x"===t?r:3&r|8).toString(16)}))}var O={},_={cloneable:{test:function test(e){return e&&"object"===_typeof(e)&&"function"==typeof e[Symbol.for("cloneEncapsulate")]},replace:function replace(e){var t=e[Symbol.for("cloneEncapsulate")](),r=generateUUID();return O[r]=e,{uuid:r,encapsulated:t}},revive:function revive(e){var t=e.uuid,r=e.encapsulated;return O[t][Symbol.for("cloneRevive")](r)}}},S={cryptokey:{test:function test(e){return"CryptoKey"===s.toStringTag(e)&&e.extractable},replaceAsync:function replaceAsync(e){return new s.Promise((function(t,r){crypto.subtle.exportKey("jwk",e).catch((function(e){r(e)})).then((function(r){t({jwk:r,algorithm:e.algorithm,usages:e.usages})}))}))},revive:function revive(e){var t=e.jwk,r=e.algorithm,n=e.usages;return crypto.subtle.importKey("jwk",t,r,!0,n)}}},j={dataview:{test:function test(e){return"DataView"===s.toStringTag(e)},replace:function replace(e,t){var r=e.buffer,n=e.byteOffset,o=e.byteLength;t.buffers||(t.buffers=[]);var i=t.buffers.indexOf(r);return i>-1?{index:i,byteOffset:n,byteLength:o}:(t.buffers.push(r),{encoded:v(r),byteOffset:n,byteLength:o})},revive:function revive(e,t){t.buffers||(t.buffers=[]);var r,n=e.byteOffset,o=e.byteLength,i=e.encoded,a=e.index;return"index"in e?r=t.buffers[a]:(r=b(i),t.buffers.push(r)),new DataView(r,n,o)}}},A={date:{test:function test(e){return"Date"===s.toStringTag(e)},replace:function replace(e){var t=e.getTime();return Number.isNaN(t)?"NaN":t},revive:function revive(e){return"NaN"===e?new Date(Number.NaN):new Date(e)}}},w={error:{test:function test(e){return"Error"===s.toStringTag(e)},replace:function replace(e){return{name:e.name,message:e.message}},revive:function revive(e){var t=e.name,r=e.message,n=new Error(r);return n.name=t,n}}},T="undefined"==typeof self?global:self,I={};["TypeError","RangeError","SyntaxError","ReferenceError","EvalError","URIError","InternalError"].forEach((function(e){var t=T[e];t&&(I[e.toLowerCase()]={test:function test(e){return s.hasConstructorOf(e,t)},replace:function replace(e){return e.message},revive:function revive(e){return new t(e)}})}));var P={file:{test:function test(e){return"File"===s.toStringTag(e)},replace:function replace(e){var t=new XMLHttpRequest;if(t.overrideMimeType("text/plain; charset=x-user-defined"),t.open("GET",URL.createObjectURL(e),!1),t.send(),200!==t.status&&0!==t.status)throw new Error("Bad File access: "+t.status);return{type:e.type,stringContents:t.responseText,name:e.name,lastModified:e.lastModified}},revive:function revive(e){var t=e.name,r=e.type,n=e.stringContents,o=e.lastModified;return new File([string2arraybuffer(n)],t,{type:r,lastModified:o})},replaceAsync:function replaceAsync(e){return new s.Promise((function(t,r){var n=new FileReader;n.addEventListener("load",(function(){t({type:e.type,stringContents:n.result,name:e.name,lastModified:e.lastModified})})),n.addEventListener("error",(function(){r(n.error)})),n.readAsBinaryString(e)}))}}},C={file:P.file,filelist:{test:function test(e){return"FileList"===s.toStringTag(e)},replace:function replace(e){for(var t=[],r=0;r<e.length;r++)t[r]=e.item(r);return t},revive:function revive(e){return new(function(){function FileList(){_classCallCheck(this,FileList),this._files=arguments[0],this.length=this._files.length}return function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),e}(FileList,[{key:"item",value:function item(e){return this._files[e]}},{key:Symbol.toStringTag,get:function get(){return"FileList"}}]),FileList}())(e)}}},N={imagebitmap:{test:function test(e){return"ImageBitmap"===s.toStringTag(e)||e&&e.dataset&&"ImageBitmap"===e.dataset.toStringTag},replace:function replace(e){var t=document.createElement("canvas");return t.getContext("2d").drawImage(e,0,0),t.toDataURL()},revive:function revive(e){var t=document.createElement("canvas"),r=t.getContext("2d"),n=document.createElement("img");return n.addEventListener("load",(function(){r.drawImage(n,0,0)})),n.src=e,t},reviveAsync:function reviveAsync(e){var t=document.createElement("canvas"),r=t.getContext("2d"),n=document.createElement("img");return n.addEventListener("load",(function(){r.drawImage(n,0,0)})),n.src=e,createImageBitmap(t)}}},$={imagedata:{test:function test(e){return"ImageData"===s.toStringTag(e)},replace:function replace(e){return{array:_toConsumableArray(e.data),width:e.width,height:e.height}},revive:function revive(e){return new ImageData(new Uint8ClampedArray(e.array),e.width,e.height)}}},E={infinity:{test:function test(e){return e===Number.POSITIVE_INFINITY},replace:function replace(e){return"Infinity"},revive:function revive(e){return Number.POSITIVE_INFINITY}}},x={IntlCollator:{test:function test(e){return s.hasConstructorOf(e,Intl.Collator)},replace:function replace(e){return e.resolvedOptions()},revive:function revive(e){return new Intl.Collator(e.locale,e)}},IntlDateTimeFormat:{test:function test(e){return s.hasConstructorOf(e,Intl.DateTimeFormat)},replace:function replace(e){return e.resolvedOptions()},revive:function revive(e){return new Intl.DateTimeFormat(e.locale,e)}},IntlNumberFormat:{test:function test(e){return s.hasConstructorOf(e,Intl.NumberFormat)},replace:function replace(e){return e.resolvedOptions()},revive:function revive(e){return new Intl.NumberFormat(e.locale,e)}}},k={map:{test:function test(e){return"Map"===s.toStringTag(e)},replace:function replace(e){return _toConsumableArray(e.entries())},revive:function revive(e){return new Map(e)}}},B={nan:{test:function test(e){return Number.isNaN(e)},replace:function replace(e){return"NaN"},revive:function revive(e){return Number.NaN}}},U={negativeInfinity:{test:function test(e){return e===Number.NEGATIVE_INFINITY},replace:function replace(e){return"-Infinity"},revive:function revive(e){return Number.NEGATIVE_INFINITY}}},K={nonbuiltinIgnore:{test:function test(e){return e&&"object"===_typeof(e)&&!Array.isArray(e)&&!["Object","Boolean","Number","String","Error","RegExp","Math","Date","Map","Set","JSON","ArrayBuffer","SharedArrayBuffer","DataView","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array","Promise","String Iterator","Array Iterator","Map Iterator","Set Iterator","WeakMap","WeakSet","Atomics","Module"].includes(s.toStringTag(e))},replace:function replace(e){}}},L={StringObject:{test:function test(e){return"String"===s.toStringTag(e)&&"object"===_typeof(e)},replace:function replace(e){return String(e)},revive:function revive(e){return new String(e)}},BooleanObject:{test:function test(e){return"Boolean"===s.toStringTag(e)&&"object"===_typeof(e)},replace:function replace(e){return Boolean(e)},revive:function revive(e){return new Boolean(e)}},NumberObject:{test:function test(e){return"Number"===s.toStringTag(e)&&"object"===_typeof(e)},replace:function replace(e){return Number(e)},revive:function revive(e){return new Number(e)}}},D={regexp:{test:function test(e){return"RegExp"===s.toStringTag(e)},replace:function replace(e){return{source:e.source,flags:(e.global?"g":"")+(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.sticky?"y":"")+(e.unicode?"u":"")}},revive:function revive(e){var t=e.source,r=e.flags;return new RegExp(t,r)}}},F={},M={resurrectable:{test:function test(e){return e&&!Array.isArray(e)&&["object","function","symbol"].includes(_typeof(e))},replace:function replace(e){var t=generateUUID();return F[t]=e,t},revive:function revive(e){return F[e]}}},J={set:{test:function test(e){return"Set"===s.toStringTag(e)},replace:function replace(e){return _toConsumableArray(e.values())},revive:function revive(e){return new Set(e)}}},V="undefined"==typeof self?global:self,Y={};["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array"].forEach((function(e){var t=e,r=V[e];r&&(Y[e.toLowerCase()]={test:function test(e){return s.toStringTag(e)===t},replace:function replace(e){return(0===e.byteOffset&&e.byteLength===e.buffer.byteLength?e:e.slice(0)).buffer},revive:function revive(e){return"ArrayBuffer"===s.toStringTag(e)?new r(e):e}})}));var W="undefined"==typeof self?global:self,q={};["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array"].forEach((function(e){var t=e,r=W[t];r&&(q[e.toLowerCase()]={test:function test(e){return s.toStringTag(e)===t},replace:function replace(e,t){var r=e.buffer,n=e.byteOffset,o=e.length;t.buffers||(t.buffers=[]);var i=t.buffers.indexOf(r);return i>-1?{index:i,byteOffset:n,length:o}:(t.buffers.push(r),{encoded:v(r),byteOffset:n,length:o})},revive:function revive(e,t){t.buffers||(t.buffers=[]);var n,o=e.byteOffset,i=e.length,a=e.encoded,c=e.index;return"index"in e?n=t.buffers[c]:(n=b(a),t.buffers.push(n)),new r(n,o,i)}})}));var G={undef:{test:function test(e,t){return void 0===e&&(t.ownKeys||!("ownKeys"in t))},replace:function replace(e){return 0},revive:function revive(e){return new s.Undefined}}},H={userObject:{test:function test(e,t){return s.isUserObject(e)},replace:function replace(e){return function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){_defineProperty(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},e)},revive:function revive(e){return e}}},X=[{arrayNonindexKeys:{testPlainObjects:!0,test:function test(e,t){return!!Array.isArray(e)&&(Object.keys(e).some((function(e){return String(Number.parseInt(e))!==e}))&&(t.iterateIn="object",t.addLength=!0),!0)},replace:function replace(e,t){return t.iterateUnsetNumeric=!0,e},revive:function revive(e){if(Array.isArray(e))return e;var t=[];return Object.keys(e).forEach((function(r){var n=e[r];t[r]=n})),t}}},{sparseUndefined:{test:function test(e,t){return void 0===e&&!1===t.ownKeys},replace:function replace(e){return 0},revive:function revive(e){}}}],z=[B,E,U],Q=[G,X,L,z,A,w,I,D].concat("function"==typeof Map?k:[],"function"==typeof Set?J:[],"function"==typeof ArrayBuffer?d:[],"function"==typeof Uint8Array?q:[],"function"==typeof DataView?j:[],"undefined"!=typeof Intl?x:[],"undefined"!=typeof BigInt?[g,h]:[]),Z=[w,I],ee=[Q,{arraybuffer:null},Y],te=[{sparseArrays:{testPlainObjects:!0,test:function test(e){return Array.isArray(e)},replace:function replace(e,t){return t.iterateUnsetNumeric=!0,e}}},{sparseUndefined:{test:function test(e,t){return void 0===e&&!1===t.ownKeys},replace:function replace(e){return 0},revive:function revive(e){}}}],re=[H,G,X,L,z,A,D,$,N,P,C,m].concat("function"==typeof Map?k:[],"function"==typeof Set?J:[],"function"==typeof ArrayBuffer?d:[],"function"==typeof Uint8Array?q:[],"function"==typeof DataView?j:[],"undefined"!=typeof Intl?x:[],"undefined"!=typeof crypto?S:[],"undefined"!=typeof BigInt?[g,h]:[]),ne=re.concat({checkDataCloneException:{test:function test(e){var t={}.toString.call(e).slice(8,-1);if(["symbol","function"].includes(_typeof(e))||["Arguments","Module","Error","Promise","WeakMap","WeakSet","Event","MessageChannel"].includes(t)||e&&"object"===_typeof(e)&&"number"==typeof e.nodeType&&"function"==typeof e.insertBefore)throw new DOMException("The object cannot be cloned.","DataCloneError");return!1}}}),oe=[te,G],ie=[Q];return s.types={arraybuffer:d,bigintObject:h,bigint:g,blob:m,cloneable:_,cryptokey:S,dataview:j,date:A,error:w,errors:I,file:P,filelist:C,imagebitmap:N,imagedata:$,infinity:E,intlTypes:x,map:k,nan:B,negativeInfinity:U,nonbuiltinIgnore:K,primitiveObjects:L,regexp:D,resurrectable:M,set:J,typedArraysSocketio:Y,typedArrays:q,undef:G,userObject:H},s.presets={arrayNonindexKeys:X,builtin:Q,postmessage:Z,socketio:ee,sparseUndefined:te,specialNumbers:z,structuredCloningThrowing:ne,structuredCloning:re,undef:oe,universal:ie},s}));
//# sourceMappingURL=all.js.map
