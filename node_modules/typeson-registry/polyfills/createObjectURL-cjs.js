!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).createObjectURL={})}(this,(function(e){"use strict";var t=require("whatwg-url"),n=require("jsdom/lib/jsdom/living/generated/utils"),r=t.serializeURLOrigin,o=t.parseURL,i={},a=n.implSymbol;e.createObjectURL=function createObjectURL(e){var t="blob:"+r(o(location.href))+"/"+function generateUUID(){var e=Date.now()+("undefined"!=typeof performance&&"function"==typeof performance.now?performance.now():0);return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var n=Math.trunc((e+16*Math.random())%16);return e=Math.floor(e/16),("x"===t?n:3&n|8).toString(16)}))}();return i[t]=e,t},e.revokeObjectURL=function revokeObjectURL(e){delete i[e]},e.xmlHttpRequestOverrideMimeType=function xmlHttpRequestOverrideMimeType(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.polyfillDataURLs,n=XMLHttpRequest.prototype.open,r=XMLHttpRequest.prototype.overrideMimeType;return function(e){"text/plain; charset=x-user-defined"===e&&(this.open=function(e,r,o){if(r.startsWith("blob:")){var x=i[r];if(!x)return void(this.send=function(){throw new DOMException("Failed to execute 'send' on 'XMLHttpRequest': Failed to "+"load '".concat(r,"'"),"NetworkError")});var s="text/plain",u=x[a]._buffer.toString("binary");if(t)return this.status=200,this.send=function(){},this.responseType=s,void(this.responseText=u);r="data:text/plain,"+encodeURIComponent(u)}return n.call(this,e,r,o)});for(var o=arguments.length,x=new Array(o>1?o-1:0),s=1;s<o;s++)x[s-1]=arguments[s];return r&&r.call.apply(r,[this,e].concat(x))}},Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=createObjectURL-cjs.js.map
