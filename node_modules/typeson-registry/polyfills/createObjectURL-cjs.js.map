{"version": 3, "file": "createObjectURL-cjs.js", "sources": ["createObjectURL.js", "../utils/generateUUID.js"], "sourcesContent": ["/* globals location, XMLHttpRequest, DOMException */\n\n// Imperfectly polyfill jsdom for testing `Blob`/`File`\n\n// Todo: `generateUUID` and `whatwgURL` can be removed once\n//    `URL.createObjectURL` may be implemented in jsdom:\n//    https://github.com/jsdom/jsdom/issues/1721\n//    though local-xmlhttprequest may need to be adapted\n// import whatwgURL from 'whatwg-url';\n\n// // eslint-disable-next-line node/no-unpublished-import\n// import utils from 'jsdom/lib/jsdom/living/generated/utils.js';\nimport generateUUID from '../utils/generateUUID.js';\n\n/* globals require */\n// These are not working well with Rollup as imports\n// We also need to tweak `XMLHttpRequest` which our types\n//    rely on to obtain the Blob/File content\n/* eslint-disable node/no-unpublished-require, import/no-commonjs */\nconst whatwgURL = require('whatwg-url');\nconst utils = require('jsdom/lib/jsdom/living/generated/utils');\n/* eslint-enable node/no-unpublished-require, import/no-commonjs */\n\nconst {serializeURLOrigin, parseURL} = whatwgURL;\n\n/*\nboth are problematic:\neslint-disable-next-line node/no-unpublished-import\neslint-disable node/file-extension-in-import, import/extensions\nimport {serializeURLOrigin, parseURL} from 'whatwg-url';\nimport utils from 'jsdom/lib/jsdom/living/generated/utils';\n*/\n\nconst blobURLs = {};\nconst createObjectURL = function (blob) {\n    // https://github.com/jsdom/jsdom/issues/1721#issuecomment-282465529\n    const blobURL = 'blob:' +\n        serializeURLOrigin(parseURL(location.href)) + '/' + generateUUID();\n    blobURLs[blobURL] = blob;\n    return blobURL;\n};\n\nconst revokeObjectURL = function (blobURL) {\n    delete blobURLs[blobURL];\n};\n\nconst impl = utils.implSymbol;\n\n// We only handle the case of binary, so no need to override `open`\n//   in all cases; but this only works if override is called first\nconst xmlHttpRequestOverrideMimeType = function ({polyfillDataURLs} = {}) {\n    // Set these references late in case global `XMLHttpRequest` has since\n    //  been changed/set\n    const _xhropen = XMLHttpRequest.prototype.open;\n    const _xhrOverrideMimeType = XMLHttpRequest.prototype.overrideMimeType;\n    return function (mimeType, ...args) {\n        if (mimeType === 'text/plain; charset=x-user-defined') {\n            this.open = function (method, url, async) {\n                if (url.startsWith('blob:')) {\n                    const blob = blobURLs[url];\n                    if (!blob) {\n                        this.send = function () {\n                            throw new DOMException(\n                                `Failed to execute 'send' on ` +\n                                    `'XMLHttpRequest': Failed to ` +\n                                    `load '${url}'`,\n                                'NetworkError'\n                            );\n                        };\n                        return undefined;\n                    }\n                    const responseType = 'text/plain'; // blob.type;\n                    // utf16le and base64 both convert lone surrogates\n                    const encoded = blob[impl]._buffer.toString('binary');\n                    // Not usable in jsdom which makes properties readonly,\n                    //   but local-xmlhttprequest can use (and jsdom can\n                    //   handle data URLs anyways)\n                    if (polyfillDataURLs) {\n                        this.status = 200;\n                        this.send = function () {\n                            // Empty\n                        };\n                        this.responseType = responseType;\n                        this.responseText = encoded;\n                        return undefined;\n                    }\n                    url = 'data:' + responseType + ',' +\n                        encodeURIComponent(encoded);\n                }\n                return _xhropen.call(this, method, url, async);\n            };\n        }\n        // The presence of `XMLHttpRequest.prototype.overrideMimeType`\n        //   is not really needed here, so making optional\n        return _xhrOverrideMimeType &&\n            _xhrOverrideMimeType.call(this, mimeType, ...args);\n    };\n};\n\nexport {createObjectURL, xmlHttpRequestOverrideMimeType, revokeObjectURL};\n", "/* globals performance */\n\n// The `performance` global is optional\n\n/**\n * @todo We could use `import generateUUID from 'uuid/v4';` (but it needs\n *   crypto library, etc.; `rollup-plugin-node-builtins` doesn't recommend\n *   using its own version and though there is <https://www.npmjs.com/package/crypto-browserify>,\n *   it may be troublesome to bundle and not strongly needed)\n * @returns {string}\n */\nexport default function generateUUID () { //  Adapted from original: public domain/MIT: http://stackoverflow.com/a/8809472/271577\n    /* istanbul ignore next */\n    let d = Date.now() +\n        // use high-precision timer if available\n        // istanbul ignore next\n        (typeof performance !== 'undefined' &&\n            typeof performance.now === 'function'\n            ? performance.now()\n            : 0);\n\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/gu, function (c) {\n        /* eslint-disable no-bitwise */\n        const r = Math.trunc((d + Math.random() * 16) % 16);\n        d = Math.floor(d / 16);\n        return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);\n        /* eslint-enable no-bitwise */\n    });\n}\n"], "names": ["whatwgURL", "require", "utils", "serializeURLOrigin", "parseURL", "blobURLs", "impl", "implSymbol", "createObjectURL", "blob", "blobURL", "location", "href", "generateUUID", "d", "Date", "now", "performance", "replace", "c", "r", "Math", "trunc", "random", "floor", "toString", "revokeObjectURL", "xmlHttpRequestOverrideMimeType", "polyfillDataURLs", "_xhropen", "XMLHttpRequest", "prototype", "open", "_xhrOverrideMimeType", "overrideMimeType", "mimeType", "method", "url", "async", "startsWith", "send", "DOMException", "responseType", "encoded", "_buffer", "status", "responseText", "encodeURIComponent", "call", "this", "args"], "mappings": "uPAmBA,IAAMA,EAAYC,QAAQ,cACpBC,EAAQD,QAAQ,0CAGfE,EAAgCH,EAAhCG,mBAAoBC,EAAYJ,EAAZI,SAUrBC,EAAW,GAaXC,EAAOJ,EAAMK,6BAZK,SAAlBC,gBAA4BC,OAExBC,EAAU,QACZP,EAAmBC,EAASO,SAASC,OAAS,IC1BvC,SAASC,mBAEhBC,EAAIC,KAAKC,OAGe,oBAAhBC,aACuB,mBAApBA,YAAYD,IACjBC,YAAYD,MACZ,SAEH,uCAAuCE,QAAQ,SAAU,SAAUC,OAEhEC,EAAIC,KAAKC,OAAOR,EAAoB,GAAhBO,KAAKE,UAAiB,WAChDT,EAAIO,KAAKG,MAAMV,EAAI,KACL,MAANK,EAAYC,EAAS,EAAJA,EAAU,GAAMK,SAAS,ODYEZ,UACxDR,EAASK,GAAWD,EACbC,qBAGa,SAAlBgB,gBAA4BhB,UACvBL,EAASK,qCAOmB,SAAjCiB,8FAAgE,GAApBC,IAAAA,iBAGxCC,EAAWC,eAAeC,UAAUC,KACpCC,EAAuBH,eAAeC,UAAUG,wBAC/C,SAAUC,GACI,uCAAbA,SACKH,KAAO,SAAUI,EAAQC,EAAKC,MAC3BD,EAAIE,WAAW,SAAU,KACnB9B,EAAOJ,EAASgC,OACjB5B,mBACI+B,KAAO,iBACF,IAAIC,aACN,2EAEaJ,OACb,sBAKNK,EAAe,aAEfC,EAAUlC,EAAKH,GAAMsC,QAAQnB,SAAS,aAIxCG,cACKiB,OAAS,SACTL,KAAO,kBAGPE,aAAeA,YACfI,aAAeH,GAGxBN,EAAM,mBACFU,mBAAmBJ,UAEpBd,EAASmB,KAAKC,KAAMb,EAAQC,EAAKC,gCAlCtBY,mCAAAA,2BAuCnBjB,GACHA,EAAqBe,WAArBf,GAA0BgB,KAAMd,UAAae"}