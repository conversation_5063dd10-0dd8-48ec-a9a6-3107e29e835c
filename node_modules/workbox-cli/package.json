{"name": "workbox-cli", "version": "7.3.0", "description": "workbox-cli is the command line interface for Workbox.", "keywords": ["workbox", "workboxjs", "service worker", "caching", "fetch requests", "offline", "cli"], "bin": {"workbox": "build/bin.js"}, "files": ["build"], "engines": {"node": ">=16.0.0"}, "author": "Google's Web DevRel Team and Google's Aurora Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/googlechrome/workbox.git"}, "bugs": "https://github.com/googlechrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox/tree/master/packages/workbox-cli", "dependencies": {"chalk": "^4.1.0", "chokidar": "^3.5.2", "common-tags": "^1.8.0", "fs-extra": "^9.0.1", "glob": "^7.1.6", "inquirer": "^7.3.3", "meow": "^7.1.0", "ora": "^5.0.0", "pretty-bytes": "^5.3.0", "stringify-object": "^3.3.0", "upath": "^1.2.0", "update-notifier": "^4.1.0", "workbox-build": "7.3.0"}, "workbox": {"packageType": "node_ts"}, "devDependencies": {"@types/common-tags": "^1.8.0", "@types/fs-extra": "^9.0.1", "@types/inquirer": "^9.0.3", "@types/stringify-object": "^3.3.0", "@types/update-notifier": "^4.1.1"}, "gitHead": "c77dceb54d4af1749db95316710d6430e82b0c48"}