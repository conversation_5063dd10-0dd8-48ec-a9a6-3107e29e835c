# base64-arraybuffer-es6

Encode/decode base64 data into ArrayBuffers

An ES6 Modules-based port of [base64-arraybuffer](https://github.com/niklasvh/base64-arraybuffer).

Also supports `byteOffset` and `length` arguments for partial encoding and
allows for selective import of the two methods.

## Getting Started

Install the module with: `npm install base64-arraybuffer-es6`

## API

The library encodes and decodes base64 to and from ArrayBuffers

 - __encode(buffer, byteOffset, length)__ - Encodes `ArrayBuffer` into base64 string
 - __decode(str)__ - Decodes base64 string to `<PERSON>rrayBuffer`

## License

Copyright (c) 2017-2019 <PERSON>, 2012 <PERSON><PERSON>
Licensed under the MIT license.
