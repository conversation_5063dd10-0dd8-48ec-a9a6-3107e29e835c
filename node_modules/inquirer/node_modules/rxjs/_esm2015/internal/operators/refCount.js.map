{"version": 3, "file": "refCount.js", "sources": ["../../../src/internal/operators/refCount.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AA2D3C,MAAM,UAAU,QAAQ;IACtB,OAAO,SAAS,wBAAwB,CAAC,MAAgC;QACvE,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;IACnD,CAAgC,CAAC;AACnC,CAAC;AAED,MAAM,gBAAgB;IACpB,YAAoB,WAAqC;QAArC,gBAAW,GAAX,WAAW,CAA0B;IACzD,CAAC;IACD,IAAI,CAAC,UAAyB,EAAE,MAAW;QAEzC,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;QACtB,WAAY,CAAC,SAAS,EAAE,CAAC;QAEhC,MAAM,UAAU,GAAG,IAAI,kBAAkB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QACnE,MAAM,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAElD,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YACf,UAAW,CAAC,UAAU,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;SACvD;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;CACF;AAED,MAAM,kBAAsB,SAAQ,UAAa;IAI/C,YAAY,WAA0B,EAClB,WAAqC;QACvD,KAAK,CAAC,WAAW,CAAC,CAAC;QADD,gBAAW,GAAX,WAAW,CAA0B;IAEzD,CAAC;IAES,YAAY;QAEpB,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,WAAW,EAAE;YAChB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,OAAO;SACR;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,MAAM,QAAQ,GAAU,WAAY,CAAC,SAAS,CAAC;QAC/C,IAAI,QAAQ,IAAI,CAAC,EAAE;YACjB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,OAAO;SACR;QAEM,WAAY,CAAC,SAAS,GAAG,QAAQ,GAAG,CAAC,CAAC;QAC7C,IAAI,QAAQ,GAAG,CAAC,EAAE;YAChB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,OAAO;SACR;QA0BD,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAC5B,MAAM,gBAAgB,GAAU,WAAY,CAAC,WAAW,CAAC;QACzD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,IAAI,gBAAgB,IAAI,CAAC,CAAC,UAAU,IAAI,gBAAgB,KAAK,UAAU,CAAC,EAAE;YACxE,gBAAgB,CAAC,WAAW,EAAE,CAAC;SAChC;IACH,CAAC;CACF"}