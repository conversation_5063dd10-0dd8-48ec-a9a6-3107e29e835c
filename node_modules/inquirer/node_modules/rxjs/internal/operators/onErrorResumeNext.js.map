{"version": 3, "file": "onErrorResumeNext.js", "sources": ["../../src/internal/operators/onErrorResumeNext.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,2CAA0C;AAI1C,2CAA0C;AAE1C,oDAAiG;AAmFjG,SAAgB,iBAAiB;IAAO,qBAC2C;SAD3C,UAC2C,EAD3C,qBAC2C,EAD3C,IAC2C;QAD3C,gCAC2C;;IACjF,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,iBAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;QACvD,WAAW,GAA2B,WAAW,CAAC,CAAC,CAAC,CAAC;KACtD;IAED,OAAO,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,yBAAyB,CAAO,WAAW,CAAC,CAAC,EAA7D,CAA6D,CAAC;AAClG,CAAC;AAPD,8CAOC;AAaD,SAAgB,uBAAuB;IAAO,qBAEb;SAFa,UAEb,EAFa,qBAEb,EAFa,IAEb;QAFa,gCAEb;;IAC/B,IAAI,MAAM,GAAmC,SAAS,CAAC;IAEvD,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,iBAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;QACvD,WAAW,GAAG,WAAW,CAAC,CAAC,CAA2B,CAAC;KACxD;IAED,MAAM,GAAG,WAAW,CAAC,KAAK,EAAG,CAAC;IAE9B,OAAO,WAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,yBAAyB,CAAO,WAAW,CAAC,CAAC,CAAC;AAC7E,CAAC;AAZD,0DAYC;AAED;IACE,mCAAoB,WAAwC;QAAxC,gBAAW,GAAX,WAAW,CAA6B;IAC5D,CAAC;IAED,wCAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,2BAA2B,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACzF,CAAC;IACH,gCAAC;AAAD,CAAC,AAPD,IAOC;AAED;IAAgD,+CAA2B;IACzE,qCAAsB,WAA0B,EAC5B,WAAwC;QAD5D,YAEE,kBAAM,WAAW,CAAC,SACnB;QAHqB,iBAAW,GAAX,WAAW,CAAe;QAC5B,iBAAW,GAAX,WAAW,CAA6B;;IAE5D,CAAC;IAED,iDAAW,GAAX;QACE,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAED,oDAAc,GAAd;QACE,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAES,4CAAM,GAAhB,UAAiB,GAAQ;QACvB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAES,+CAAS,GAAnB;QACE,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAEO,2DAAqB,GAA7B;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,CAAC,IAAI,EAAE;YACV,IAAM,eAAe,GAAG,IAAI,sCAAqB,CAAC,IAAI,CAAC,CAAC;YACxD,IAAM,WAAW,GAAG,IAAI,CAAC,WAA2B,CAAC;YACrD,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YACjC,IAAM,iBAAiB,GAAG,+BAAc,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;YAIhE,IAAI,iBAAiB,KAAK,eAAe,EAAE;gBACzC,WAAW,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;aACpC;SACF;aAAM;YACL,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;SAC7B;IACH,CAAC;IACH,kCAAC;AAAD,CAAC,AAzCD,CAAgD,sCAAqB,GAyCpE"}