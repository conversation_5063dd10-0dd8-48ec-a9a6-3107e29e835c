{"name": "typeson", "version": "6.1.0", "description": "Preserves types over JSON, BSON or socket.io", "main": "./dist/typeson-commonjs2.min.js", "browser": "./dist/typeson.js", "module": "./dist/typeson-esm.js", "scripts": {"prepublishOnly": "pnpm i", "eslint": "eslint --ext js,md,html .", "start": "http-server -p 8092", "rollup": "rollup -c", "open-test": "open-cli http://localhost:8092/test/ && npm start", "browser-test": "npm run rollup && npm run eslint && npm run open-test", "open-coverage": "open-cli http://localhost:8092/coverage/ && npm start", "mocha": "mocha --require esm --require chai/register-assert --require chai/register-expect test/test.js", "mocha-cov": "rm -Rf node_modules/.cache/esm && nyc --reporter=html --reporter=text npm run mocha", "test": "npm run rollup && npm run eslint && npm run mocha-cov"}, "nyc": {"exclude": ["test/**", "node_modules/**"]}, "repository": {"type": "git", "url": "git+https://github.com/dfahlander/typeson.git"}, "browserslist": ["cover 100%"], "keywords": ["JSON", "remoting", "serialization", "types"], "author": "<PERSON><PERSON><PERSON><PERSON>", "contributors": ["<PERSON>"], "license": "MIT", "bugs": {"url": "https://github.com/dfahlander/typeson/issues"}, "homepage": "https://github.com/dfahlander/typeson#readme", "engines": {"node": ">=0.1.14"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.13.15", "@babel/preset-env": "^7.13.15", "@brettz9/eslint-plugin": "^1.0.3", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-replace": "^2.4.2", "babel-plugin-transform-async-to-promises": "^0.8.15", "base64-arraybuffer-es6": "^0.7.0", "chai": "^4.3.4", "eslint": "^7.24.0", "eslint-config-ash-nazg": "29.10.0", "eslint-config-standard": "^16.0.2", "eslint-plugin-array-func": "^3.1.7", "eslint-plugin-chai-expect": "^2.2.0", "eslint-plugin-chai-friendly": "^0.6.0", "eslint-plugin-compat": "^3.9.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsdoc": "^32.3.0", "eslint-plugin-markdown": "^2.0.1", "eslint-plugin-no-unsanitized": "^3.1.4", "eslint-plugin-no-use-extend-native": "^0.5.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-radar": "^0.2.1", "eslint-plugin-standard": "^4.1.0", "eslint-plugin-unicorn": "^29.0.0", "esm": "^3.2.25", "http-server": "^0.12.3", "mocha": "^8.3.2", "nyc": "^15.1.0", "open-cli": "^6.0.1", "rollup": "2.45.1", "rollup-plugin-re": "^1.0.7", "rollup-plugin-terser": "^7.0.2"}, "tonicExample": "var Typeson = require('typeson');\nvar TSON = new Typeson().register(require('typeson-registry/presets/builtin'));\n\nTSON.stringify({foo: new Date()}, null, 2);"}