/* SizeWise Suite - Main Stylesheet */

/* CSS Variables for theming */
:root {
  --primary-color: #1976d2;
  --primary-dark: #1565c0;
  --primary-light: #42a5f5;
  --secondary-color: #424242;
  --accent-color: #ff9800;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --info-color: #2196f3;
  
  --background-color: #f5f5f5;
  --surface-color: #ffffff;
  --text-primary: #212121;
  --text-secondary: #757575;
  --text-disabled: #bdbdbd;
  
  --border-color: #e0e0e0;
  --border-radius: 4px;
  --box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  --box-shadow-elevated: 0 4px 8px rgba(0,0,0,0.15);
  
  --sidebar-width: 280px;
  --header-height: 64px;
  --status-bar-height: 32px;
  
  --font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', Robot<PERSON>, Oxygen, Ubuntu, Cantarell, sans-serif;
  --font-size-base: 14px;
  --font-size-small: 12px;
  --font-size-large: 16px;
  --font-size-xl: 20px;
  
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: var(--font-size-base);
  line-height: 1.5;
}

body {
  font-family: var(--font-family);
  color: var(--text-primary);
  background-color: var(--background-color);
  overflow-x: hidden;
}

/* Layout structure */
#app {
  display: grid;
  grid-template-areas: 
    "header header"
    "sidebar main"
    "sidebar status";
  grid-template-columns: var(--sidebar-width) 1fr;
  grid-template-rows: var(--header-height) 1fr var(--status-bar-height);
  height: 100vh;
}

/* Header */
#app-header {
  grid-area: header;
  background-color: var(--primary-color);
  color: white;
  box-shadow: var(--box-shadow);
  z-index: 1000;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 var(--spacing-md);
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.nav-brand h1 {
  font-size: var(--font-size-xl);
  font-weight: 600;
}

.version {
  font-size: var(--font-size-small);
  opacity: 0.8;
  background-color: rgba(255,255,255,0.2);
  padding: 2px 6px;
  border-radius: var(--border-radius);
}

.menu-toggle {
  display: none;
  background: none;
  border: none;
  color: white;
  font-size: var(--font-size-large);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
}

.menu-toggle:hover {
  background-color: rgba(255,255,255,0.1);
}

/* Sidebar */
#sidebar {
  grid-area: sidebar;
  background-color: var(--surface-color);
  border-right: 1px solid var(--border-color);
  overflow-y: auto;
  transition: transform 0.3s ease;
}

.sidebar-nav {
  padding: var(--spacing-md);
}

.nav-list {
  list-style: none;
}

.nav-list > li {
  margin-bottom: var(--spacing-sm);
}

.nav-section-title {
  font-size: var(--font-size-small);
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-xs);
  display: block;
}

.nav-subsection {
  list-style: none;
  margin-left: var(--spacing-md);
}

.nav-link {
  display: block;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  text-decoration: none;
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
}

.nav-link:hover {
  background-color: var(--primary-light);
  color: white;
}

.nav-link.active {
  background-color: var(--primary-color);
  color: white;
}

/* Main content */
#main-content {
  grid-area: main;
  overflow-y: auto;
  background-color: var(--background-color);
}

.content-area {
  padding: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.module-content {
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.module-content h2 {
  margin-bottom: var(--spacing-md);
  color: var(--primary-color);
}

/* Dashboard */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
}

.action-card {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-elevated);
}

.action-card h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.action-card p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

/* Forms */
.calculation-form {
  max-width: 600px;
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-primary);
}

.form-group input,
.form-group select {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.form-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 36px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #616161;
}

.btn-small {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-small);
  min-height: 28px;
}

/* Results */
.results-section {
  margin-top: var(--spacing-xl);
  padding: var(--spacing-lg);
  background-color: #f8f9fa;
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  background-color: white;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.result-item label {
  font-weight: 500;
  color: var(--text-secondary);
}

.result-item span {
  font-weight: 600;
  color: var(--text-primary);
}

.compliance-section {
  margin-top: var(--spacing-lg);
}

.compliance-status {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-weight: 500;
  margin-bottom: var(--spacing-sm);
}

.compliance-status.compliant {
  background-color: #e8f5e8;
  color: var(--success-color);
}

.compliance-status.non-compliant {
  background-color: #ffebee;
  color: var(--error-color);
}

.compliance-note {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.compliance-check {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid var(--border-color);
}

.check-parameter {
  font-weight: 500;
  min-width: 120px;
}

.check-status {
  font-weight: 600;
  min-width: 20px;
}

.check-status.passed {
  color: var(--success-color);
}

.check-status.failed {
  color: var(--error-color);
}

.check-message {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
  flex: 1;
}

.warnings-section {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: var(--border-radius);
}

.warnings-section h4 {
  color: var(--warning-color);
  margin-bottom: var(--spacing-sm);
}

.warning-note {
  font-size: var(--font-size-small);
  color: #856404;
  margin-bottom: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* Status bar */
#status-bar {
  grid-area: status;
  background-color: var(--surface-color);
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-md);
  font-size: var(--font-size-small);
}

.status-left,
.status-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.status-indicator::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-indicator.online::before {
  background-color: var(--success-color);
}

.status-indicator.offline::before {
  background-color: var(--error-color);
}

.status-text {
  color: var(--text-secondary);
}

/* Utility classes */
.hidden {
  display: none !important;
}

.text-center {
  text-align: center;
}

.text-muted {
  color: var(--text-secondary);
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

/* Error boundary */
#error-boundary {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.error-content {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-elevated);
  max-width: 400px;
}

.error-content h2 {
  color: var(--error-color);
  margin-bottom: var(--spacing-md);
}

.error-content p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

/* Responsive design */
@media (max-width: 768px) {
  #app {
    grid-template-areas: 
      "header"
      "main"
      "status";
    grid-template-columns: 1fr;
    grid-template-rows: var(--header-height) 1fr var(--status-bar-height);
  }
  
  .menu-toggle {
    display: block;
  }
  
  #sidebar {
    position: fixed;
    top: var(--header-height);
    left: 0;
    bottom: var(--status-bar-height);
    width: var(--sidebar-width);
    z-index: 999;
    transform: translateX(-100%);
  }
  
  #sidebar.open {
    transform: translateX(0);
  }
  
  .content-area {
    padding: var(--spacing-md);
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .results-grid {
    grid-template-columns: 1fr;
  }
  
  .status-left,
  .status-right {
    gap: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .nav-brand h1 {
    font-size: var(--font-size-large);
  }
  
  .content-area {
    padding: var(--spacing-sm);
  }
  
  .module-content {
    padding: var(--spacing-md);
  }
}
